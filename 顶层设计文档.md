# 配置定义编辑器顶层设计方案

## 一、系统架构设计
采用分层架构模式，分为核心层、界面层、数据持久层

## 二、代码结构设计
### 2.1 目录结构
```
src/
├── core/                   # 核心层
│   ├── model/             # 数据模型
│   │   ├── item.h         # UCI元素模型
│   │   ├── type.h         # 高级类型模型
│   │   └── device.h       # 设备模型
│   ├── parser/            # 解析器
│   │   ├── ucitype.h      # UCI类型文件解析
│   │   ├── json.h         # JSON文件解析
│   │   └── ini.h          # INI文件解析
│   └── validator/         # 验证器
│       └── type_check.h   # 类型检查
├── ui/                    # 界面层
│   ├── gtk/              # GTK界面实现
│   │   ├── main_window.h
│   │   ├── element_tab.h
│   │   ├── type_tab.h
│   │   └── device_tab.h
│   └── win32/            # Win32界面实现
│       ├── main_window.h
│       ├── element_tab.h
│       ├── type_tab.h
│       └── device_tab.h
├── persistence/          # 数据持久层
│   ├── file_manager.h    # 文件管理
│   └── config_manager.h  # 配置管理
└── utils/               # 工具类
    ├── version.h        # 版本处理
    ├── error.h         # 错误处理
    └── singleton.h     # 单例模式

```

### 2.2 核心类设计
```cpp
// 核心数据模型
class Item {
    string name;              // UCI元素名字
    string type;              // UCI元素类型
    string adv_type;          // 新增额外类型
    string file_path;         // 对应的实际文件
    vector<string> affect_devices;   // 受影响的设备家族
    vector<string> affect_versions;  // 受影响的版本范围
    bool is_list;             // 是否为列表类型
    int level;                // 层级
    vector<shared_ptr<Item>> children;
    weak_ptr<Item> parent;    // 指向父元素
    map<string, string> attributes;  // 属性键值对
};

// 高级类型模型
class AdvancedType {
    string name;              // 类型名称
    string base_type;         // 基础类型
    string parent_type;       // 父类型
    vector<string> enum_values;    // 枚举值列表
    string regex_pattern;     // 正则表达式
    int min_value;           // 最小值
    int max_value;           // 最大值
    map<string, string> extra_attrs;  // 额外属性
};

// 设备模型
class Device {
    string name;             // 设备名称
    vector<shared_ptr<Device>> children;  // 子设备
    weak_ptr<Device> parent;  // 父设备
};
```

## 三、界面交互设计
### 3.1 主界面布局
```ascii
+---------------------------------+
| 菜单栏 工具栏                    |
+---------------------------------+
| 元素/类型/设备                   |
+-----------------+---------------+
| 树状/列表导航        | 编辑区   |
+-----------------+                +
| 添加/删除控件        |          |
+-----------------+---------------+
```

### 3.2 关键交互流程
1. 文件加载流程：
   ```mermaid
   graph TD
   A[获取根目录] --> B[扫描.ucitype文件]
   B --> C[解析类型定义]
   C --> D[构建内存模型]
   D --> E[更新界面状态]
   ```

2. 元素编辑流程：
   ```mermaid
   graph TD
   A[选择元素] --> B[加载属性]
   B --> C[编辑属性]
   C --> D[实时保存]
   D --> E[更新树视图]
   ```

3. 类型编辑流程：
   ```mermaid
   graph TD
   A[选择类型] --> B[加载类型定义]
   B --> C[编辑类型属性]
   C --> D[检查继承关系]
   D --> E[保存类型定义]
   ```

4. 设备编辑流程：
   ```mermaid
   graph TD
   A[选择设备] --> B[加载设备信息]
   B --> C[编辑设备名称]
   C --> D[更新设备树]
   D --> E[保存设备定义]
   ```

### 3.3 Tab页面设计
1. 元素Tab
   - 左侧树状视图：显示UCI元素层级结构
   - 右侧编辑面板：
     * 基本属性：名称、类型、高级类型
     * 设备支持：设备家族选择器
     * 版本支持：版本范围输入
     * 其他属性：只读、导出、合并等
   - 底部按钮组：添加、删除、上移、下移、升级、降级

2. 类型Tab
   - 左侧列表：显示所有高级类型
   - 右侧编辑面板：
     * 类型名称
     * 父类型选择器
     * 类型约束：
       - 枚举值列表
       - 正则表达式
       - 数值范围
     * 额外属性表格
     * XSD Extras 编辑区域：用于编辑全局 XSD 内容
   - 底部按钮组：添加、删除类型

3. 设备Tab
   - 左侧树状视图：显示设备家族层级
   - 右侧编辑面板：
     * 设备名称
     * 设备描述
   - 底部按钮组：添加、删除、移动设备

## 四、文件存储方案
### 4.1 文件组织结构
```
bin_dir/
├── config.ini
├── types.json
├── device_family.json
└── xsd_editor
```
```
work_dir_/
├── types.json
└── device_family.json
```
```
root_dir/
└── modules/
    ├── network/network.ucitype
    └── system/system.ucitype
```

### 4.2 存储格式示例
1. config.ini
```ini
[paths]
root_dir=/path/to/root
work_dir=/path/to/work

[app]
single_instance=true
auto_save=false
```

2. types.json
```json
{
  "ipv4": {
    "base": "string",
    "regex": "^\\d{1,3}(\\.\\d{1,3}){3}$",
    "extra_attrs": {
      "netmask": "string",
      "gateway": "string"
    }
  },
  "port_number": {
    "base": "int",
    "min": 1,
    "max": 65535
  },
  "protocol": {
    "base": "string",
    "enum": ["tcp", "udp", "icmp"]
  },
  "@extras": [
    "<xs:attributeGroup name=\"config_attributes\">",
    "<xs:attribute name=\"merge\" type=\"ipf:string_no\"/>",
    "<xs:attribute name=\"export\" type=\"ipf:string_no\"/>",
    "</xs:attributeGroup>"
  ]
}
```
3. device_family.json
```json
{
  "mediatek": {
    "mt76xx": {
      "mt7621": {}
    }
  },
  "qca": {
    "ipq40xx": {},
    "ipq806x": {}
  },
  "ramips": {}
}
```

4. .ucitype文件
```uci
# 网络配置模块
@device_family(mt7621, ipq40xx) @min_version(19.07.0)
config interface 'lan'
    @type(ipv4) @readonly
    option ipaddr '***********'
    
    @type(port_number)
    option mtu '1500'
    
    @type(protocol) @export(false)
    list proto 'tcp'
    list proto 'udp'
```

### 4.3 XSD Extras 功能
`types.json` 文件中的 `@extras` 字段是一个高级功能，允许用户直接添加任意的 XSD 内容到生成的 XSD 文件中。这个字段是一个字符串数组，每个字符串代表 XSD 文件中的一行。这个功能主要用于：

1. 定义属性组（attributeGroup），可以在类型定义中引用
2. 添加其他 XSD 元素，如复杂类型、简单类型等
3. 添加注释或文档说明

在 UI 中，用户可以通过 "XSD Extras" 文本框直接编辑这些内容。

### 4.4 错误处理
1. 文件操作错误
   - 文件不存在
   - 权限不足
   - 格式错误

2. 类型验证错误
   - 类型不存在
   - 继承链断裂
   - 类型约束不匹配

3. 设备验证错误
   - 设备不存在
   - 重复设备名

4. 用户界面错误
   - 无效操作
   - 数据不一致
