#!/bin/bash

# 检查是否安装必要工具
check_dependencies() {
    local missing=()
    command -v cmake >/dev/null 2>&1 || missing+=("cmake")
    command -v dpkg-buildpackage >/dev/null 2>&1 || missing+=("build-essential")
    command -v convert >/dev/null 2>&1 || missing+=("imagemagick")
    
    if [ ${#missing[@]} -ne 0 ]; then
        echo "缺少必要依赖: ${missing[*]}"
        exit 1
    fi
}

# 构建deb包
build_deb() {
    echo "=== 构建deb包 ==="
    
    # 创建原始压缩包
    cd .. && tar -czf xsd-editor_1.0.0.orig.tar.gz \
        --exclude=.git \
        --exclude=debian \
        --exclude=build \
        --exclude=work \
        xsd_editor && cd xsd_editor
    
    # 构建deb包
    dpkg-buildpackage -us -uc
    
    echo "=== deb包构建完成 ==="
    echo "生成的deb包: ../xsd-editor_1.0.0-1_amd64.deb"
}

# 构建AppImage
build_appimage() {
    echo "=== 构建AppImage ==="
    
    # 清理旧的AppDir
    rm -rf AppDir
    
    # 创建AppDir结构
    mkdir -p AppDir/usr/{bin,lib,share} \
             AppDir/usr/share/icons/hicolor/256x256/apps \
             AppDir/usr/etc/xsd-editor
    
    # 清理并重新构建
    echo "=== 清理构建目录 ==="
    rm -rf build
    mkdir -p build || { echo "创建build目录失败"; exit 1; }
    
    echo "=== 配置CMake ==="
    cd build || exit 1
    cmake -DCMAKE_BUILD_TYPE=Release .. || { echo "CMake配置失败"; exit 1; }
    
    echo "=== 编译程序 ==="
    make -j$(nproc) || { echo "编译失败"; exit 1; }
    cd .. || exit 1
    
    # 复制程序文件和配置文件
    echo "=== 复制可执行文件 ==="
    [ -f build/bin/xsd_editor ] || { echo "错误: 可执行文件未生成"; exit 1; }
    cp build/bin/xsd_editor AppDir/usr/bin/ || exit 1
    
    echo "=== 复制配置文件 ==="
    [ -f config/config.ini ] || { echo "错误: 配置文件未找到"; exit 1; }
    cp config/config.ini AppDir/usr/etc/xsd-editor/default.ini || exit 1
    
    # 创建设置文件
    cat > AppDir/xsd-editor.desktop <<EOF
[Desktop Entry]
Type=Application
Name=XSD Editor
Comment=A graphical tool for editing XML Schema Definition files
Exec=xsd_editor
Icon=xsd-editor
Categories=Development;
Terminal=false
EOF

    # 创建AppRun脚本
    cat > AppDir/AppRun <<'EOF'
#!/bin/sh
HERE="$(dirname "$(readlink -f "${0}")")"
CONFIG_FILE="$(dirname "$APPIMAGE")/config.ini"

export PATH="${HERE}/usr/bin:${PATH}"
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"
exec "${HERE}/usr/bin/xsd_editor" --config "$CONFIG_FILE" "$@"
EOF
    chmod +x AppDir/AppRun

    # 创建简单图标
    convert -size 256x256 xc:white -gravity center -pointsize 24 \
            -fill black -draw "text 0,0 'XSD'" \
            AppDir/usr/share/icons/hicolor/256x256/apps/xsd-editor.png

    # 下载linuxdeploy工具
    if [ ! -f linuxdeploy-x86_64.AppImage ]; then
        wget https://github.com/linuxdeploy/linuxdeploy/releases/download/continuous/linuxdeploy-x86_64.AppImage
        chmod +x linuxdeploy-x86_64.AppImage
    fi

    # 构建AppImage
    if [ ! -f "./linuxdeploy-x86_64.AppImage" ]; then
        echo "Error: linuxdeploy-x86_64.AppImage not found"
        exit 1
    fi
    
    if [ ! -f "runtime-x86_64" ]; then
        echo "Error: runtime-x86_64 not found"
        exit 1
    fi

    chmod +x ./linuxdeploy-x86_64.AppImage
    
    ./linuxdeploy-x86_64.AppImage \
        --appdir AppDir \
        --output appimage \
        --desktop-file AppDir/xsd-editor.desktop \
        --icon-file AppDir/usr/share/icons/hicolor/256x256/apps/xsd-editor.png

    echo "=== AppImage构建完成 ==="
    echo "生成的AppImage: XSD_Editor-x86_64.AppImage"
}

# 清理临时文件
clean() {
    echo "=== 清理临时文件 ==="
    rm -rf ../xsd-editor_1.0.0.orig.tar.gz AppDir
    echo "清理完成"
}

# 主菜单
main() {
    check_dependencies
    
    case "$1" in
        deb)
            build_deb
            ;;
        appimage)
            build_appimage
            ;;
        all)
            build_deb
            build_appimage
            ;;
        clean)
            clean
            ;;
        *)
            echo "用法: $0 {deb|appimage|all|clean}"
            echo "  deb       - 构建deb包"
            echo "  appimage  - 构建AppImage"
            echo "  all       - 构建全部包"
            echo "  clean     - 清理临时文件"
            exit 1
            ;;
    esac
}

main "$@"