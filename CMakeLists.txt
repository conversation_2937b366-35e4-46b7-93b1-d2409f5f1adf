cmake_minimum_required(VERSION 3.15)
project(xsd_editor VERSION 1.0.0 LANGUAGES CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 检查操作系统
if(WIN32)
    set(USE_WIN32_UI ON)
    set(USE_GTK_UI OFF)
else()
    set(USE_WIN32_UI OFF)
    set(USE_GTK_UI ON)
endif()

# 查找依赖包
find_package(LibXml2 REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)
if(USE_GTK_UI)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GTKMM REQUIRED gtkmm-4.0)
endif()


# 源文件
set(CORE_SOURCES
    src/core/model/item.cpp
    src/core/model/type.cpp
    src/core/model/device.cpp
    src/core/parser/ucitype.cpp
    src/core/parser/json.cpp
    src/core/parser/ini.cpp
    src/core/validator/type_check.cpp
)

set(UTILS_SOURCES
    src/utils/version.cpp
)

set(PERSISTENCE_SOURCES
    src/persistence/file_manager.cpp
    src/persistence/config_manager.cpp
)

if(USE_GTK_UI)
    set(UI_SOURCES
        src/ui/gtk/main_window.cpp
        src/ui/gtk/element_tab.cpp
        src/ui/gtk/type_tab.cpp
        src/ui/gtk/device_tab.cpp
    )
else()
    set(UI_SOURCES
        src/ui/win32/main_window.cpp
        src/ui/win32/element_tab.cpp
        src/ui/win32/type_tab.cpp
        src/ui/win32/device_tab.cpp
    )
endif()

# 主可执行文件
add_executable(${PROJECT_NAME}
    src/main.cpp
    ${CORE_SOURCES}
    ${UTILS_SOURCES}
    ${PERSISTENCE_SOURCES}
    ${UI_SOURCES}
)

# 包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${LibXml2_INCLUDE_DIRS}
)

if(USE_GTK_UI)
    target_include_directories(${PROJECT_NAME} PRIVATE
        ${GTKMM_INCLUDE_DIRS}
    )
endif()

# 链接库
target_link_libraries(${PROJECT_NAME} PRIVATE
    ${LibXml2_LIBRARIES}
)

target_link_libraries(${PROJECT_NAME} PRIVATE nlohmann_json::nlohmann_json)

if(USE_GTK_UI)
    target_link_directories(${PROJECT_NAME} PRIVATE
        ${GTKMM_LIBRARY_DIRS}
    )
    foreach(LIB ${GTKMM_LIBRARIES})
        string(REGEX REPLACE "^-l" "" LIB ${LIB})
        target_link_libraries(${PROJECT_NAME} PRIVATE ${LIB})
    endforeach()
endif()

# 编译定义
if(USE_GTK_UI)
    target_compile_definitions(${PROJECT_NAME} PRIVATE USE_GTK_UI)
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE USE_WIN32_UI)
endif()

# 编译选项
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4     # 警告级别
        /WX     # 警告视为错误
        /MP     # 多处理器编译
        /utf-8  # UTF-8编码
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall
        -Wextra
        -Werror
        -pedantic
    )
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 复制配置文件
configure_file(
    ${CMAKE_SOURCE_DIR}/config/config.ini
    ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/config.ini
    COPYONLY
)

# vcpkg 集成
if(DEFINED ENV{VCPKG_ROOT} AND NOT DEFINED CMAKE_TOOLCHAIN_FILE)
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"
        CACHE STRING "")
    message(STATUS "Using vcpkg toolchain file: ${CMAKE_TOOLCHAIN_FILE}")
endif()

# 输出配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Using GTK UI: ${USE_GTK_UI}")
message(STATUS "Using Win32 UI: ${USE_WIN32_UI}")

# 在构建后创建符号链接到顶层目录
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_SOURCE_DIR}
    COMMAND ${CMAKE_COMMAND} -E create_symlink
        ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}$<$<PLATFORM_ID:Windows>:.exe>
        ${CMAKE_SOURCE_DIR}/${PROJECT_NAME}$<$<PLATFORM_ID:Windows>:.exe>
    COMMENT "Creating symlink to ${PROJECT_NAME} in top-level directory"
    VERBATIM
)

# 如果符号链接创建失败，回退到复制文件
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}$<$<PLATFORM_ID:Windows>:.exe>
        ${CMAKE_SOURCE_DIR}/${PROJECT_NAME}$<$<PLATFORM_ID:Windows>:.exe>
    COMMENT "Copying ${PROJECT_NAME} to top-level directory (fallback)"
    VERBATIM
)