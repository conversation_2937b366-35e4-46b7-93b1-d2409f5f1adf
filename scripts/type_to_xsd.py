import json
from xml.etree import ElementTree as ET
from xml.dom import minidom

def parse_json_to_xsd(json_data, target_namespace):
    # 创建XSD的根元素
    schema = ET.Element("xs:schema")
    schema.set("targetNamespace", target_namespace)
    schema.set("xmlns:xs", "http://www.w3.org/2001/XMLSchema")
    schema.set("xmlns:ipf", target_namespace)

    # 添加固定的any类型
    any_type = ET.SubElement(schema, "xs:complexType", name="any")
    sequence = ET.SubElement(any_type, "xs:sequence")
    ET.SubElement(sequence, "xs:any", processContents="skip", minOccurs="0", maxOccurs="unbounded")
    ET.SubElement(any_type, "xs:anyAttribute", processContents="skip")

    # 处理类型定义
    for type_name, type_def in json_data.items():
        if type_name == "@extras":
            continue  # @extras单独处理

        # 去掉前缀
        type_name = type_name.split(":")[-1]
        base_type = type_def.get("base", "").split(":")[-1]
        base_ns = type_def.get("base", "").split(":")[0]

        # 确定是simpleType还是complexType
        if "enum" in type_def or "regex" in type_def or "min" in type_def or "max" in type_def:
            # 生成restriction
            if base_ns =="xs":
                simple_type = ET.SubElement(schema, "xs:simpleType", name=type_name)
                restriction = ET.SubElement(simple_type, "xs:restriction", base=f"xs:{base_type}")
            else:
                complex_type = ET.SubElement(schema, "xs:complexType", name=type_name)
                simple_content = ET.SubElement(complex_type, "xs:simpleContent")
                restriction = ET.SubElement(simple_content, "xs:restriction", base=f"ipf:{base_type}")

            # 添加enum
            if "enum" in type_def:
                for value in type_def["enum"]:
                    ET.SubElement(restriction, "xs:enumeration", value=value)

            # 添加pattern
            if "regex" in type_def:
                ET.SubElement(restriction, "xs:pattern", value=type_def["regex"])

            # 添加min/max
            if "min" in type_def:
                ET.SubElement(restriction, "xs:minInclusive", value=str(type_def["min"]))
            if "max" in type_def:
                ET.SubElement(restriction, "xs:maxInclusive", value=str(type_def["max"]))

        else:
            # 生成extension
            if base_ns =="xs":
                complex_type = ET.SubElement(schema, "xs:complexType", name=type_name)
                simple_content = ET.SubElement(complex_type, "xs:simpleContent")
                extension = ET.SubElement(simple_content, "xs:extension", base=f"xs:{base_type}")
            else:
                complex_type = ET.SubElement(schema, "xs:complexType", name=type_name)
                simple_content = ET.SubElement(complex_type, "xs:simpleContent")
                extension = ET.SubElement(simple_content, "xs:extension", base=f"ipf:{base_type}")

            # 处理extra_attrs
            if "extra_attrs" in type_def:
                attrs_dict = {}
                for attr_key, attr_value in type_def["extra_attrs"].items():
                    if attr_key.startswith("@"):
                        ET.SubElement(extension, "xs:attributeGroup", ref=f"ipf:{attr_key.split(':')[-1]}")
                    else:
                        # 解析属性参数
                        parts = attr_key.split('.', 1)
                        attr_name = parts[0]
                        param = parts[1] if len(parts) > 1 else 'type'
                        
                        # 合并参数到字典
                        if attr_name not in attrs_dict:
                            attrs_dict[attr_name] = {"name": attr_name}
                        
                        # 特殊处理空值
                        if attr_value != "":
                            attrs_dict[attr_name][param] = attr_value

                # 生成属性元素
                for attr in attrs_dict.values():
                    # 清理无效参数
                    if "type" in attr and attr["type"] == "":
                        del attr["type"]
                    ET.SubElement(extension, "xs:attribute", **attr)
    # 处理@extras
    schema_str = ET.tostring(schema, encoding="unicode")
    if "@extras" in json_data:
        extras = "\n".join(json_data["@extras"])
        schema_str = schema_str.replace("</xs:schema>", f"{extras}\n</xs:schema>")
        # schema = ET.fromstring(schema_str)
        
    # 美化输出
    # rough_string = ET.tostring(schema, encoding="unicode")
    # parsed = minidom.parseString(rough_string)
    return schema_str# parsed.toprettyxml(indent="    ")

# 示例使用
json_data = {
    "ipf:CIDR": {
        "base": "ipf:string",
        "regex": "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)/(3[01]|[012]?\\d?)$"
    },
    "ipf:IPv4": {
        "base": "ipf:string",
        "regex": "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    },
    "ipf:bool": {
        "base": "ipf:string",
        "enum": ["0", "no", "off", "false", "disabled", "1", "yes", "on", "true", "enabled"]
    },
    "ipf:constant": {
        "base": "xs:string",
        "extra_attrs": {
            "@ipf:config_attributes": "",
            "@ipf:config_constant_attributes": ""
        }
    },
    "ipf:constant_bool": {
        "base": "ipf:bool",
        "extra_attrs": {
            "modify": "ipf:string_no",
            "modify.fixed": "no"
        }
    },
    "ipf:device_bool": {
        "base": "ipf:bool",
        "extra_attrs": {
            "modify": ""
        }
    },
    "ipf:integer": {
        "base": "xs:integer",
        "extra_attrs": {
            "@ipf:config_attributes": ""
        }
    },
    "ipf:remodel_name": {
        "base": "xs:integer",
        "max": 4,
        "min": 1
    },
    "ipf:remodel_ver": {
        "base": "xs:integer",
        "max": 3,
        "min": 1
    },
    "ipf:string": {
        "base": "xs:string",
        "extra_attrs": {
            "@ipf:config_attributes": ""
        }
    },
    "ipf:string_no": {
        "base": "xs:string",
        "enum": ["no"]
    },
    "@extras": [
        "<xs:attributeGroup name=\"config_attributes\">",
        "<xs:attribute name=\"merge\" type=\"ipf:string_no\"/>",
        "<xs:attribute name=\"export\" type=\"ipf:string_no\"/>",
        "<xs:attribute name=\"remodel_name\">",
        "    <xs:simpleType>",
        "        <xs:restriction base=\"xs:integer\">",
        "            <xs:minInclusive value=\"1\"/>",
        "            <xs:maxInclusive value=\"4\"/>",
        "        </xs:restriction>",
        "    </xs:simpleType>",
        "</xs:attribute>",
        "<xs:attribute name=\"remodel_ver\">",
        "    <xs:simpleType>",
        "        <xs:restriction base=\"xs:integer\">",
        "            <xs:minInclusive value=\"1\"/>",
        "            <xs:maxInclusive value=\"3\"/>",
        "        </xs:restriction>",
        "    </xs:simpleType>",
        "</xs:attribute>",
        "</xs:attributeGroup>",
        "<xs:attributeGroup name=\"config_constant_attributes\">",
        "    <xs:attribute name=\"modify\" fixed=\"no\" type=\"ipf:string_no\"/>",
        "</xs:attributeGroup>"
    ]
}

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("Usage: python script.py input.json")
        sys.exit(1)
    input_file = sys.argv[1]
    with open(input_file, 'r') as f:
        json_data = json.load(f)
        target_namespace = "http://tplinkwifi.net/"
        xsd_output = parse_json_to_xsd(json_data, target_namespace)
        print(xsd_output)
