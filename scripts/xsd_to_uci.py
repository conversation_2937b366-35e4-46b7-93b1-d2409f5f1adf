import os
import sys
import xml.etree.ElementTree as ET

namespaces = {'xs': 'http://www.w3.org/2001/XMLSchema'}

def parse_xsd(file_path):
    tree = ET.parse(file_path)
    root = tree.getroot()
    return root.findall('.//xs:complexType', namespaces)

def write_uci_config(complex_type, output_dir):
    type_name = complex_type.get('name')
    if not type_name:
        return
    output_path = os.path.join(output_dir, f'{type_name.removesuffix('_type')}.ucitype')
    with open(output_path, 'w') as f:
        lines = process_complex_type(complex_type)
        f.write('\n'.join(lines))

def process_complex_type(complex_type):
    lines = []
    for child in complex_type:
        if child.tag.endswith(('all', 'sequence', 'choice')):
            lines += process_structure(child, {}, 0)
    return lines

def process_structure(node, context, indent_level):
    handlers = {
        'all': process_all,
        'sequence': process_sequence,
        'choice': process_choice,
        'element': process_element,
    }
    tag = node.tag.split('}')[-1]
    if tag not in handlers:
        return []
    return handlers[tag](node, context, indent_level)

def process_all(node, context, indent_level):
    lines = []
    for child in node:
        lines += process_structure(child, context, indent_level)
    return lines

def process_sequence(node, context, indent_level):
    new_context = context.copy()
    new_context.update({
        'minOccurs': node.get('minOccurs', context.get('minOccurs', '1')),
        'maxOccurs': node.get('maxOccurs', context.get('maxOccurs', '1')),
    })
    lines = []
    for child in node:
        lines += process_structure(child, new_context, indent_level)
    return lines

def process_choice(node, context, indent_level):
    new_context = context.copy()
    new_context.update({
        'minOccurs': node.get('minOccurs', context.get('minOccurs', '1')),
        'maxOccurs': node.get('maxOccurs', context.get('maxOccurs', '1')),
    })
    lines = []
    for child in node:
        lines += process_structure(child, new_context, indent_level)
    return lines

def process_element(node, context, indent_level):
    name = node.get('name')
    type_attr = node.get('type')
    min_occurs = node.get('minOccurs', context.get('minOccurs', '1'))
    max_occurs = node.get('maxOccurs', context.get('maxOccurs', '1'))
    is_list = context.get('list', False)
    
    decorators = []
    if min_occurs != '1':
        decorators.append(f'@minOccurs({min_occurs})')
    if max_occurs != '1':
        decorators.append(f'@maxOccurs({max_occurs})')
    if type_attr:
        decorators.append(f'@type({type_attr})')
    
    complex_type = node.find('./xs:complexType', namespaces)
    if complex_type is not None:
        # 处理name属性特殊逻辑
        for attr in complex_type.findall('./xs:attribute', namespaces):
            if attr.get('name') == 'name':
                enum_values = []
                # 检查fixed属性
                if (fixed := attr.get('fixed')) is not None:
                    enum_values.append(fixed)
                # 检查枚举类型
                elif (simple_type := attr.find('./xs:simpleType', namespaces)) is not None:
                    if (restriction := simple_type.find('./xs:restriction', namespaces)) is not None:
                        enum_values = [e.get('value') for e in restriction.findall('./xs:enumeration', namespaces)]
                
                if enum_values:
                    decorators.append(f'@name(enum:{",".join(enum_values)})')
                else:
                    decorators.append('@name')
    
    lines = []
    indent = '    ' * indent_level
    
    if name == 'list':
        if complex_type is not None:
            for child in complex_type:
                new_context = context.copy()
                new_context.update({
                    'list': True
                })
                sub_lines = process_structure(child, new_context, indent_level + 1)
                for line in sub_lines:
                    if line.startswith('option ') or line.startswith('list '):
                        line_decorators = ' '.join(decorators)
                        if line_decorators:
                            lines.append(f'{indent}{line_decorators}')
                    lines.append(f'{indent}{line}')
    else:
        if decorators:
            lines.append(f'{indent}{" ".join(decorators)}')
        if indent_level == 0:
            lines.append(f'{indent}config {name}')
            if complex_type is not None:
                for child in complex_type:
                    lines += process_structure(child, context, indent_level + 1)
        elif is_list:
            lines.append(f'{indent}list {name}')
            if complex_type is not None:
                for child in complex_type:
                    lines += process_structure(child, context, indent_level + 1)
        else:
            lines.append(f'{indent}option {name}')
            if complex_type is not None:
                for child in complex_type:
                    lines += process_structure(child, context, indent_level + 1)
    return lines

def main(input_output_dir):
    for root_dir, _, files in os.walk(input_output_dir):
        for file in files:
            if file.endswith('.xsd'):
                file_path = os.path.join(root_dir, file)
                complex_types = parse_xsd(file_path)
                for ct in complex_types:
                    write_uci_config(ct, root_dir)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python3 xsd_to_uci.py <directory>")
        sys.exit(1)
    main(sys.argv[1])
