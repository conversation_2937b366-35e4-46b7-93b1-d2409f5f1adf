设计一个配置定义编辑器，即一个编辑配置格式的程序，需求如下：

1. 包含基于gtk的界面和基于win32组件的界面，通过CMake判断所在操作系统，编译所需的界面。
2. 使用CMake和C++进行开发，包管理使用vcpkg。
3. 界面包含顶部菜单栏和工具栏，主工作区为一个多Tab界面，有元素、类型、设备三个页面。
    (a) 每个页面的左侧为树状选择器或列表选择器，树状选择器下方有添加/删除/顺序/层级调整按钮组合，右侧为编辑面板，编辑过的值自动更新，不需要人工确认。
    (b) 元素Tab用于设置配置定义，左边树状选择器是元素树，根元素是module元素，子元素是config和option/list。右边包含多个控件，可以配置各种项目。注意module元素需要能配置保存路径。能够自由添加或删除元素。
    (c) 类型用于配置高级类型，需要支持正则/枚举/最大最小值的配置，需要有个下拉框来设置继承的类型。另外需要可以设置多个额外属性，额外属性包含名字和高级类型共两个字符串。
    (d) 设备需要支持设备家族和设备的配置，为一个简单的树结构。编辑面板只需要编辑名字即可。
4. 引入libxml2来支持以后的XSD导出功能，虽然暂时用不到。
5. 程序需要支持的基本操作包含：读取存储文件、写入存储文件、修改配置。
6. 存储文件分散在多个文件夹下，读取的时候应打开存储文件根目录，搜索里面所有的存储文件，其格式为`模块名.ucitype`
7. 高级类型的保存和读取需要另外存放文件在工作目录，名字为`types.json`。设备树也需要，名字为`device_family.json`。
8. 高级类型里面有一个写死的any类型，不包含任何属性/继承。
9. 程序可执行文件的同级目录下有一个简单的ini配置文件，其配置了存储文件的根目录的路径、以及工作目录的路径。
10. 需要提供详细设计文档和使用文档
11. 若出现错误，需要弹窗提醒用户，由于是内部工具，错误内容应尽量详细。
12. 因设计因素，禁止用户同时启动多个实例
13. 修改过的配置应由用户手动保存，如同文本编辑器一样。
14. 高级类型是单继承的，若一个类型的父类型被删除，仅需要在保存时提供警告。
15. 高级类型不对实际值做校验，但限制枚举/正则的根类型必须为string，最大最小值的根类型为int。

-----------------------

核心数据结构考虑以下设计

```
struct item{/*仅作示例，请根据存储格式来调整*/
    string name;/*UCI元素名字*/
    string type;/*UCI元素类型*/
    string adv_type;/*新增额外类型，用于增强安全性*/
    string file_path;/*对应的实际文件，仅模块item用到*/
    stringlist affect_devices;/*受影响的设备家族*/
    stringlist affect_versions;/*受影响的版本范围（语义化，e.g. >18.06.0, 19.07.*）*/
    bool is_list;/*是否为列表类型*/
    int level;/*层级，0为module，1为config，2为option/list*/
    list<shared_ptr<item>> children;
    weak_ptr<item> parent;/*指向父元素*/
};
```

-----------------------

基于UCI格式的设计思路，存储格式设计如下方案：

1. **基础结构保留**：
- 延续`config`/`option`/`list`基础结构
- 保留`#`注释符号
- 保持相似的缩进风格

2. **新增属性语法**：
```ucitype
# 类型定义符后接括号包裹的属性键值对
@key(value1, value2...)
```

3. **完整示例**：
```ucitype
# 设备级属性（继承给子元素）
@device_family(ar71xx, ramips) @version(>=18.06.0)
config system 'led'
    # 类型约束 + 读写控制
    @type(string) @readonly @export(false)
    option name 'status_led'
    
    # 多设备支持 + 可合并列表
    @device_family(ipq806x) @merge
    list gpio_pins '12'
    list gpio_pins '24'
    
# 版本条件分支配置
@version(>=19.07.0, <=21.02.0) @export
config wireless 'radio0'
    @type(enum:dfs,non-dfs) @readonly(true)
    option channel 'auto'
    
    # 带类型约束的可合并列表
    @type(macaddr) @merge @device_family(ath79)
    list maclist '00:11:22:33:44:55'

@name
config list
    @name @readonly(true)
    option channel 'auto'
```

4. **属性说明表**：

| 属性名称       | 适用对象        | 值类型                  | 说明                                                                 |
|----------------|----------------|-------------------------|---------------------------------------------------------------------|
| type           | option/list    | 类型描述符              | 高级数据类型约束（string/int/bool/ipaddr等），可扩展`enum:xx,yy`格式和`regex:"expr"`格式     |
| device_family  | 任意            | 字符串列表              | 生效的设备平台列表（e.g. ar71xx, ramips）                          |
| name  | 任意            | 字符串列表              | 名称，`enum:xx,yy`格式或为空                          |
| version        | 任意            | 版本范围表达式          | 语义化版本范围（e.g. >18.06.0, 19.07.*）                            |
| readonly       | 任意            | bool（可省略值）         | 是否只读（默认false）                                               |
| export         | 任意            | bool（可省略值）         | 是否允许导出（默认true）                                  |
| merge          | option/list     | [策略]（可省略值）       | 是否允许合并（默认true）                     |
| source          | 任意     | 来源字符串       | 使用到此配置的源代码位置及提取正则，可以多次指定                     |

5. **设计特点**：
- 显式继承：config段的属性自动继承给子option/list
- 条件覆盖：支持多条件逻辑（设备类型&&版本范围）
- 类型安全：通过类型描述符实现配置验证

6. **版本表达式语法**：
- 基础比较符：`>`, `<`, `>=`, `<=`, `=`
- 通配符：`19.07.*`（匹配所有修订版本）
- 区间：`18.06.2-20.04.5`
- 逻辑或：`18.06.0|19.07.0`

7. **进阶功能示例**：
```ucitype
# 带枚举类型的复杂约束
@type(enum:pppoe,dhcp,static) @device_family(ramips)
option proto 'pppoe'

# 多维条件配置
@device_family(ar71xx) @version(>=21.02.0) 
config qos 'rule1'
    @export(false)  # 隐藏配置项
    @type(iprange)
    option target '*************-*************'
```

8. **source语法**
```ucitype
# 多维条件配置
@source(src/main.c:rule1) @version(>=21.02.0) 
config qos 'rule1'
    @export(false)  # 隐藏配置项
    @type(iprange)
    option target '*************-*************'
```
