{"@extras": ["<xs:attributeGroup name=\"config_attributes\">", "<xs:attribute name=\"merge\" type=\"ipf:string_no\"/>", "<xs:attribute name=\"export\" type=\"ipf:string_no\"/>", "<xs:attribute name=\"remodel_name\">", "    <xs:simpleType>", "        <xs:restriction base=\"xs:integer\">", "            <xs:minInclusive value=\"1\"/>", "            <xs:maxInclusive value=\"4\"/>", "        </xs:restriction>", "    </xs:simpleType>", "</xs:attribute>", "<xs:attribute name=\"remodel_ver\">", "    <xs:simpleType>", "        <xs:restriction base=\"xs:integer\">", "            <xs:minInclusive value=\"1\"/>", "            <xs:maxInclusive value=\"3\"/>", "        </xs:restriction>", "    </xs:simpleType>", "</xs:attribute>", "</xs:attributeGroup>", "<xs:attributeGroup name=\"config_constant_attributes\">", "    <xs:attribute name=\"modify\" fixed=\"no\" type=\"ipf:string_no\"/>", "</xs:attributeGroup>"], "ipf:CIDR": {"base": "ipf:string", "regex": "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)/(3[01]|[012]?\\d?)$"}, "ipf:IPv4": {"base": "ipf:string", "regex": "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"}, "ipf:bool": {"base": "ipf:string", "enum": ["0", "no", "off", "false", "disabled", "1", "yes", "on", "true", "enabled"]}, "ipf:constant": {"base": "xs:string", "extra_attrs": {"@ipf:config_attributes": "", "@ipf:config_constant_attributes": ""}}, "ipf:constant_bool": {"base": "ipf:string", "enum": ["0", "no", "off", "false", "disabled", "1", "yes", "on", "true", "enabled"], "extra_attrs": {"modify": "ipf:string_no", "modify.fixed": "no"}}, "ipf:device_bool": {"base": "ipf:string", "enum": ["0", "no", "off", "false", "disabled", "1", "yes", "on", "true", "enabled"], "extra_attrs": {"modify": ""}}, "ipf:integer": {"base": "xs:integer", "extra_attrs": {"@ipf:config_attributes": ""}}, "ipf:remodel_name": {"base": "xs:integer", "max": 4, "min": 1}, "ipf:remodel_ver": {"base": "xs:integer", "max": 3, "min": 1}, "ipf:string": {"base": "xs:string", "extra_attrs": {"@ipf:config_attributes": ""}}, "ipf:string_no": {"base": "xs:string", "enum": ["no"]}}