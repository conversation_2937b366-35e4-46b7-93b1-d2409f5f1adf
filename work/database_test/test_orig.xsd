<?xml version="1.0" encoding="UTF-8"?>
<xs:schema 
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:ipf="http://tplinkwifi.net/">
    <xs:import namespace="http://tplinkwifi.net/" schemaLocation="../types.xsd"/>
    <xs:element name="access_control" type="access_control_type" />
    <xs:complexType name="access_control_type">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="global">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:choice minOccurs="0" maxOccurs="unbounded">
                                <xs:element name="access_mode" type="ipf:string"/>
                                <xs:element name="enable" type="ipf:bool"/>
                                <xs:element name="guest_enable" type="ipf:bool"/>
                                <xs:element name="guest_limit" type="ipf:string"/>
                                <xs:element name="new_device_notify" type="ipf:string"/>
                                <xs:element name="list">
                                    <xs:complexType>
                                        <xs:sequence>
                                            <xs:choice minOccurs="0" maxOccurs="unbounded">
                                                <xs:element name="fw_white_domain" type="ipf:string" />
                                                <xs:element name="fw_white_ip" type="ipf:string" />
                                            </xs:choice>
                                        </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                        <xs:attribute name="name" use="required">
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="settings" />
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:attribute>
                    </xs:complexType>
                </xs:element>
                <xs:element name="black_list">
                    <xs:complexType>
                        <xs:all>
                            <xs:element name="name" type="ipf:string" minOccurs="0"/>
                            <xs:element name="mac" type="ipf:string" minOccurs="0" />
                        </xs:all>
                    </xs:complexType>
                </xs:element>
                <xs:element name="white_list">
                    <xs:complexType>
                        <xs:all>
                            <xs:element name="name" type="ipf:string" minOccurs="0" />
                            <xs:element name="mac" type="ipf:string" minOccurs="0" />
                            <xs:element name="real_mac" type="ipf:string" minOccurs="0" />
                        </xs:all>
                        <xs:attribute name="name"/>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
</xs:schema>