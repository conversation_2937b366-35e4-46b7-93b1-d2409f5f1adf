@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:settings) @version(>=18.06.0) @device_family(ar71xx, ramips)
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option guest_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option new_device_notify
    @type(ipf:string)
    list fw_white_domain
    @type(ipf:string)
    list fw_white_ip
@.minOccurs(0) @.maxOccurs(unbounded) @name
config black_list
    @.minOccurs(0) @type(ipf:string)
    option name
    @.minOccurs(0) @type(ipf:string)
    option mac
@.minOccurs(0) @.maxOccurs(unbounded) @name
config white_list 'a1'
    @.minOccurs(0) @type(ipf:string)
    option name
    @.minOccurs(0) @type(ipf:string)
    option mac
    @.minOccurs(0) @type(ipf:string)
    option real_mac
@.minOccurs(0) @.maxOccurs(unbounded) @name
config white_list 'a2'
    @.minOccurs(0) @type(ipf:string)
    option name
    @.minOccurs(0) @type(ipf:string)
    option mac
    @.minOccurs(0) @type(ipf:string)
    option real_mac