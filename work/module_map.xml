<?xml version="1.0" encoding="UTF-8"?>
<map xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="module_map.xsd">
    <item name="access_control">access_control.xsd</item>
    <item name="accountmgnt">accountmgnt.xsd</item>
    <item name="administration">administration.xsd</item>
    <item name="amazon_ffs">amazon_ffs.xsd</item>
    <item name="antivirus_white_list">antivirus_white_list.xsd</item>
    <item name="app_dpi">app_dpi.xsd</item>
    <item name="appflow">appflow.xsd</item>
    <item name="auto_upgrade">auto_upgrade.xsd</item>
    <item name="autoreboot">autoreboot.xsd</item>
    <item name="avira">avira.xsd</item>
    <item name="basic_security">basic_security.xsd</item>
    <item name="bluetooth">bluetooth.xsd</item>
    <item name="client_mgmt">client_mgmt.xsd</item>
    <item name="client_speed_limit">client_speed_limit.xsd</item>
    <item name="cloud_config">cloud_config.xsd</item>
    <item name="cnss_diag">cnss_diag.xsd</item>
    <item name="cwmp">cwmp.xsd</item>
    <item name="ddns">ddns.xsd</item>
    <item name="dhcp">dhcp.xsd</item>
    <item name="dhcp_server">dhcp.xsd</item>
    <item name="dhcp6c">dhcp6c.xsd</item>
    <item name="dhcp6s">dhcp6s.xsd</item>
    <item name="diagnostic">diagnostic.xsd</item>
    <item name="dns_privacy">dns_privacy.xsd</item>
    <item name="dos_protection">dns_protection.xsd</item>
    <item name="dnsproxy">dnsproxy.xsd</item>
    <item name="domain_login">domain_login.xsd</item>
    <item name="dos_protection">dos_protection.xsd</item>
    <item name="test">test.xsd</item>
    <item name="dropbear">dropbear.xsd</item>
    <item name="dsliteV6plus">dsliteV6plus.xsd</item>
    <item name="ap_bind">easymesh_client.xsd</item>
    <item name="easymesh_client">easymesh_client.xsd</item>
    <item name="easymesh">easymesh.xsd</item>
    <item name="ecm">ecm.xsd</item>
    <item name="eco_mode">eco_mode.xsd</item>
    <item name="ezmesh">ezmesh.xsd</item>
    <item name="factory">factory.xsd</item>
    <item name="feedback">feedback.xsd</item>
    <item name="fing">fing.xsd</item>
    <item name="firewall">firewall.xsd</item>
    <item name="fstab">fstab.xsd</item>
    <item name="gpnc">gpnc.xsd</item>
    <item name="guestnetwork_bandwidth_ctrl">guestnetwork_bandwith_ctrl.xsd</item>
    <item name="guestnetwork_effectivetime_ctrl">guestnetwork_bandwith_ctrl.xsd</item>
    <item name="history_list">history_list.xsd</item>
    <item name="homecare">homecare.xsd</item>
    <item name="hyd">hyd.xsd</item>
    <item name="ifttt_trigger">ifttt_trigger.xsd</item>
    <item name="igmpproxy">igmpproxy.xsd</item>
    <item name="imb">imb.xsd</item>
    <item name="iot_security">iot_security.xsd</item>
    <item name="iptv">iptv.xsd</item>
    <item name="l2tpoveripsec">l2tpoveripsec.xsd</item>
    <item name="led_matrix">led_matrix.xsd</item>
    <item name="ledctrl">ledctrl.xsd</item>
    <item name="ledpm">ledpm.xsd</item>
    <item name="locale">locale.xsd</item>
    <item name="luci">luci.xsd</item>
    <item name="mcproxy">mcproxy.xsd</item>
    <item name="mcsd">mcsd.xsd</item>
    <item name="meshd">meshd.xsd</item>
    <item name="minidlna">minidlna.xsd</item>
    <item name="modem">modem.xsd</item>
    <item name="nat">nat.xsd</item>
    <item name="network">network.xsd</item>
    <item name="nrd">nrd.xsd</item>
    <item name="lbd">nrd.xsd</item>
    <item name="nrd_blacklist">nrd.xsd</item>
    <item name="nss">nss.xsd</item>
    <item name="offline_download">offline_download.xsd</item>
    <item name="onemesh">onemesh.xsd</item>
    <item name="onemesh_client">onemesh.xsd</item>
    <item name="openvpn">openvpn.xsd</item>
    <item name="parental_control_v2">parental_control_v2.xsd</item>
    <item name="parental_control">parental_control_v2.xsd</item>
    <item name="tm_parental_control">parental_control_v2.xsd</item>
    <item name="pc_insights">pc_insights.xsd</item>
    <item name="plc">plc.xsd</item>
    <item name="portspeed">portspeed.xsd</item>
    <item name="power_ctrl">power_ctrl.xsd</item>
    <item name="pptpd">pptpd.xsd</item>
    <item name="profile">profile.xsd</item>
    <item name="protocol">protocol.xsd</item>
    <item name="pwdog">pwdog.xsd</item>
    <item name="qca_nss_dp">qca_nss_dp.xsd</item>
    <item name="qos_v2">qos.xsd</item>
    <item name="qos">qos.xsd</item>
    <item name="quicksetup">quicksetup.xsd</item>
    <item name="radvd">radvd.xsd</item>
    <item name="repacd">repacd.xsd</item>
    <item name="ripd">ripd.xsd</item>
    <item name="safesearch">safesearch.xsd</item>
    <item name="samba">samba.xsd</item>
    <item name="security_history">security.xsd</item>
    <item name="security">security.xsd</item>
    <item name="sharecfg">sharecfg.xsd</item>
    <item name="shn_dev_stats">shn_dev_stats.xsd</item>
    <item name="smart_home">smart_home.xsd</item>
    <item name="smartip">smartip.xsd</item>
    <item name="snmp">snmp.xsd</item>
    <item name="speed_test">speed_test.xsd</item>
    <item name="spdt">speed_test.xsd</item>
    <item name="switch">switch.xsd</item>
    <item name="syslog">syslog.xsd</item>
    <item name="sysmode">sysmode.xsd</item>
    <item name="system">system.xsd</item>
    <item name="systime">systime.xsd</item>
    <item name="telemetry">telemetry.xsd</item>
    <item name="tfstats">tfstats.xsd</item>
    <item name="time_machine">time_machine.xsd</item>
    <item name="tm_qos">tm_qos.xsd</item>
    <item name="tp_security">tp_security.xsd</item>
    <item name="tpfile">tpfile.xsd</item>
    <item name="ubootenv">ubootenv.xsd</item>
    <item name="ucitrack">ucitrack.xsd</item>
    <item name="uhttpd">uhttpd.xsd</item>
    <item name="upnpd">upnpd.xsd</item>
    <item name="usbshare">usbshare.xsd</item>
    <item name="vendor">vendor.xsd</item>
    <item name="vpn">vpn.xsd</item>
    <item name="weather">weather.xsd</item>
    <item name="wifi_cfg">wifi_cfg.xsd</item>
    <item name="wifiaclCfg">wifi_cfg.xsd</item>
    <item name="wificommonCfg">wifi_cfg.xsd</item>
    <item name="mlo">wifi_cfg.xsd</item>
    <item name="wifi_button">wifi_cfg.xsd</item>
    <item name="wireguard">wireguard.xsd</item>
    <item name="wireless_schedule">wireless_schedule.xsd</item>
    <item name="wireless">wireless.xsd</item>
    <item name="wol">wol.xsd</item>
    <item name="wportal">wportal.xsd</item>
    <item name="wps">wps.xsd</item>
    <item name="wpsd_info">wps.xsd</item>
    <item name="yandex_dns">yandex_dns.xsd</item>

</map>