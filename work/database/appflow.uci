
@.minOccurs(0)
config replicate
    @.minOccurs(0) @type(ipf:string)
    option stage
@.minOccurs(0)
config stage 'tccontroller'
    @.minOccurs(0) @type(ipf:string)
    option ifup
    @.minOccurs(0) @type(ipf:bool)
    option enable_auto
    @.minOccurs(0) @type(ipf:bool)
    option enable_streamboost
    @.minOccurs(0) @type(ipf:string)
    option downlimit_auto
    @.minOccurs(0) @type(ipf:string)
    option uplimit_auto
    @.minOccurs(0) @type(ipf:string)
    option ifdown
    @.minOccurs(0) @type(ipf:string)
    option daemon
    @.minOccurs(0) @type(ipf:string)
    option downlimit
    @.minOccurs(0) @type(ipf:string)
    option uplimit
    @.minOccurs(0) @type(ipf:string)
    option listen_path
    @.minOccurs(0) @type(ipf:string)
    option auto_update
@.minOccurs(0) @name
config policy
    @.minOccurs(0) @type(ipf:bool)
    option enable
@.minOccurs(0) @name
config bigdata
    @.minOccurs(0) @type(ipf:bool)
    option enable
