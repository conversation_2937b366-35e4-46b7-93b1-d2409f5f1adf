
@.minOccurs(0) @.maxOccurs(unbounded)
config replicate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option include
@.minOccurs(0) @.maxOccurs(unbounded) @name
config interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option alises
@.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:any)
config firewall_state
@.minOccurs(0) @.maxOccurs(unbounded) @name
config defaults
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accept_redirects
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accept_source_route
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom_chains
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option drop_invalid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option synflood_burst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option synflood_protect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option synflood_rate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcp_ecn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcp_syncookies
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcp_westwood
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcp_window_scaling
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option syn_flood
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option output
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option disable_ipv6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option input
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option forward
@.minOccurs(0) @.maxOccurs(unbounded) @name
config zone
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option conntrack
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option custom_chains
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option family
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option log_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option masq_dest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option masq_src
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option network
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option output
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option input
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option forward
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant_bool)
    option masq
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant_bool)
    option mtu_fix
@.minOccurs(0) @.maxOccurs(unbounded) @name
config forwarding
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option family
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option _name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option dest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option src
@.minOccurs(0) @.maxOccurs(unbounded) @name
config rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option _name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dest_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extra
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option limit_burst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_dport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list icmp_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option dest_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option family
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option target
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option src
@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:include_usr,miniupnpd,qcanssconnmgr)
config include
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option modify
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option path
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option family
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option reload
@.minOccurs(0) @.maxOccurs(unbounded) @name
config redirect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option _name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dest_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dest_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extra
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option family
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reflection
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_dip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_dport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option src_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option target
