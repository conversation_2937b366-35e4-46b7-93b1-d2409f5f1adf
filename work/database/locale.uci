
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option default
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option locale
    @.minOccurs(0) @.maxOccurs(unbounded)
    option country
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option locale_mobile
@.minOccurs(0) @.maxOccurs(unbounded) @name
config langinfo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option value
