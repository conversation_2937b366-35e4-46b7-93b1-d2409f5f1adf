
@.minOccurs(0) @.maxOccurs(unbounded) @name
config interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip6addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wan_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connectable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keepup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option igmp_snooping
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option peerdns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option conn_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option broadcast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option autodial_flag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dnsv6_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipv6_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mru
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option city
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option country
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option isp_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manual_switch
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option leasetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prefix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prefixlen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option startip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option relydev
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option send_rs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option teql
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option parent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rely_interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rdnss_prefix_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option passtype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option des
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encryption
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option listen_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns_bk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option public_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allowed_ips
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preshared_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option persistent_keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nat
@.minOccurs(0) @.maxOccurs(unbounded)
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keepup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
@.minOccurs(0) @.maxOccurs(unbounded)
config switch_vlan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vlan
@.minOccurs(0) @.maxOccurs(unbounded)
config route
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option target
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
