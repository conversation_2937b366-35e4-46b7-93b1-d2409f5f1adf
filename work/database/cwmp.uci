
@.minOccurs(0) @name
config info
    @.minOccurs(0) @type(ipf:string)
    option ccs_enable
    @.minOccurs(0) @type(ipf:string)
    option ccs_tag
    @.minOccurs(0) @type(ipf:string)
    option ccs_vid
    @.minOccurs(0) @type(ipf:string)
    option ccs_vprio
    @.minOccurs(0) @type(ipf:string)
    option conn_password
    @.minOccurs(0) @type(ipf:string)
    option conn_reqpath
    @.minOccurs(0) @type(ipf:string)
    option conn_reqport
    @.minOccurs(0) @type(ipf:string)
    option conn_url
    @.minOccurs(0) @type(ipf:string)
    option conn_username
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option flag
    @.minOccurs(0) @type(ipf:string)
    option inform_enable
    @.minOccurs(0) @type(ipf:string)
    option inform_interval
    @.minOccurs(0) @type(ipf:string)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option password
    @.minOccurs(0) @type(ipf:string)
    option url
    @.minOccurs(0) @type(ipf:string)
    option username
    @.minOccurs(0) @type(ipf:string)
    option vlan_changed
@.minOccurs(0) @name
config tr111
    @.minOccurs(0) @type(ipf:string)
    option Enable
    @.minOccurs(0) @type(ipf:string)
    option MaxKeepPeriod
    @.minOccurs(0) @type(ipf:string)
    option MinKeepPeriod
    @.minOccurs(0) @type(ipf:string)
    option NATDetected
    @.minOccurs(0) @type(ipf:string)
    option STUNPassword
    @.minOccurs(0) @type(ipf:string)
    option STUNSAdd
    @.minOccurs(0) @type(ipf:string)
    option STUNSPort
    @.minOccurs(0) @type(ipf:string)
    option STUNUsername
    @.minOccurs(0) @type(ipf:string)
    option udpConReqAdd
@.minOccurs(0) @name
config xmpp_conn
    @.minOccurs(0) @type(ipf:string)
    option enable
@.minOccurs(0) @name
config xmpp_srv
    @.minOccurs(0) @type(ipf:string)
    option enable
