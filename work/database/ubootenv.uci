
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ubootenv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option envsize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option numsec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offset
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option secsize
