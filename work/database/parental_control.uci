
@.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:any)
config black_list
@.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:any)
config white_list
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns_filter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns_forward
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filter_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option w_httpr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option w_https
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option en_content
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
