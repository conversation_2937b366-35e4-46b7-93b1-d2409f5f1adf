
@.minOccurs(0) @name
config global
    @.minOccurs(0) @type(ipf:string)
    option state
    @.minOccurs(0) @type(ipf:string)
    option service
    @.minOccurs(0) @type(ipf:string)
    option aucsecure
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option libauc
    @.minOccurs(0) @type(ipf:string)
    option process
    @.minOccurs(0) @type(ipf:string)
    option safethings
    @.minOccurs(0) @type(ipf:string)
    option status
    @.minOccurs(0) @type(ipf:string)
    option web_protection_status
    @.minOccurs(0) @type(ipf:bool)
    option hardware
@.minOccurs(0) @name
config default
    @.minOccurs(0) @type(ipf:string)
    option aucsecure
    @.minOccurs(0) @type(ipf:string)
    option libauc
    @.minOccurs(0) @type(ipf:string)
    option safethings
    @.minOccurs(0) @type(ipf:string)
    option state
    @.minOccurs(0) @type(ipf:string)
    option status
