
@.minOccurs(0) @name
config global
    @.minOccurs(0) @type(ipf:bool)
    option enable
    @.minOccurs(0) @type(ipf:constant)
    option hw_thresh
    @.minOccurs(0) @type(ipf:string)
    option up_band
    @.minOccurs(0) @type(ipf:string)
    option down_band
    @.minOccurs(0) @type(ipf:string)
    option up_unit
    @.minOccurs(0) @type(ipf:string)
    option down_unit
    @.minOccurs(0) @type(ipf:string)
    option high
    @.minOccurs(0) @type(ipf:string)
    option low
    @.minOccurs(0) @type(ipf:string)
    option rUpband
    @.minOccurs(0) @type(ipf:string)
    option rDownband
    @.minOccurs(0) @type(ipf:string)
    option qos_mode
    @.minOccurs(0) @type(ipf:string)
    option bandwidth_mode
    @.minOccurs(0) @type(ipf:string)
    option conference
    @.minOccurs(0) @type(ipf:string)
    option file_transfer
    @.minOccurs(0) @type(ipf:string)
    option game
    @.minOccurs(0) @type(ipf:string)
    option social
    @.minOccurs(0) @type(ipf:string)
    option stream_media
    @.minOccurs(0) @type(ipf:string)
    option old_qos_mode
    @.minOccurs(0) @type(ipf:string)
    option percent
    @.minOccurs(0) @type(ipf:string)
    option hw_percent
