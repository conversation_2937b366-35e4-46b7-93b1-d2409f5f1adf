
@.minOccurs(0)
config dns_privacy 'global'
    @.minOccurs(0) @type(ipf:string)
    option dns_privacy
    @.minOccurs(0) @type(ipf:string)
    option dns_mode
@.minOccurs(0)
config doh 'https_dns_proxy'
    @.minOccurs(0) @type(ipf:string)
    option bootstrap_dns
    @.minOccurs(0) @type(ipf:string)
    option listen_addr
    @.minOccurs(0) @type(ipf:string)
    option listen_port1
    @.minOccurs(0) @type(ipf:string)
    option listen_port2
    @.minOccurs(0) @type(ipf:string)
    option listen_port3
    @.minOccurs(0) @type(ipf:string)
    option server1
    @.minOccurs(0) @type(ipf:string)
    option server2
    @.minOccurs(0) @type(ipf:string)
    option server3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list optional_server
@.minOccurs(0)
config dot 'stubby'
    @.minOccurs(0) @type(ipf:string)
    option server1
    @.minOccurs(0) @type(ipf:string)
    option server2
    @.minOccurs(0) @type(ipf:string)
    option server3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list optional_server
