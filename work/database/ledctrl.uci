
@.minOccurs(0) @.maxOccurs(unbounded) @name
config rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ledon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ledname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option duration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option delayon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option delayoff
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option delaylast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list subrule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option finalon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option style
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model
