
@.minOccurs(0) @.maxOccurs(unbounded)
config replicate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option button
@.minOccurs(0) @.maxOccurs(unbounded) @name
config system
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option log_size
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timezone
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option zonename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option hour24_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option timezone_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option time_format
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option quicksetup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sel_tz
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rsa2048_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option update_enc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option first_login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option homecare
@.minOccurs(0) @.maxOccurs(unbounded) @name
config timeserver
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list server
@.minOccurs(0) @.maxOccurs(unbounded) @name
config dst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dst_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_month
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_week
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_hour
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option end_month
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option end_week
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option end_day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option end_hour
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_year
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option end_year
@.minOccurs(0) @.maxOccurs(unbounded)
config button 'led_button'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option modify
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option action
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option button
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option handler
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option min
@.minOccurs(0) @.maxOccurs(unbounded)
config timeserver 'ntp'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list server
@.minOccurs(0) @.maxOccurs(unbounded)
config button_reuse 'button_reuse'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option type
@.minOccurs(0) @.maxOccurs(unbounded)
config usb 'usb'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option support
