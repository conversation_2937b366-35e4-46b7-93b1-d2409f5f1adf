
@.minOccurs(0) @.maxOccurs(unbounded) @name
config owner
    @.minOccurs(0) @type(ipf:string)
    option history
    @.minOccurs(0) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @type(ipf:string)
    option owner_name
    @.minOccurs(0) @type(ipf:string)
    option day_1
    @.minOccurs(0) @type(ipf:string)
    option day_2
    @.minOccurs(0) @type(ipf:string)
    option day_3
    @.minOccurs(0) @type(ipf:string)
    option day_4
    @.minOccurs(0) @type(ipf:string)
    option day_5
    @.minOccurs(0) @type(ipf:string)
    option day_6
    @.minOccurs(0) @type(ipf:string)
    option day_7
    @.minOccurs(0) @type(ipf:string)
    option day_8
    @.minOccurs(0) @type(ipf:string)
    option day_9
    @.minOccurs(0) @type(ipf:string)
    option day_10
    @.minOccurs(0) @type(ipf:string)
    option day_11
    @.minOccurs(0) @type(ipf:string)
    option day_12
    @.minOccurs(0) @type(ipf:string)
    option day_13
    @.minOccurs(0) @type(ipf:string)
    option day_14
    @.minOccurs(0) @type(ipf:string)
    option day_15
    @.minOccurs(0) @type(ipf:string)
    option day_16
    @.minOccurs(0) @type(ipf:string)
    option day_17
    @.minOccurs(0) @type(ipf:string)
    option day_18
    @.minOccurs(0) @type(ipf:string)
    option day_19
    @.minOccurs(0) @type(ipf:string)
    option day_20
    @.minOccurs(0) @type(ipf:string)
    option day_21
    @.minOccurs(0) @type(ipf:string)
    option day_22
    @.minOccurs(0) @type(ipf:string)
    option day_23
    @.minOccurs(0) @type(ipf:string)
    option day_24
    @.minOccurs(0) @type(ipf:string)
    option day_25
    @.minOccurs(0) @type(ipf:string)
    option day_26
    @.minOccurs(0) @type(ipf:string)
    option day_27
    @.minOccurs(0) @type(ipf:string)
    option day_28
    @.minOccurs(0) @type(ipf:string)
    option day_29
    @.minOccurs(0) @type(ipf:string)
    option day_30
    @.minOccurs(0) @type(ipf:string)
    option day_31
    @.minOccurs(0) @type(ipf:string)
    option day_32
