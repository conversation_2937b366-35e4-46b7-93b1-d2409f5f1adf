
@.minOccurs(0) @name
config base
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ad_guard
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option app_limit_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list fw_white_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list fw_white_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list fw_white_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list fw_white_protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list in_white_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list in_white_protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wireless_iface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wireless_iface_ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wireless_iface
@.minOccurs(0) @name
config dhcp
    @.minOccurs(0) @type(ipf:string)
    option force
    @.minOccurs(0) @type(ipf:string)
    option ignore
    @.minOccurs(0) @type(ipf:string)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option leasetime
    @.minOccurs(0) @type(ipf:string)
    option limit
    @.minOccurs(0) @type(ipf:string)
    option start
    @.maxOccurs(unbounded) @type(ipf:string)
    list dhcp_option
@.minOccurs(0) @name
config interface
    @.minOccurs(0) @type(ipf:string)
    option gateway
    @.minOccurs(0) @type(ipf:string)
    option igmp_snooping
    @.minOccurs(0) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @type(ipf:string)
    option lan_type
    @.minOccurs(0) @type(ipf:string)
    option netmask
    @.minOccurs(0) @type(ipf:string)
    option proto
    @.minOccurs(0) @type(ipf:string)
    option type
    @.minOccurs(0) @type(ipf:string)
    option ifname
@.minOccurs(0) @name
config wifi-iface
    @.minOccurs(0) @type(ipf:string)
    option access
    @.minOccurs(0) @type(ipf:string)
    option authentication_timeout
    @.minOccurs(0) @type(ipf:string)
    option authentication_type
    @.minOccurs(0) @type(ipf:string)
    option device
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option encryption
    @.minOccurs(0) @type(ipf:string)
    option ffs
    @.minOccurs(0) @type(ipf:string)
    option guest
    @.minOccurs(0) @type(ipf:string)
    option hidden
    @.minOccurs(0) @type(ipf:string)
    option isolate
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option onemesh_ie
    @.minOccurs(0) @type(ipf:string)
    option passwd_cycle
    @.minOccurs(0) @type(ipf:string)
    option portal_password
    @.minOccurs(0) @type(ipf:string)
    option psk_cipher
    @.minOccurs(0) @type(ipf:string)
    option psk_version
    @.minOccurs(0) @type(ipf:string)
    option redirect
    @.minOccurs(0) @type(ipf:string)
    option redirect_url
    @.minOccurs(0) @type(ipf:string)
    option ssid
    @.minOccurs(0) @type(ipf:string)
    option wds
    @.minOccurs(0) @type(ipf:string)
    option ifname
