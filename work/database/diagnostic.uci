
@.minOccurs(0) @name
config default
    @.minOccurs(0) @type(ipf:string)
    option count
    @.minOccurs(0) @type(ipf:string)
    option pktsize
    @.minOccurs(0) @type(ipf:constant)
    option timeout
    @.minOccurs(0) @type(ipf:constant)
    option ttl
    @.minOccurs(0) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @type(ipf:string)
    option host
@.minOccurs(0) @name
config basic
    @.minOccurs(0) @type(ipf:bool)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option mirror_port_wan
    @.minOccurs(0) @type(ipf:string)
    option mirror_port_lan1
    @.minOccurs(0) @type(ipf:string)
    option mirror_port_lan2
    @.minOccurs(0) @type(ipf:string)
    option mirror_port_lan3
    @.minOccurs(0) @type(ipf:string)
    option mirror_port_lan4
    @.minOccurs(0) @type(ipf:string)
    option monitor_port
