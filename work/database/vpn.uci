
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vpntype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipsec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipsecServerIP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option maxserver
@.minOccurs(0) @.maxOccurs(unbounded) @name
config user
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option expire_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option last_check_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option support_third_vpn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vendor
@.minOccurs(0) @.maxOccurs(unbounded) @name
config thirdvpn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vendor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option token
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option country
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option city
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server_name
@.minOccurs(0) @.maxOccurs(unbounded) @name
config server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_third
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option des
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nat
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option public_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns_bk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allowed_ips
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option persistent_keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preshared_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option file
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option caFile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manual
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option upload_speed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option download_speed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vendor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option country
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option city
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option token
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_ovpn_modified
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option listen_port
