
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ripd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option redistribute
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config rip-interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option send_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option receive_version
