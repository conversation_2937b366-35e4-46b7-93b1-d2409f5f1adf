
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option has_convert
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option calendar
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option active
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option next_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wifi_schedule_duration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option has_set_wifi_schedule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_byts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option active_start_time
@.minOccurs(0) @.maxOccurs(unbounded)
config no 'changed'
@.minOccurs(0) @.maxOccurs(unbounded) @name
config time_entry
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option repeat_days
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_start
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option band_list
