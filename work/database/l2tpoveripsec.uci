
@.minOccurs(0) @.maxOccurs(unbounded) @name
config service
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encryption
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option xl2tpd_enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipsec_saref
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option subnet
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option maxconn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option localip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option remoteip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option samba_access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netbios_pass
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
@.minOccurs(0) @.maxOccurs(unbounded) @name
config login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
