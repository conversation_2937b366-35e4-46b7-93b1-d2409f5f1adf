
@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:familytime,settings,patrol_setting)
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sec_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option scan_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option patrol_mark
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option intrusion_sec_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iot_sec_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option optimize_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sec_state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option web_sec_enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config owner
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filter_categories_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option profile_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_workdays
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option age
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_eve_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list website
    @.minOccurs(0) @.maxOccurs(unbounded) @type(xs:anyURI)
    list website_white
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option create_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option blocked
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_eve_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option available
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weekend_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workday_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weekend_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workday_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workday_bedtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workday_begin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workday_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weekend_bedtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weekend_begin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weekend_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeLimits_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeLimits_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeLimits_workdays
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeLimits_eve_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filter_level
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option appblock_app_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option appblock_category_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bonus_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cross_day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filterCategoriesList
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ign_online
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ign_website
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option safe_search
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeLimits_cus_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option today_bonus_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option today_reward_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option website_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option workdays
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option yesterday_bonus_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option yesterday_reward_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option youtube_restricted
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_cus_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_fri_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_fri_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_mon_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_mon_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_sat_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_sat_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_sun_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_sun_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_thu_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_thu_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_tue_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_tue_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_wed_beg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bedtime_wed_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fri_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fri_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fri_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mon_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mon_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mon_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_cus_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_eve_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_eve_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wek_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wek_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wek_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wrk_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wrk_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offTime_wrk_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sat_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sat_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sat_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sun_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sun_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sun_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option thu_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option thu_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option thu_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tue_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tue_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tue_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wed_afternoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wed_forenoon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wed_time
