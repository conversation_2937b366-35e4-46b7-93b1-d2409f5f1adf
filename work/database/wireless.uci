
@.minOccurs(0) @.maxOccurs(unbounded) @name
config smart-connect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option smart_enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wifi-device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option disabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option disabled_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option frag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_group_rekey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option isolate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option channel
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hwmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option shortgi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wmm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disablestats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant_bool)
    option mu_mimo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option htmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dbdc_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option qwrap_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option qwrap_dbdc_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option country
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option beacon_int
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option phy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dtim_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txpower
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option airtime_fairness
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option zerowait_dfs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_6g_only_psc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option acs_bkscanen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_chloadvar
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_ctrlflags
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_dbgtrace
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_lmtobss
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_rssivar
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_scanintvl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_txpwr_opt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list aggr_burst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option aldstats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option allowpromisc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AMPDU
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ampdudensity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AMPDUFrames
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AMPDULim
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AMPDURxBsize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option AMSDU
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ani_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_numitr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_numpkts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_pktlen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_retrain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_traffic_timer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_train
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_train_min_thres
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_train_thres
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_trainmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ant_traintype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option antenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ap_isolation_enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_sched_dur
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfgrouppolicy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfobssscale
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfobsssched
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfstrictsched
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option bcnburst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option beacon_offload_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bintval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option blk_report_fld
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option block_interbss
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option blockdfschan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option blockdfslist
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option burst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option burst_dur
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option channel_block_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_mcast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CSwOpts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option current_ant
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_coch_int
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_debug
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_errth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_phyerrth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dcs_usermaxc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option default_ant
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option delay_stavapup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option device_if
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option distance
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option diversity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option drop_sta_query
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dscp_ovride
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dscp_tid_map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option edge_channel_deprioritize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option emiwar80p80
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_macreq
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_ol_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option eppovrd_ch_144
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ext_nss
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fast_lane
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force_hostapd_attach
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option hmmc_dscp_ovride
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hmmc_dscp_tid_map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ht_capab
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hwmode_11n
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option igmp_dscp_ovride
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option igmp_dscp_tid_map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option interCACChan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option led_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main_ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mainvap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mbss_auto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mcast_echo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode0
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option multi_beacon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option no_vlan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nobckhlradio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option nosbeacon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option obss_long_slot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option obss_rssi_th
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option obss_rx_rssi_th
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option obss_rxrssi_th
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pas_scanen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pCACTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pre_configed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preCACEn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pref_uplink
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option primaryradio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option promisc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qca_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option qwrap_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reconfig_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option regdomain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reset_dscp_map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option retrain_drop
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option retrain_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rst_lro_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rst_sg_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rst_tso_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rxantenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rxchainmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option scan_over_cac_en
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_pmf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_sa_param
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_smart_antenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option set_fw_recovery
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_ch_144
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option softled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option staDFSEn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tid_override_queue_map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tpscale
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txantenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txbf_snd_int
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txchainmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TXPowLim2G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TXPowLim5G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option usb_interference_reduction
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option unicastflood_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wifi_debug_sh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_try_sta_always
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_skip_ap_if_sta_disconnected
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc_overwrite_ssid_band_suffix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_overwrite_ap_settings
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disabled_by
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disabled_sche
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disabled_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option discon_time
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wifi-iface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option disabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option onemesh_ie
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_cipher
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option hidden
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option network
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_select
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option iot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option extap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wds
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option athnewind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encryption
    @.minOccurs(0) @.maxOccurs(unbounded)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option disablecoext
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ppe_vp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mld
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nrshareflag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option owe
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option sae
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option psk_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_cipher
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option passwd_cycle
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option guest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option isolate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wds_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option atf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant_bool)
    option rrm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wnm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MapBSSType
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access_network_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acct_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acct_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acct_secret
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acparams
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_6g_only_psc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acs_freq_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acsmaxdwell
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acsmindwell
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option acsreport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option add_sha256
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option add_sha384
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option addba
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option addbaresp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option aifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ampdu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option amsdu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option anqp_3gpp_cell_net
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list anqp_elem
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list domain_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list hmmc_add
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list hmwds_add_addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list hs20_conn_capab
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list hs20_icon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list hs20_oper_friendly_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list nai_realm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list nawds_add_repeater
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list network_auth_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list osu_provider
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list owe_groups
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list roaming_consortium
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list sae_groups
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list sae_password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list set_max_rate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list venue_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list venue_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list vow_dbg_cfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ant_ps_on
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ap_macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ap2_macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ap2_r1_key_holder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option asra
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option assocwar160
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_min_buf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_shr_buf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_tput_at
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfmaxclient
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atfssidgroup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auth_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auth_secret
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auth_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option authentication_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option authentication_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ba_bufsize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option backhaul
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option beacon_prot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bintval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option blockdfschan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bpr_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bridge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bss_load_update_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option bss_transition
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ca_cert
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cactimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option caprssi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cca_thresh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cfreq2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_cnt_th
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_cntwn_dur
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_hop_en
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_long_dur
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_nhop_dur
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ch_noise_th
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option chanbw
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option channel_block_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option chbwmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option chwidth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_cert
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option clr_rawsim_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option commitatf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option conf_11acmcs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option content
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option countryie
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ctsprt_dtmbcn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cwmax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option cwmenable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cwmin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option db_rssi_thr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option db_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dbgLVL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dbgLVL_high
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option decap_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option delba
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option deny11ab
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dgaf_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_rapid_commit_proxy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dis_legacy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disable_dgaf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disable_pmksa_caching
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disable11nmcs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option do_tdls_dc_req
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option doth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option doth_chanswitch
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dp_tx_allow_per_pkt_vdev_id_check
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dpp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_connector
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_csign
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_curve
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_netaccesskey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dpp_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dtim_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option dynamicbeacon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option eap_reauth_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option eap_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option edge_channel_deprioritize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option en_6g_sec_comp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_lci
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_lcr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_rtt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_tkip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enablertscts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encap_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option erp_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option erp_send_reauth_start
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option esr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option exclude
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ext_ifu_acs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ext_nss_sup
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ext_registrar
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extender_device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extprotmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extprotspac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fils_cache_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fils_fd_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fils_hlp_wait_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fils_realm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option font_color
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option font_opacity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force_invalid_group
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force_tkip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ft_over_ds
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ft_psk_generate_local
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ft_r0_key_lifetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gas_comeback_delay
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gas_frag_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option group
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option group_mgmt_cipher
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_dcm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_dlofdma
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_extrange
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_ltf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_mcs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_mubfee
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_mubfer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_rxmcsmap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_subfee
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_subfer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_txmcsmap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_ulmumimo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option he_ulofdma
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hessid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hlos_tidoverride
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hp_stavap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option hs20
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_deauth_req_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_operating_class
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_release
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_t_c_filename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_t_c_server_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_t_c_timestamp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hs20_wan_metrics
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option htmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hwmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iapp_interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option identity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option identity_request_retry_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ieee80211ai
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ieee80211d
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ieee80211r
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ieee80211w
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ieee80211w_retry_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ignore11d
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option implicitbf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option inact
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option index
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr_type_availability
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_root
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keymgmt_mask_6g
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option kh_key_hex
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option l2tif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lastenable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ldpc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option log_80211
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macfilter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option maclist
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option map8021qvlan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_ppdu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option maxampdu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option maxsta
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mbo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mbo_cell_data_conn_pref
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mbss_tx_vdev
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mcast_rate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mcastenhance
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option me_adddeny
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option medropmcast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option meshdbg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option metimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option metimer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mfptest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mlo_host
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mobility_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mscs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option multi_cred
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option multi_ssid_1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nasid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nasid2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nawds_defcaps
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nawds_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nawds_override
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option neighbourfilter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ngvhtintop
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option noackpolicy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option noedgech
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option noIBSSCreate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nss
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option oce
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ocv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option offchan_tx_test
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option onemesh_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option operator_icon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_ssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owe_group
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owe_transition_bssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owe_transition_ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owe_transition_ssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option own_ie_override
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option own_ip_addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pbc_in_m1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option periodicScan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option phase1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option phy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pkex_code
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pkex_identifier
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pmk_r1_push
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option portal_password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option powersave
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ppdu_duration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option priv_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option priv_key_pwd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option protmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option proxyarp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ps_on_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ps_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pure11ac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pure11ax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pureg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option puren
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option qbssload
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qca_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qos_map_set
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option quiet
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option r0_key_lifetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option r1_key_holder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option r1_max_key_lifetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radius_das_client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radius_das_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radius_das_require_event_timestamp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radius_max_retry_wait
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radius_server_retries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rawdwepind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rawsim_debug
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rawsim_txagr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option re_scalingfactor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reassociation_deadline
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option redirect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option redirect_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rept_spl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option revsig160
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rmode_pktsim
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option root_distance
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rrmdbg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rrmslwin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rrmsstats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option rrmstats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rsim_de_frmcnt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rsim_en_frmcnt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rsn_preauth
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rsnxe_override_eapol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rxcorrection
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rx_stbc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_anti_clogging_threshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_commit_override
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_commit_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_confirm_immediate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_pk_omit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_pwe
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_reflection_attack
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_require_mfp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae_sync
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option scanband
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option scanchevent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option send_add_ies
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serial_number
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_bcn_rate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_monrxfilter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_tdls_rmac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set11NRates
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set11NRetries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setaddbaoper
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setibssdfsparam
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setibssrssiclass
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setibssrssihyst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setwmmparams
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sko_max_xretries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option stafwd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option startibssrssimon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option suite_b
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option support
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option sw_merge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_auto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_dtoken
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_margin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_off_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_path_sel
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_path_sel_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_qosnull
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_rssi_lb
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_rssi_offset
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_rssi_ub
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_set_rcpi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_set_rcpi_hi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_set_rcpi_lo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_set_rcpi_margin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_tdb_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_uapsd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdls_weak_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsaction
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsecchnoffst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsmacaddr1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsmacaddr2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsoffchan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsoffchnmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlsswitchtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tdlstimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option theme_color
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option theme_opacity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option title
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option transition_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tx_stbc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TxBFCTL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txcorrection
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txoplimit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txpower
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option txqueuelen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option uapsd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uesa
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ul_hyst
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vap_contryie
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vap_ind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option venue_group
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option venue_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vht_11ng
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vht_mcsmap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vhtintop
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vhtmaxampdu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vhtmcs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vhtmubfee
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vhtmubfer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vhtsounddim
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vhtstscap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vhtsubfee
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vhtsubfer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vlan_bridge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option vol_dbg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key_len_broadcast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key_len_unicast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_key4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_rekey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wmm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wnm_sleep_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wnm_sleep_mode_no_keys
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_disable_eapol_key_retries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_group_update_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_key_mgmt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_master_rekey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_pair_rekey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_pairwise_update_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_psk_file
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_strict_rekey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_independent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc_duration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_noclone
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc_skip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc_start_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_rf_bands
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_vap_tie_dbdc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_manufacturer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_manufacturer_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_device_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_device_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option shortgi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disabled_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option channel
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model_number
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wifi-mld
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mld_macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mld_ssid
@.minOccurs(0) @.maxOccurs(unbounded) @name
config product_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
@.minOccurs(0) @.maxOccurs(unbounded) @name
config mac-filter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option action
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wps-device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option os_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_manufacturer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option serial_number
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_device_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_number
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_device_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_manufacturer_url
@.minOccurs(0) @.maxOccurs(unbounded) @name
config qcawifi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant_bool)
    option atf_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option alwaysprimary
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ar900b_20_targ_clk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option ar900b_emu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_max_vdevs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_msdu_desc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf_peers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option b_twt_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option carrier_vow_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option carrier_vow_optimization
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cce_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option debugtxbf_ulofdma
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dump_at_kernel_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_eapol_minrate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_mesh_peer_cap_update
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_mesh_support
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_pktlog_support
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_rdk_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_smart_antenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_smart_antenna_da
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enableuartprint
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option frac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fw_dump_options
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fw_vow_stats_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option g_unicast_deauth_on_stop
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option global_wds
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hp_sta_periodic_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hp_sta_scan_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option intval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option load_rawsimulation_mod
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option log_80211
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_active_peers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_clients
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_descs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_peers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_vaps
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mbss_ie_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mem_debug_disabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nss_wifi_nxthop_cfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nss_wifi_olcfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ol_be_min_free
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ol_bk_min_free
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ol_vi_min_free
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ol_vo_min_free
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option otp_mod_param
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option peer_ext_stats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option poison_spectral_bufs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prealloc_disabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pref_uplink_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qca9888_20_targ_clk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_br_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_dbglvl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_dbglvl_high
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_eth_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_eth_sta_add_en
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_eth_sta_del_en
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_poll_timer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option qwrap_sta_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reconfig_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sa_validate_sw
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option samessid_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_eapol_minrate_ac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option son_event_bcast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option spectral_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option testmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tgt_sched_params
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option twt_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vow_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_extender_enhance
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_overwrite_ap_settings_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc_overwrite_ssid_suffix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_pbc_overwrite_sta_settings_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option wps_vap_tie_dbdc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option discon_time
@.minOccurs(0) @.maxOccurs(unbounded) @name
config twt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ofdma
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config smart_antenna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
@.minOccurs(0) @.maxOccurs(unbounded) @name
config smart-connect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option smart_enable
@.minOccurs(0) @.maxOccurs(unbounded)
config mac-filter 'filter'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option action
@.minOccurs(0) @.maxOccurs(unbounded) @name
config atf-config-ac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option airtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option command
@.minOccurs(0) @.maxOccurs(unbounded) @name
config atf-config-group
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option airtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option command
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option group
@.minOccurs(0) @.maxOccurs(unbounded) @name
config atf-config-ssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option airtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option command
@.minOccurs(0) @.maxOccurs(unbounded) @name
config atf-config-sta
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option airtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option command
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
@.minOccurs(0) @.maxOccurs(unbounded) @name
config atf-config-tput
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option command
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_airtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option throughput
@.minOccurs(0) @.maxOccurs(unbounded) @name
config icm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dbglvl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dbgmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable11axunii3pref
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enablechangradeusage
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rejpolicybitmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reptxpowerpolicy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option seldebug
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config lowi
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config onemesh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option group_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option product_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option version
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wpc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config hostapd_cfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_80211
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_8021x
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_driver
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_iapp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_level
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_mlme
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_radius
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option log_wpa
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wifi-mld
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mld_macaddr
@.minOccurs(0) @.maxOccurs(unbounded) @name
config osu_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list osu_friendly_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list osu_icon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list osu_service_desc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_method_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_nai
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_nai2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_provider
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option osu_server_uri
@.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:any)
config vlanid
