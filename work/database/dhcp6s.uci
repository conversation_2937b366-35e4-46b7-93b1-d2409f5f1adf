
@.minOccurs(0)
config replicate
    @.minOccurs(0) @type(ipf:string)
    option dhcp6s
@.minOccurs(0)
config dhcp6s 'basic'
    @.minOccurs(0) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @type(ipf:string)
    option proto
    @.minOccurs(0) @type(ipf:constant)
    option interface
    @.minOccurs(0) @type(ipf:constant)
    option ifname
    @.minOccurs(0)
    option primary_dns
    @.minOccurs(0)
    option secondary_dns
    @.minOccurs(0)
    option startip
    @.minOccurs(0)
    option endip
    @.minOccurs(0) @type(ipf:string)
    option leasetime
    @.minOccurs(0) @type(ipf:constant)
    option config_file
