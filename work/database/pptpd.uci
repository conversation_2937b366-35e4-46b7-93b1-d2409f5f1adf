
@.minOccurs(0) @.maxOccurs(unbounded) @name
config service
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option localip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option remoteip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option samba_access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netbios_pass
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option unencrypted_access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option connections
@.minOccurs(0) @.maxOccurs(unbounded) @name
config login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
