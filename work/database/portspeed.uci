
@.minOccurs(0) @.maxOccurs(unbounded) @name
config portspeed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option supported
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option current
@.minOccurs(0) @.maxOccurs(unbounded) @name
config profile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option supported
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option current
