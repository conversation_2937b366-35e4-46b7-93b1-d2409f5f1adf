
@.minOccurs(0) @.maxOccurs(unbounded)
config replicate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option core
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extern
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internal
@.minOccurs(0) @.maxOccurs(unbounded)
config core 'main'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option mediaurlbase
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option resourcebase
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lang
@.minOccurs(0) @.maxOccurs(unbounded)
config extern 'flash_keep'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option openvpn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option uci
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option dropbear
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option firewall
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option uploads
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option opkg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option passwd
@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:languages,sauth,ccache,themes)
config internal
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option en
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option maxclients
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option sessionpath
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option sessiontime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option OpenWrt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option Killer
