
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option used_mark
@.minOccurs(0) @.maxOccurs(unbounded) @name
config client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option up_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option old_mark
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option down_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option need_reload
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mark
@.minOccurs(0) @.maxOccurs(unbounded) @name
config client_speed_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option down_low
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option downlink
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option up_low
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uplink
