
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lan_label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lan_port_set
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lan_phy_port_set
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lan_port_devset
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lan_default_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_port_set
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_phy_port_set
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_port_devset
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_default_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_proc_idx
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_vlan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reset
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port_iptv_seq
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port_status_seq
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port_wanlan_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port_wanlan_rec_list
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option switch_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option portspeed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fc_tx
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fc_rx
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option addr
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch_vlan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vlan
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch_agg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_agg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lacpports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lacpmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option hashmode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lanport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_agg_ports
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch_addl_wan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(xs:int)
    option addl_wan_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option addl_wan_port
@.minOccurs(0) @.maxOccurs(unbounded) @name
config interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option switch_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fc_tx
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fc_rx
@.minOccurs(0) @.maxOccurs(unbounded) @name
config state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sw_vlan_list
