
@.minOccurs(0) @.maxOccurs(unbounded) @name
config upnpd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option secure_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option internal_iface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_upnp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option download
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option upnp_lease_file
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option upload
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option external_iface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option log_output
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enable_natpmp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option manufacturer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option manufacturer_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_description
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_number
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option model_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option friendly_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option presentation_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option notify_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option external_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option clean_ruleset_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option clean_ruleset_threshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option config_file
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serial_number
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option system_uptime
@.minOccurs(0) @.maxOccurs(unbounded) @name
config perm_rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option action
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option int_addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ext_ports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option comment
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option int_ports
