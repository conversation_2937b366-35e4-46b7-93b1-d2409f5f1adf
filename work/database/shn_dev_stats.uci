
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev_mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option last_used_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option total_minutes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option total_game_minutes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option current_game_minutes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option botm_minutes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option botm_ts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bolm_minutes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bolm_ts
