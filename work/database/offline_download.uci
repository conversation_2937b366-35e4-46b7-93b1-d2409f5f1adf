
@.minOccurs(0) @.maxOccurs(unbounded) @name
config offline_download
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option path
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_concurrent_downloads
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option limit_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serverip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serverport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcpport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option udpport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cryptkey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option userid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option schedule_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option seed_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_download
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_upload
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_active_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option schedule
@.minOccurs(0) @.maxOccurs(unbounded)
config aria2 'aria2'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option aria2c_priority
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_download_result
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_hash_check_seed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hash_check_only
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_concurrent_downloads
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option continue
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option file_allocation
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option bt_enable_lpd
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_dht
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option follow_torrent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option user
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dir
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option min_split_size
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option save_session_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rpc_listen_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disk_cache
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_overall_download_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_overall_upload_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connect_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_stop_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_tracker_connect_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_tracker_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_tracker_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option limit_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option seed_ratio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option seed_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_min_crypto_level
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_force_encryption
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable_peer_exchange
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_max_peers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bt_max_open_files
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option config_dir
