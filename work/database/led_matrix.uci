
@.minOccurs(0) @name
config default
    @.minOccurs(0) @type(ipf:string)
    option animation_mode_supported
    @.minOccurs(0) @type(ipf:string)
    option character_type_supported
    @.minOccurs(0) @type(ipf:string)
    option column_num
    @.minOccurs(0) @type(ipf:string)
    option max_bright_led
    @.maxOccurs(unbounded) @type(ipf:string)
    list mode_supported
    @.minOccurs(0) @type(ipf:string)
    option row_num
    @.minOccurs(0) @type(ipf:string)
    option slides_mode_supported_detail
@.minOccurs(0) @name
config led_matrix_settings
    @.minOccurs(0) @type(ipf:string)
    option brightness
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option endHour
    @.minOccurs(0) @type(ipf:string)
    option endMin
    @.minOccurs(0) @type(ipf:string)
    option nignt_enable
    @.minOccurs(0) @type(ipf:string)
    option startHour
    @.minOccurs(0) @type(ipf:string)
    option startMin
@.minOccurs(0) @name
config led_matrix_status
    @.minOccurs(0) @type(ipf:string)
    option animation_type
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option playback_speed
    @.minOccurs(0) @type(ipf:string)
    option scroll_direction
    @.maxOccurs(unbounded) @type(ipf:string)
    list slides_mode_detail
    @.maxOccurs(unbounded) @type(ipf:string)
    list slides_mode
    @.minOccurs(0) @type(ipf:string)
    option text
