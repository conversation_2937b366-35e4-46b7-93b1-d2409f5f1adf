
@.minOccurs(0) @.maxOccurs(unbounded) @name
config dropbear
    @.minOccurs(0) @type(ipf:constant)
    option RootPasswordAuth
    @.minOccurs(0) @type(ipf:constant)
    option Port
    @.minOccurs(0) @type(ipf:constant)
    option PasswordAuth
    @.minOccurs(0) @type(ipf:constant)
    option SysAccountLogin
    @.minOccurs(0) @type(ipf:constant)
    option DisableIpv6
    @.minOccurs(0) @type(ipf:string)
    option AlgoVersion
    @.minOccurs(0) @type(ipf:string)
    option BannerFile
    @.minOccurs(0) @type(ipf:string)
    option dsskeyfile
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option GatewayPorts
    @.minOccurs(0) @type(ipf:string)
    option Interface
    @.minOccurs(0) @type(ipf:string)
    option RootLogin
    @.minOccurs(0) @type(ipf:string)
    option rsakeyfile
    @.minOccurs(0) @type(ipf:string)
    option verbose
