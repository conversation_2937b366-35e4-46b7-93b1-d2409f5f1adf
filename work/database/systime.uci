
@.minOccurs(0) @name
config zoneinfo
    @.minOccurs(0) @type(ipf:string)
    option zoneId
    @.minOccurs(0) @type(ipf:string)
    option updated
    @.minOccurs(0) @type(ipf:string)
    option dst_rule
    @.minOccurs(0) @type(ipf:string)
    option dstrule_num
    @.minOccurs(0) @type(ipf:string)
    option zone_rule
    @.minOccurs(0) @type(ipf:string)
    option dst_save
    @.minOccurs(0) @type(ipf:string)
    option tz_region
    @.minOccurs(0) @type(ipf:string)
    option update_time
@.minOccurs(0) @name
config state
    @.minOccurs(0) @type(ipf:string)
    option sync
