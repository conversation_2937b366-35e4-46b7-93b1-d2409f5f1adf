
@.minOccurs(0) @name
config openvpn
    @.minOccurs(0) @type(ipf:string)
    option access
    @.minOccurs(0) @type(ipf:string)
    option ca
    @.minOccurs(0) @type(ipf:string)
    option cert
    @.minOccurs(0) @type(ipf:constant)
    option cipher
    @.minOccurs(0) @type(ipf:constant)
    option comp_lzo
    @.minOccurs(0) @type(ipf:constant)
    option dev
    @.minOccurs(0) @type(ipf:string)
    option dh
    @.minOccurs(0) @type(ipf:constant)
    option duplicate_cn
    @.minOccurs(0) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_pool_persist
    @.minOccurs(0) @type(ipf:constant)
    option keepalive
    @.minOccurs(0) @type(ipf:string)
    option key
    @.minOccurs(0) @type(ipf:constant)
    option management
    @.minOccurs(0) @type(ipf:constant)
    option max_clients
    @.minOccurs(0) @type(ipf:constant)
    option persist_key
    @.minOccurs(0) @type(ipf:constant)
    option persist_tun
    @.minOccurs(0) @type(ipf:constant)
    option client_to_client
    @.minOccurs(0) @type(ipf:string)
    option port
    @.minOccurs(0) @type(ipf:string)
    option proto
    @.minOccurs(0) @type(ipf:string)
    option server
    @.minOccurs(0) @type(ipf:string)
    option status
    @.minOccurs(0) @type(ipf:constant)
    option status_version
    @.minOccurs(0) @type(ipf:constant)
    option verb
    @.minOccurs(0) @type(ipf:string)
    option askpass
    @.minOccurs(0) @type(ipf:string)
    option auth
    @.minOccurs(0) @type(ipf:string)
    option auth_nocache
    @.minOccurs(0) @type(ipf:string)
    option auth_retry
    @.minOccurs(0) @type(ipf:string)
    option auth_user_pass
    @.minOccurs(0) @type(ipf:string)
    option auth_user_pass_optional
    @.minOccurs(0) @type(ipf:string)
    option auth_user_pass_verify
    @.minOccurs(0) @type(ipf:string)
    option bcast_buffers
    @.minOccurs(0) @type(ipf:string)
    option bind
    @.minOccurs(0) @type(ipf:string)
    option ccd_exclusive
    @.minOccurs(0) @type(ipf:string)
    option cd
    @.minOccurs(0) @type(ipf:string)
    option chroot
    @.minOccurs(0) @type(ipf:string)
    option client
    @.minOccurs(0) @type(ipf:string)
    option client_cert_not_required
    @.minOccurs(0) @type(ipf:string)
    option client_config_dir
    @.minOccurs(0) @type(ipf:string)
    option client_connect
    @.minOccurs(0) @type(ipf:string)
    option client_disconnect
    @.minOccurs(0) @type(ipf:string)
    option comp_noadapt
    @.minOccurs(0) @type(ipf:string)
    option connect_freq
    @.minOccurs(0) @type(ipf:string)
    option connect_retry
    @.minOccurs(0) @type(ipf:string)
    option connect_retry_max
    @.minOccurs(0) @type(ipf:string)
    option connect_timeout
    @.minOccurs(0) @type(ipf:string)
    option crl_verify
    @.minOccurs(0) @type(ipf:string)
    option dev_node
    @.minOccurs(0) @type(ipf:string)
    option dev_type
    @.minOccurs(0) @type(ipf:string)
    option disable
    @.minOccurs(0) @type(ipf:string)
    option disable_occ
    @.minOccurs(0) @type(ipf:string)
    option down
    @.minOccurs(0) @type(ipf:string)
    option down_pre
    @.minOccurs(0) @type(ipf:string)
    option echo
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option engine
    @.minOccurs(0) @type(ipf:string)
    option explicit_exit_notify
    @.minOccurs(0) @type(ipf:string)
    option fast_io
    @.minOccurs(0) @type(ipf:string)
    option float
    @.minOccurs(0) @type(ipf:string)
    option fragment
    @.minOccurs(0) @type(ipf:string)
    option group
    @.minOccurs(0) @type(ipf:string)
    option hand_window
    @.minOccurs(0) @type(ipf:string)
    option hash_size
    @.minOccurs(0) @type(ipf:string)
    option http_proxy
    @.minOccurs(0) @type(ipf:string)
    option http_proxy_option
    @.minOccurs(0) @type(ipf:string)
    option http_proxy_retry
    @.minOccurs(0) @type(ipf:string)
    option http_proxy_timeout
    @.minOccurs(0) @type(ipf:string)
    option ifconfig
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_noexec
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_nowarn
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_pool
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_pool_linear
    @.minOccurs(0) @type(ipf:string)
    option ifconfig_push
    @.minOccurs(0) @type(ipf:string)
    option inactive
    @.minOccurs(0) @type(ipf:string)
    option ipchange
    @.minOccurs(0) @type(ipf:string)
    option iroute
    @.minOccurs(0) @type(ipf:string)
    option key_method
    @.minOccurs(0) @type(ipf:string)
    option keysize
    @.minOccurs(0) @type(ipf:string)
    option learn_address
    @.minOccurs(0) @type(ipf:string)
    option link_mtu
    @.minOccurs(0) @type(ipf:string)
    option lladdr
    @.minOccurs(0) @type(ipf:string)
    option local
    @.minOccurs(0) @type(ipf:string)
    option log
    @.minOccurs(0) @type(ipf:string)
    option log_append
    @.minOccurs(0) @type(ipf:string)
    option lport
    @.minOccurs(0) @type(ipf:string)
    option management_forget_disconnect
    @.minOccurs(0) @type(ipf:string)
    option management_hold
    @.minOccurs(0) @type(ipf:string)
    option management_log_cache
    @.minOccurs(0) @type(ipf:string)
    option management_query_passwords
    @.minOccurs(0) @type(ipf:string)
    option management_signal
    @.minOccurs(0) @type(ipf:string)
    option max_routes_per_client
    @.minOccurs(0) @type(ipf:string)
    option mktun
    @.minOccurs(0) @type(ipf:string)
    option mlock
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option mssfix
    @.minOccurs(0) @type(ipf:string)
    option mtu_disc
    @.minOccurs(0) @type(ipf:string)
    option mtu_test
    @.minOccurs(0) @type(ipf:string)
    option multihome
    @.minOccurs(0) @type(ipf:string)
    option mute
    @.minOccurs(0) @type(ipf:string)
    option mute_replay_warnings
    @.minOccurs(0) @type(ipf:string)
    option nice
    @.minOccurs(0) @type(ipf:string)
    option no_iv
    @.minOccurs(0) @type(ipf:string)
    option no_name_remapping
    @.minOccurs(0) @type(ipf:string)
    option no_replay
    @.minOccurs(0) @type(ipf:string)
    option nobind
    @.minOccurs(0) @type(ipf:string)
    option ns_cert_type
    @.minOccurs(0) @type(ipf:string)
    option opt_verify
    @.minOccurs(0) @type(ipf:string)
    option passtos
    @.minOccurs(0) @type(ipf:string)
    option password
    @.minOccurs(0) @type(ipf:string)
    option persist_local_ip
    @.minOccurs(0) @type(ipf:string)
    option persist_remote_ip
    @.minOccurs(0) @type(ipf:string)
    option ping
    @.minOccurs(0) @type(ipf:string)
    option ping_exit
    @.minOccurs(0) @type(ipf:string)
    option ping_restart
    @.minOccurs(0) @type(ipf:string)
    option ping_timer_rem
    @.minOccurs(0) @type(ipf:string)
    option pkcs12
    @.minOccurs(0) @type(ipf:string)
    option plugin
    @.minOccurs(0) @type(ipf:string)
    option port_share
    @.minOccurs(0) @type(ipf:string)
    option prng
    @.minOccurs(0) @type(ipf:string)
    option pull
    @.minOccurs(0) @type(ipf:string)
    option push
    @.minOccurs(0) @type(ipf:string)
    option push_reset
    @.minOccurs(0) @type(ipf:string)
    option rcvbuf
    @.minOccurs(0) @type(ipf:string)
    option redirect_gateway
    @.minOccurs(0) @type(ipf:string)
    option remap_usr1
    @.minOccurs(0) @type(ipf:string)
    option remote
    @.minOccurs(0) @type(ipf:string)
    option remote_cert_eku
    @.minOccurs(0) @type(ipf:string)
    option remote_cert_ku
    @.minOccurs(0) @type(ipf:string)
    option remote_cert_tls
    @.minOccurs(0) @type(ipf:string)
    option remote_random
    @.minOccurs(0) @type(ipf:string)
    option reneg_bytes
    @.minOccurs(0) @type(ipf:string)
    option reneg_pkts
    @.minOccurs(0) @type(ipf:string)
    option reneg_sec
    @.minOccurs(0) @type(ipf:string)
    option replay_persist
    @.minOccurs(0) @type(ipf:string)
    option replay_window
    @.minOccurs(0) @type(ipf:string)
    option resolv_retry
    @.minOccurs(0) @type(ipf:string)
    option rmtun
    @.minOccurs(0) @type(ipf:string)
    option route
    @.minOccurs(0) @type(ipf:string)
    option route_delay
    @.minOccurs(0) @type(ipf:string)
    option route_gateway
    @.minOccurs(0) @type(ipf:string)
    option route_metric
    @.minOccurs(0) @type(ipf:string)
    option route_noexec
    @.minOccurs(0) @type(ipf:string)
    option route_nopull
    @.minOccurs(0) @type(ipf:string)
    option route_up
    @.minOccurs(0) @type(ipf:string)
    option rport
    @.minOccurs(0) @type(ipf:string)
    option script_security
    @.minOccurs(0) @type(ipf:string)
    option secret
    @.minOccurs(0) @type(ipf:string)
    option server_bridge
    @.minOccurs(0) @type(ipf:string)
    option setenv
    @.minOccurs(0) @type(ipf:string)
    option shaper
    @.minOccurs(0) @type(ipf:string)
    option single_session
    @.minOccurs(0) @type(ipf:string)
    option sndbuf
    @.minOccurs(0) @type(ipf:string)
    option socks_proxy
    @.minOccurs(0) @type(ipf:string)
    option socks_proxy_retry
    @.minOccurs(0) @type(ipf:string)
    option suppress_timestamps
    @.minOccurs(0) @type(ipf:string)
    option syslog
    @.minOccurs(0) @type(ipf:string)
    option tcp_nodelay
    @.minOccurs(0) @type(ipf:string)
    option tcp_queue_limit
    @.minOccurs(0) @type(ipf:string)
    option test_crypto
    @.minOccurs(0) @type(ipf:string)
    option tls_auth
    @.minOccurs(0) @type(ipf:string)
    option tls_cipher
    @.minOccurs(0) @type(ipf:string)
    option tls_client
    @.minOccurs(0) @type(ipf:string)
    option tls_exit
    @.minOccurs(0) @type(ipf:string)
    option tls_remote
    @.minOccurs(0) @type(ipf:string)
    option tls_server
    @.minOccurs(0) @type(ipf:string)
    option tls_timeout
    @.minOccurs(0) @type(ipf:string)
    option tls_verify
    @.minOccurs(0) @type(ipf:string)
    option tmp_dir
    @.minOccurs(0) @type(ipf:string)
    option topology
    @.minOccurs(0) @type(ipf:string)
    option tran_window
    @.minOccurs(0) @type(ipf:string)
    option tun_ipv6
    @.minOccurs(0) @type(ipf:string)
    option tun_mtu
    @.minOccurs(0) @type(ipf:string)
    option tun_mtu_extra
    @.minOccurs(0) @type(ipf:string)
    option txqueuelen
    @.minOccurs(0) @type(ipf:string)
    option up
    @.minOccurs(0) @type(ipf:string)
    option up_delay
    @.minOccurs(0) @type(ipf:string)
    option up_restart
    @.minOccurs(0) @type(ipf:string)
    option user
    @.minOccurs(0) @type(ipf:string)
    option username
    @.minOccurs(0) @type(ipf:string)
    option username_as_common_name
    @.minOccurs(0) @type(ipf:string)
    option vpntype
