
@.minOccurs(0) @name
config mlo
    @.minOccurs(0) @type(ipf:bool)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option ssid
    @.minOccurs(0)
    option password
    @.minOccurs(0) @type(ipf:string)
    option encryption
    @.minOccurs(0) @type(ipf:string)
    option band2
    @.minOccurs(0) @type(ipf:string)
    option band5
    @.minOccurs(0) @type(ipf:string)
    option band5_2
    @.minOccurs(0) @type(ipf:string)
    option band6
    @.minOccurs(0) @type(ipf:string)
    option band6_2
    @.minOccurs(0) @type(ipf:string)
    option hidessid
    @.minOccurs(0) @type(ipf:string)
    option reserved
    @.minOccurs(0) @type(ipf:string)
    option band_support_bmap
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option variation
