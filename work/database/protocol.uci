
@.minOccurs(0) @.maxOccurs(unbounded) @name
config interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mactype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mask_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom_mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom_value
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto_name
@.minOccurs(0) @.maxOccurs(unbounded) @name
config proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pri_dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option snd_dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option unicast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option auto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_start
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option conn_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option snd_conn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option specific_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manual_idle
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_end
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option demand_idle
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AFTR_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list AFTR_name_list_private
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list AFTR_name_list_public
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option is_support_dynamic_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dynamic_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wanv6_proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wanv6_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option dynamic_ip_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dynamic_ip_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dynamic_dns_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pppoe_dns_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pppoe_ip_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pppoe_ip_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option carrier
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keeplive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option lanif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ttl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option peerdns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option mru
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connectable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option prefixlen
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option leasetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option endip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option startip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option rdnss_prefix_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac_clone_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ILLEGAL_FIRST_CHAR_6rd_prefix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prefix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_pridns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_snddns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ILLEGAL_FIRST_CHAR_6rd_prefix_len
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option conn_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dnsv6_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_gateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_pridns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dyn_snddns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option inet_ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option inet_pridns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option inet_snddns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip6addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip6gw
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipv4_mask_len
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipv6_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipv6_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manual_pridns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manual_snddns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option relay_ipv4_addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option specific_ipv6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_gateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_pridnsv6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option static_snddnsv6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tunnel_addr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
@.minOccurs(0) @.maxOccurs(unbounded) @name
config connproto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list protos
@.minOccurs(0) @.maxOccurs(unbounded) @name
config network
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preferred
@.minOccurs(0) @.maxOccurs(unbounded) @name
config vpn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option des
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encryption
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option listen_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wg_dns_bk
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option public_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endpoint_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allowed_ips
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preshared_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option persistent_keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nat
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connectable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
