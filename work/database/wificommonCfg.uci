
@.minOccurs(0) @name
config wificommonCfg
    @.minOccurs(0) @type(ipf:string)
    option bandMode
    @.minOccurs(0) @type(ipf:string)
    option WIFIRegion
    @.minOccurs(0) @type(ipf:string)
    option regionChange
    @.minOccurs(0) @type(ipf:string)
    option eth2wifi
    @.minOccurs(0) @type(ipf:bool)
    option mesh_enable
    @.minOccurs(0) @type(ipf:string)
    option remode_twt
    @.minOccurs(0) @type(ipf:string)
    option remode_ofdma
    @.minOccurs(0) @type(ipf:string)
    option remode_mimo
