
@.minOccurs(0) @name
config speed_test_result
    @.minOccurs(0) @type(ipf:string)
    option up_speed
    @.minOccurs(0) @type(ipf:string)
    option down_speed
    @.minOccurs(0) @type(ipf:string)
    option test_time
    @.minOccurs(0) @type(ipf:string)
    option latency
@.minOccurs(0) @name
config service_status
    @.minOccurs(0) @type(ipf:string)
    option exp_time
    @.minOccurs(0) @type(ipf:string)
    option is_avail
    @.minOccurs(0) @type(ipf:string)
    option is_first
@.minOccurs(0) @type(ipf:any)
config record
