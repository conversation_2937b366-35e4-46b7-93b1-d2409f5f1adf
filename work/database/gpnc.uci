
@.minOccurs(0) @.maxOccurs(unbounded) @name
config gpnc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option first_flag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option log_level
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option log_dir
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option comp_info
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option pro_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option stg_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option account_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list platform
@.minOccurs(0) @.maxOccurs(unbounded) @name
config client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option app_meta_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option config_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option app_meta_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option app_meta_cover_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option platform
