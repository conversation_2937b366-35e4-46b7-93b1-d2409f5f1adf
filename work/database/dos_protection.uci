
@.minOccurs(0) @name
config global
    @.minOccurs(0) @type(ipf:string)
    option syn_level
    @.minOccurs(0) @type(ipf:string)
    option syn_low
    @.minOccurs(0) @type(ipf:bool)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option syn_middle
    @.minOccurs(0) @type(ipf:string)
    option icmp_curr
    @.minOccurs(0) @type(ipf:string)
    option time
    @.minOccurs(0) @type(ipf:string)
    option udp_level
    @.minOccurs(0) @type(ipf:string)
    option icmp_middle
    @.minOccurs(0) @type(ipf:string)
    option icmp_high
    @.minOccurs(0) @type(ipf:string)
    option icmp_low
    @.minOccurs(0) @type(ipf:string)
    option udp_high
    @.minOccurs(0) @type(ipf:string)
    option udp_middle
    @.minOccurs(0) @type(ipf:string)
    option udp_curr
    @.minOccurs(0) @type(ipf:string)
    option udp_low
    @.minOccurs(0) @type(ipf:string)
    option syn_high
    @.minOccurs(0) @type(ipf:string)
    option syn_curr
    @.minOccurs(0) @type(ipf:string)
    option icmp_level
