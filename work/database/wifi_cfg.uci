
@.minOccurs(0) @name
config staCfg
    @.minOccurs(0)
    option rootApBssid
    @.minOccurs(0)
    option rootApSsid
    @.minOccurs(0)
    option data
    @.minOccurs(0) @type(ipf:string)
    option support_mesh
    @.minOccurs(0) @type(ipf:string)
    option wlanSecType
    @.minOccurs(0) @type(ipf:string)
    option psk_wpaVersion
    @.minOccurs(0) @type(ipf:string)
    option psk_wpaEncryption
    @.minOccurs(0) @type(ipf:string)
    option psk_keyUpdatePeriod
    @.minOccurs(0) @type(ipf:string)
    option wep_wepAuthType
    @.minOccurs(0) @type(ipf:string)
    option wep_wepKeyFormat
    @.minOccurs(0) @type(ipf:string)
    option wep_wepKeyIndex
    @.minOccurs(0) @type(ipf:string)
    option ent_wpaVersion
    @.minOccurs(0) @type(ipf:string)
    option ent_keyUpdatePeriod
    @.minOccurs(0) @type(ipf:string)
    option ent_radiusIp
    @.minOccurs(0) @type(ipf:string)
    option ent_radiusPort
    @.minOccurs(0) @type(ipf:string)
    option ent_encryption
    @.minOccurs(0)
    option ent_password
    @.minOccurs(0)
    option password
    @.minOccurs(0) @type(ipf:bool)
    option staBssEnabled
@.minOccurs(0) @name
config apCfg
    @.minOccurs(0) @type(ipf:string)
    option localApSsid
    @.minOccurs(0) @type(ipf:string)
    option wlanSecType
    @.minOccurs(0) @type(ipf:string)
    option psk_wpaVersion
    @.minOccurs(0) @type(ipf:string)
    option psk_wpaEncryption
    @.minOccurs(0) @type(ipf:string)
    option psk_keyUpdatePeriod
    @.minOccurs(0) @type(ipf:string)
    option wep_wepAuthType
    @.minOccurs(0) @type(ipf:string)
    option wep_wepKeyFormat
    @.minOccurs(0) @type(ipf:string)
    option wep_wepKeyIndex
    @.minOccurs(0) @type(ipf:string)
    option ent_wpaVersion
    @.minOccurs(0) @type(ipf:string)
    option ent_keyUpdatePeriod
    @.minOccurs(0) @type(ipf:string)
    option ent_radiusIp
    @.minOccurs(0) @type(ipf:string)
    option ent_radiusPort
    @.minOccurs(0) @type(ipf:string)
    option ent_encryption
    @.minOccurs(0)
    option ent_password
    @.minOccurs(0)
    option password
    @.minOccurs(0) @type(ipf:bool)
    option apBssEnabled
    @.minOccurs(0) @type(ipf:bool)
    option apBssEnabledApMode
    @.minOccurs(0) @type(ipf:string)
    option apHideSSID
    @.minOccurs(0) @type(ipf:string)
    option apHideSSIDApMode
    @.minOccurs(0) @type(ipf:string)
    option apFollowSTASec
@.minOccurs(0) @name
config advCfg
    @.minOccurs(0) @type(ipf:bool)
    option bIsolationEnabled
    @.minOccurs(0) @type(ipf:string)
    option bShortGI
    @.minOccurs(0) @type(ipf:string)
    option bShortPreambleDisabled
    @.minOccurs(0) @type(ipf:bool)
    option bWMEEnabled
    @.minOccurs(0) @type(ipf:string)
    option uBeaconInterval
    @.minOccurs(0) @type(ipf:string)
    option uDTIMInterval
    @.minOccurs(0) @type(ipf:string)
    option uFragThreashold
    @.minOccurs(0) @type(ipf:string)
    option uPower
    @.minOccurs(0) @type(ipf:string)
    option uRTSThreshold
@.minOccurs(0) @name
config aclCfg
    @.minOccurs(0) @type(ipf:bool)
    option aclModeEnable
    @.minOccurs(0) @type(ipf:string)
    option filterRule
    @.minOccurs(0) @type(ipf:string)
    option aclNumber
@.minOccurs(0) @name
config radioCfg
    @.minOccurs(0) @type(ipf:bool)
    option bandenable
    @.minOccurs(0) @type(ipf:string)
    option channel
    @.minOccurs(0) @type(ipf:string)
    option chWidth
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option wifiMac
    @.minOccurs(0) @type(ipf:string)
    option apmode_twt
    @.minOccurs(0) @type(ipf:string)
    option apmode_ofdma
    @.minOccurs(0) @type(ipf:string)
    option apmode_mimo
@.minOccurs(0) @name
config backhaulApCfg
    @.minOccurs(0) @type(ipf:string)
    option hiddenApSsid
    @.minOccurs(0) @type(ipf:string)
    option hiddenApKey
