
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cloud_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_flag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pc_enter_tether_guide_page
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pc_click_continue_web_management_button
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_first_onboarding
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval_2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval_4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option post_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_set_each
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auto_fw_collect_flag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option restart_time_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option device_model
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hw_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hw_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option oem_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sw_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_id_login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_find_fw
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_new_fw
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_update
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_timestamp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option download_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option count_add_click
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option plugged_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option detect_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option detect_name_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option clear_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option account_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accelerate_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option info
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enter_user
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enter_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option scan_user
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option scan_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option firmwareVer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option viewPageViaWAN
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pingViaWAN
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option WPS
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option portTrigger
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option portForwarding
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UPnP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DMZ
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main2G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main5G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main5G1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main5G2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option main6G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guestNetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest2G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest5G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest5G1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest5G2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest6G
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option reset_login_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_auto_detect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option restart_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mobile_enter_tether_guide_page
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mobile_click_download_tether_button
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mobile_click_continue_web_management_button
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tp_id_collect_flag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accessKey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accessSecret
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option strategyID
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wtfast_post_interval
@.minOccurs(0) @.maxOccurs(unbounded) @name
config basic
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cloud_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accessKey
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accessSecret
@.minOccurs(0) @.maxOccurs(unbounded) @name
config strategy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval_2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option collect_interval_4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option strategyID
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option post_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wtfast_post_interval
