
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option mciptv_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option igmp_snooping_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option igmp_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option igmp_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option udp_proxy_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option udp_proxy
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option mcwifi_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option handle_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option set_iptv_ports
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allow_no_internet
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allow_lan_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lanport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_lanport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option l2ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option l3ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan7
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan8
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode_kr_alias
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wanlanport
@.minOccurs(0) @.maxOccurs(unbounded) @name
config profile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option display_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option configure
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_item
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_item
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option iptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option iptv_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ipphone_item
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ipphone_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_item
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option mciptv_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vprio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option porttype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option seltype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_tag
@.minOccurs(0) @.maxOccurs(unbounded) @name
config state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bridge_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bridge_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bridgePort
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internet_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipphone_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vid_pos
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iptv_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lan_real_ifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option last_wan_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macvlan_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_lan_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vid_pos
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mciptv_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option net_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option net_vif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option passthrough_iptv_ifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option passthrough_net_ifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option passthrough_phone_ifs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option priority
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vids
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option viface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wan_in_bridge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wan_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wan_vif
