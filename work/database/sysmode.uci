
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @type(ipf:constant)
    option support
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:constant)
    option initial
    @.minOccurs(0) @type(ipf:string)
    option access
    @.minOccurs(0) @type(ipf:string)
    option basic_security_settings
    @.minOccurs(0) @type(ipf:string)
    option iptv_iptv
    @.minOccurs(0) @type(ipf:string)
    option parental_control_settings
    @.minOccurs(0) @type(ipf:string)
    option qos_settings
    @.minOccurs(0) @type(ipf:string)
    option switch_addl_wan
    @.minOccurs(0) @type(ipf:string)
    option switch_lan_agg
    @.minOccurs(0) @type(ipf:string)
    option isolate
    @.minOccurs(0) @type(ipf:string)
    option network_ipaddr
