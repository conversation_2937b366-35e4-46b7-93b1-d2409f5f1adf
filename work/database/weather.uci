
@.minOccurs(0) @.maxOccurs(unbounded) @name
config baseconfig
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option city
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option location_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperature_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option error
@.minOccurs(0) @.maxOccurs(unbounded) @name
config realtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option errorcode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option message
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_renew
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option month
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperature
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperatureMax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperatureMin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weatherType
@.minOccurs(0) @.maxOccurs(unbounded) @name
config forecast
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperatureMax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option temperatureMin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dayWeatherType
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nightWeatherType
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option month
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option errorcode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option message
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_renew
