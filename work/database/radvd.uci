
@.minOccurs(0) @name
config interface
    @.minOccurs(0) @type(ipf:constant)
    option AdvManagedFlag
    @.minOccurs(0) @type(ipf:constant)
    option AdvSendAdvert
    @.minOccurs(0) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @.maxOccurs(unbounded)
    list client
    @.minOccurs(0) @type(ipf:constant)
    option AdvOtherConfigFlag
    @.minOccurs(0) @type(ipf:constant)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option AdvCurHopLimit
    @.minOccurs(0) @type(ipf:string)
    option AdvDefaultLifetime
    @.minOccurs(0) @type(ipf:string)
    option AdvDefaultPreference
    @.minOccurs(0) @type(ipf:string)
    option AdvHomeAgentFlag
    @.minOccurs(0) @type(ipf:string)
    option AdvHomeAgentInfo
    @.minOccurs(0) @type(ipf:string)
    option AdvIntervalOpt
    @.minOccurs(0) @type(ipf:string)
    option AdvLinkMTU
    @.minOccurs(0) @type(ipf:string)
    option AdvMobRtrSupportFlag
    @.minOccurs(0) @type(ipf:string)
    option AdvReachableTime
    @.minOccurs(0) @type(ipf:string)
    option AdvRetransTimer
    @.minOccurs(0) @type(ipf:string)
    option AdvSourceLLAddress
    @.minOccurs(0) @type(ipf:string)
    option HomeAgentLifetime
    @.minOccurs(0) @type(ipf:string)
    option HomeAgentPreference
    @.minOccurs(0) @type(ipf:string)
    option IgnoreIfMissing
    @.minOccurs(0) @type(ipf:string)
    option MaxRtrAdvInterval
    @.minOccurs(0) @type(ipf:string)
    option MinDelayBetweenRAs
    @.minOccurs(0) @type(ipf:string)
    option MinRtrAdvInterval
    @.minOccurs(0) @type(ipf:string)
    option UnicastOnly
@.minOccurs(0) @name
config prefix
    @.minOccurs(0) @type(ipf:constant)
    option AdvRouterAddr
    @.minOccurs(0) @type(ipf:constant)
    option AdvAutonomous
    @.minOccurs(0) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @.maxOccurs(unbounded)
    list prefix
    @.minOccurs(0) @type(ipf:constant)
    option AdvOnLink
    @.minOccurs(0) @type(ipf:constant)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option AdvPreferredLifetime
    @.minOccurs(0) @type(ipf:string)
    option AdvValidLifetime
    @.minOccurs(0) @type(ipf:string)
    option Base6Interface
    @.minOccurs(0) @type(ipf:string)
    option Base6to4Interface
    @.minOccurs(0) @type(ipf:string)
    option DecrementLifetimes
    @.minOccurs(0) @type(ipf:string)
    option DeprecatePrefix
@.minOccurs(0) @name
config route
    @.minOccurs(0) @.maxOccurs(unbounded)
    list prefix
    @.minOccurs(0) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @type(ipf:constant)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option AdvRouteLifetime
    @.minOccurs(0) @type(ipf:string)
    option AdvRoutePreference
    @.minOccurs(0) @type(ipf:string)
    option RemoveRoute
@.minOccurs(0) @name
config rdnss
    @.minOccurs(0) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @.maxOccurs(unbounded)
    list addr
    @.minOccurs(0) @type(ipf:constant)
    option interface
@.minOccurs(0) @name
config RDNSS
    @.minOccurs(0) @type(ipf:string)
    option addr
    @.minOccurs(0) @type(ipf:string)
    option AdvRDNSSLifetime
    @.minOccurs(0) @type(ipf:string)
    option FlushRDNSS
    @.minOccurs(0) @type(ipf:string)
    option ignore
    @.minOccurs(0) @type(ipf:string)
    option interface
@.minOccurs(0) @name
config dnssl
    @.minOccurs(0) @.maxOccurs(unbounded)
    list suffix
    @.minOccurs(0) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @type(ipf:constant)
    option interface
@.minOccurs(0) @name
config DNSSL
    @.minOccurs(0) @type(ipf:string)
    option AdvDNSSLLifetime
    @.minOccurs(0) @type(ipf:string)
    option FlushDNSSL
    @.minOccurs(0) @type(ipf:string)
    option ignore
    @.minOccurs(0) @type(ipf:string)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option suffix
@.minOccurs(0) @name
config radvd
    @.minOccurs(0) @type(ipf:string)
    option config_file
    @.minOccurs(0) @type(ipf:string)
    option ignore
