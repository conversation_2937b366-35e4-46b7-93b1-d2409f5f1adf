
@.minOccurs(0)
config replicate
    @.minOccurs(0) @type(ipf:string)
    option sambashare
@.minOccurs(0) @name
config samba
    @.minOccurs(0) @type(ipf:string)
    option charset
    @.minOccurs(0) @type(ipf:string)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option name
    @.minOccurs(0) @type(ipf:constant)
    option description
    @.minOccurs(0) @type(ipf:constant)
    option workgroup
    @.minOccurs(0) @type(ipf:constant)
    option homes
@.minOccurs(0)
config sambashare 'sambashare'
    @.minOccurs(0) @type(ipf:constant)
    option name
    @.minOccurs(0) @type(ipf:constant)
    option guest_ok
    @.minOccurs(0) @type(ipf:constant)
    option path
    @.minOccurs(0) @type(ipf:constant)
    option read_only
