
@.minOccurs(0) @.maxOccurs(unbounded)
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serial
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option model
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vendor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:integer)
    option capacity
@.minOccurs(0) @.maxOccurs(unbounded)
config volumn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serial
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option devname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mntdir
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:integer)
    option capacity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:integer)
    option used
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config folder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option index
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sharename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option path
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option read_users
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option write_users
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sharetype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option gnetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
