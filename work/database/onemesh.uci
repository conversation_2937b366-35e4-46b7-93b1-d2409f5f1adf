
@.minOccurs(0) @.maxOccurs(unbounded) @name
config onemesh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option role
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option group_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option autodiscovery_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option autosync_disable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option onemesh_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option level
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option slave_auto_attach_needed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option slave_auto_join_needed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option backhaul_connect
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option master_auto_probe_needed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option update
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option macaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tpie_label
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wifi-iface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option onemesh_ie
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wps_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_cipher
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hidden
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pin
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_pbc
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option network
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_select
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option iot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option extap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wds
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option athnewind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ssid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option encryption
    @.minOccurs(0) @.maxOccurs(unbounded)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option disablecoext
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ppe_vp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mld
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nrshareflag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owe
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sae
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option backhaul
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wpa_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_cipher
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option isolate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps_state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_format3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wds_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wep_type4
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option atf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option rrm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option wnm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MapBSSType
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option map
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option onemesh_config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ngvhtintop
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option a4_enable
