
@.minOccurs(0) @name
config global
    @.minOccurs(0) @type(ipf:string)
    option ad_guard
    @.minOccurs(0) @type(ipf:string)
    option app_limit_time
    @.minOccurs(0) @type(ipf:string)
    option game_enable
    @.minOccurs(0) @type(ipf:string)
    option traffic_enable
@.minOccurs(0) @name
config app_limit_rule
    @.minOccurs(0) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @type(ipf:string)
    option enable
    @.minOccurs(0) @type(ipf:string)
    option category_list
    @.minOccurs(0) @type(ipf:string)
    option mode
    @.minOccurs(0) @type(ipf:string)
    option daily_time
    @.minOccurs(0) @type(ipf:string)
    option enable_workday
    @.minOccurs(0) @type(ipf:string)
    option workday_time
    @.minOccurs(0) @type(ipf:string)
    option enable_weekend
    @.minOccurs(0) @type(ipf:string)
    option weekend_time
    @.minOccurs(0) @type(ipf:string)
    option enable_custom_day
    @.minOccurs(0) @type(ipf:string)
    option custom_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list category_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list app_id
@.minOccurs(0) @name
config block_rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list category_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list app_id
    @.minOccurs(0) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @type(ipf:string)
    option id
@.minOccurs(0) @name
config alway_allow_rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list category_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list app_id
    @.minOccurs(0) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @type(ipf:string)
    option id
