
@.minOccurs(0) @.maxOccurs(unbounded)
config config 'config'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option SwitchLanVid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option SwitchInterface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Control
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DisableSteering
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Enable
@.minOccurs(0) @.maxOccurs(unbounded)
config hy 'hy'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ExtraQueryResponseTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PathTransitionMethod
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxLBReordTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ConstrainTCPMedium
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LoadBalancingSeamless
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option HActiveMaxAge
@.minOccurs(0) @.maxOccurs(unbounded)
config Wlan 'Wlan'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option WlanCheckFreqInterval
@.minOccurs(0) @.maxOccurs(unbounded) @name
config Vlanid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vid
@.minOccurs(0) @.maxOccurs(unbounded)
config PathChWlan 'PathChWlan'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CPULimitedUDPThroughput_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilizationForLC_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ProbePacketInterval_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StatsAgedOutInterval_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorHigh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilization_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ProbePacketInterval_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UpdatedStatsInterval_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilization_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilizationForLC_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorHighRate_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MediumChangeThreshold_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ProbePacketSize_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableProbe_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NumUpdatesUntilStatsValid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LinkChangeThreshold_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CPULimitedUDPThroughput_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AssocDetectionDelay_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorTCP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StatsAgedOutInterval_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CPULimitedTCPThroughput_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorHighRate_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorLow
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MediumChangeThreshold_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UpdatedStatsInterval_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UseWHCAlgorithm
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableProbe_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CPULimitedTCPThroughput_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PHYRateThresholdForMU_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AssocDetectionDelay_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LinkChangeThreshold_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PHYRateThresholdForMU_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ProbePacketSize_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScalingFactorMedium
@.minOccurs(0) @.maxOccurs(unbounded)
config PathChPlc 'PathChPlc'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option HostPLCInterfaceSpeed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MediumChangeThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LinkChangeThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilizationForLC
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EntryExpirationInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StatsAgedOutInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LCThresholdForUnreachable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LCThresholdForReachable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UpdateStatsInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxMediumUtilization
@.minOccurs(0) @.maxOccurs(unbounded)
config Topology 'Topology'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option HA_AGING_INTERVAL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ENABLE_TD3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BD_UPDATE_INTERVAL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option HOLDING_TIME
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ND_UPDATE_INTERVAL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MSGID_DELTA
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TIMER_LOW_BOUND
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ENABLE_BD_SPOOFING
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TIMER_UPPER_BOUND
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NOTIFICATION_THROTTLING_WINDOW
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PERIODIC_QUERY_INTERVAL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ENABLE_NOTIFICATION_UNICAST
@.minOccurs(0) @.maxOccurs(unbounded)
config HSPECEst 'HSPECEst'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BufferAllocationThresholdHigh
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ReservedMemory
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NotificationThresholdLimit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BufferAllocationThresholdLow
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AlphaNumerator
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NotificationThresholdPercentage
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LocalFlowRatioThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UpdateHSPECInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AlphaDenominator
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LocalFlowRateThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxTrackedFlows
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxHActiveEntries
@.minOccurs(0) @.maxOccurs(unbounded)
config PathSelect 'PathSelect'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DeltaLCThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option SerialflowIterations
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NonUDPInterfaceOrder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UpdateHDInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LinkCapacityThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option UDPInterfaceOrder
@.minOccurs(0) @.maxOccurs(unbounded)
config LogSettings 'LogSettings'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogServerIP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogHEThreshold1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogHETables
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogPCP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogPCSummaryPeriodSec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogHE
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogRestartIntervalSec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogServerPort
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogPCW5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogPCW2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLog
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogTD
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogPSTables
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableLogPS
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LogHEThreshold2
@.minOccurs(0) @.maxOccurs(unbounded)
config IEEE1905Settings 'IEEE1905Settings'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option GenerateLLDP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StrictIEEE1905Mode
@.minOccurs(0) @.maxOccurs(unbounded)
config HCPSettings 'HCPSettings'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option V1Compat
@.minOccurs(0) @.maxOccurs(unbounded)
config SteerMsg 'SteerMsg'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LoadBalancingCompleteTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AvgUtilReqTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RspTimeout
