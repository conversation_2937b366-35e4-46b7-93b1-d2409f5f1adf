
@.minOccurs(0) @.maxOccurs(unbounded) @name
config provider
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option provider
@.minOccurs(0) @.maxOccurs(unbounded) @name
config service
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_network
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option service_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ip_source
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option check_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option retry_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option retry_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option check_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force_interval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option retry_times
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wan_bind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option cacert
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option update_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option use_https
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ddns_state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option state
