
@.minOccurs(0)
config replicate
    @.minOccurs(0) @type(ipf:string)
    option minidlna
@.minOccurs(0)
config minidlna 'config'
    @.minOccurs(0) @type(ipf:constant)
    option strict_dlna
    @.minOccurs(0) @type(ipf:constant)
    option enable_tivo
    @.minOccurs(0) @type(ipf:constant)
    option port
    @.minOccurs(0) @type(ipf:constant)
    option interface
    @.minOccurs(0) @type(ipf:string)
    option db_dir
    @.minOccurs(0) @type(ipf:constant)
    option inotify
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list media_dir
    @.minOccurs(0) @type(ipf:constant)
    option log_dir
    @.minOccurs(0) @type(ipf:constant)
    option album_art_names
    @.minOccurs(0) @type(ipf:constant)
    option root_container
    @.minOccurs(0) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @type(ipf:constant)
    option notify_interval
    @.minOccurs(0) @type(ipf:constant)
    option model_number
    @.minOccurs(0) @type(ipf:string)
    option friendly_name
    @.minOccurs(0) @type(ipf:constant)
    option serial
    @.minOccurs(0) @type(ipf:string)
    option max_scanfile_num
    @.minOccurs(0) @type(ipf:string)
    option minissdpsocket
    @.minOccurs(0) @type(ipf:string)
    option presentation_url
    @.minOccurs(0) @type(ipf:string)
    option minidlna
