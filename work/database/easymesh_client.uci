
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option location
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom_location
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option joined
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option add_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connect_source
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connect_mode
