
@.minOccurs(0)
config replicate
    @.minOccurs(0) @type(ipf:string)
    option default
@.minOccurs(0)
config default 'dnsproxy'
    @.minOccurs(0) @type(ipf:constant)
    option query_timeout
    @.minOccurs(0) @type(ipf:constant_bool)
    option enable
    @.minOccurs(0) @type(ipf:constant)
    option check_interval
    @.minOccurs(0) @type(ipf:string)
    option domains
    @.minOccurs(0) @type(ipf:constant)
    option retry_times
