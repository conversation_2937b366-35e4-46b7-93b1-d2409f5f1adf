
@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:login,local,account,remote)
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option recovery
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option authentication
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option smtp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option from
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option to
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option https_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option preempt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option https_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option managers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option description
