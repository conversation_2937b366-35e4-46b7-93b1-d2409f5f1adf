
@.minOccurs(0) @.maxOccurs(unbounded) @name
config router_post
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option role
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud_push
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cloud_push
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fw_new_notify
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option login_count
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option remind_later
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option remind_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud_reply
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bind_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option need_unbind
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option need_checkupgrade
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option accountid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cloud_reply
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option download_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fw_title
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option release_log
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option version
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud_device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option alias
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcsp_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option show_flag
@.minOccurs(0) @.maxOccurs(unbounded) @name
config info
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pop_count_login
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pop_count_country_code
@.minOccurs(0) @.maxOccurs(unbounded) @name
config switch
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config cloud_url
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option anti_virus_white_list
