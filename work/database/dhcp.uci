
@.minOccurs(0) @.maxOccurs(unbounded)
config replicate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dnsmasq
@.minOccurs(0) @.maxOccurs(unbounded)
config dnsmasq 'dnsmasq'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option readethers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option expandhosts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option boguspriv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option localise_queries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option nonegcache
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option resolvfile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option rebind_localhost
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option rebind_protection
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option filterwin2k
    @.minOccurs(0) @.maxOccurs(unbounded)
    option local
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option authoritative
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option domainneeded
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option leasefile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option add_local_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list addnhosts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list bogusnxdomain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list notinterface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list rebind_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option add_local_hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cachelocal
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cachesize
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dbus
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_boot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcphostsfile
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcpleasemax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcpscript
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dnsforwardmax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ednspacket_max
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable_tftp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option fqdn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option local_ttl
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option logqueries
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nodaemon
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nohosts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nonwildcard
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option noresolv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option queryport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option strictorder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tftp_root
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option update_domain
@.minOccurs(0) @.maxOccurs(unbounded) @name
config dhcp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ignore
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option leasetime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option interface
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list dhcp_option
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dynamicdhcp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netmask
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option options
@.minOccurs(0) @.maxOccurs(unbounded) @name
config boot
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option filename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serveraddress
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option servername
@.minOccurs(0) @.maxOccurs(unbounded) @name
config circuitid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option circuitid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option target
@.minOccurs(0) @.maxOccurs(unbounded) @name
config domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
@.minOccurs(0) @.maxOccurs(unbounded) @name
config host
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
@.minOccurs(0) @.maxOccurs(unbounded) @name
config mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
@.minOccurs(0) @.maxOccurs(unbounded) @name
config mxhost
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pref
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option relay
@.minOccurs(0) @.maxOccurs(unbounded) @name
config remoteid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option remoteid
@.minOccurs(0) @.maxOccurs(unbounded) @name
config srvhost
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option class
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option srv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option target
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option weight
@.minOccurs(0) @.maxOccurs(unbounded) @name
config subscrid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option subscriberid
@.minOccurs(0) @.maxOccurs(unbounded) @name
config tag
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_option
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
@.minOccurs(0) @.maxOccurs(unbounded) @name
config userclass
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option userclass
@.minOccurs(0) @.maxOccurs(unbounded) @name
config vendorclass
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option force
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vendorclass
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ffs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
