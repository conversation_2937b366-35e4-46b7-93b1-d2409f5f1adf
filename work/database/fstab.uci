
@.minOccurs(0) @.maxOccurs(unbounded)
config replicate
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mount
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option swap
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option anon_mount
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option anon_swap
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option from_fstab
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option anon_fsck
@.minOccurs(0) @.maxOccurs(unbounded)
config mount 'mount'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option options
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enabled_fsck
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option target
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option fstype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_rootfs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
@.minOccurs(0) @.maxOccurs(unbounded)
config swap 'swap'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
