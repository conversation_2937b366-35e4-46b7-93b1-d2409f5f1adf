
@.minOccurs(0) @.maxOccurs(unbounded) @name
config reservation
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
@.minOccurs(0) @.maxOccurs(unbounded) @name
config dhcp_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option defaultGateway
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcpLeaseTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcpServer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option endIpAddress
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option primaryDns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option secondaryDns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option startIpAddress
