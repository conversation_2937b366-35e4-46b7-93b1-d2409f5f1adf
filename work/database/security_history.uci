
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bolm_attacks
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option bolm_ts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option botm_attacks
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option botm_ts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option checked_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option days
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_updating
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option last_save_ts
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option total_attacks
