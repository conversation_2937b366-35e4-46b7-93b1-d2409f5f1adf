
@.minOccurs(0)
config network
    @.minOccurs(0) @type(ipf:string)
    option exec
    @.minOccurs(0) @type(ipf:string)
    option init
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list affects
@.minOccurs(0)
config wireless
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config firewall
    @.minOccurs(0) @type(ipf:string)
    option init
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list affects
@.minOccurs(0)
config olsr
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config dhcp
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config dropbear
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config httpd
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config fstab
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config qos
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config system
    @.minOccurs(0) @type(ipf:string)
    option init
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list affects
@.minOccurs(0)
config luci_splash
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config upnpd
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config ntpclient
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config samba
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config tinyproxy
    @.minOccurs(0) @type(ipf:string)
    option init
@.minOccurs(0)
config appflow
    @.minOccurs(0) @type(ipf:string)
    option exec
