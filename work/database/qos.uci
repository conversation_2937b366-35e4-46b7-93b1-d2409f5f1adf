
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option chat
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option download
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option game
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option media
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option surf
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rDownband
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rUpband
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option down_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option down_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option high
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option low
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option middle
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option percent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option up_band
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option up_unit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option version
@.minOccurs(0) @.maxOccurs(unbounded) @name
config app
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tcp_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option udp_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option appid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option id
@.minOccurs(0) @.maxOccurs(unbounded) @name
config state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option loaded
@.minOccurs(0) @.maxOccurs(unbounded) @name
config rule
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option app
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option priority
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dev_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option phy
