
@.minOccurs(0) @.maxOccurs(unbounded) @name(enum:starttime,defcfg,wanerror,upgrade)
config wportal
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option seconds
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option defcfg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option time
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TYPE
