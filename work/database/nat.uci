
@.minOccurs(0) @.maxOccurs(unbounded) @name
config default
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option zones
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option norder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list network
@.minOccurs(0) @.maxOccurs(unbounded) @name
config nat_global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option hw_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option boost_enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config nat_dmz
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
@.minOccurs(0) @.maxOccurs(unbounded) @name
config nat_alg
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option rtsp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pptp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option l2tp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tftp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ftp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option h323
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipsec
@.minOccurs(0) @.maxOccurs(unbounded)
config rule_vs
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option internal_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ipaddr
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option external_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option index
@.minOccurs(0) @.maxOccurs(unbounded)
config rule_pt
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option external_protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option trigger_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option trigger_protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list external_port
@.minOccurs(0) @.maxOccurs(unbounded)
config fr6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
@.minOccurs(0) @.maxOccurs(unbounded)
config rule_fr6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option protocol
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
@.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:any)
config nat_state
