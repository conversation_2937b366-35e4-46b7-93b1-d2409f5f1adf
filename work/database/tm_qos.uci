
@.minOccurs(0) @name
config bandwidth
    @.minOccurs(0) @type(ipf:string)
    option bandwidth_mode
    @.minOccurs(0) @type(ipf:string)
    option download
    @.minOccurs(0) @type(ipf:string)
    option privacy
    @.minOccurs(0) @type(ipf:string)
    option upload
@.minOccurs(0) @name
config state
    @.minOccurs(0) @type(ipf:string)
    option qos_real_up
@.minOccurs(0) @name
config custom_detail
    @.minOccurs(0) @type(ipf:string)
    option change
    @.minOccurs(0) @type(ipf:string)
    option chat
    @.minOccurs(0) @type(ipf:string)
    option download
    @.minOccurs(0) @type(ipf:string)
    option game
    @.minOccurs(0) @type(ipf:string)
    option media
    @.minOccurs(0) @type(ipf:string)
    option surf
@.minOccurs(0) @name
config qos_mode
    @.minOccurs(0) @type(ipf:string)
    option mode
@.minOccurs(0) @name
config global
    @.minOccurs(0) @type(ipf:string)
    option qos_enable
