
@.minOccurs(0) @name
config profile
    @.minOccurs(0) @type(ipf:constant)
    option dfs_enab
    @.minOccurs(0) @type(ipf:constant)
    option support_160M
    @.minOccurs(0) @type(ipf:constant)
    option dslite_support
    @.minOccurs(0) @type(ipf:constant)
    option v6plus_support
    @.minOccurs(0) @type(ipf:string)
    option is_ce_model
    @.minOccurs(0) @type(ipf:string)
    option radar_scanning_time_5g
    @.minOccurs(0) @type(ipf:string)
    option radar_scanning_time_5g_2
    @.minOccurs(0) @type(ipf:string)
    option blockdfschan
    @.minOccurs(0) @type(ipf:string)
    option exclude_160M_5G
    @.minOccurs(0) @type(ipf:string)
    option exclude_160M_5GH
    @.minOccurs(0) @type(ipf:string)
    option exclude_160M_5GL
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    list region
    @.minOccurs(0) @type(ipf:string)
    option region_select_permission
    @.minOccurs(0) @type(ipf:string)
    option support_240M
    @.minOccurs(0) @type(ipf:string)
    option support_320M
@.minOccurs(0) @name
config tpfile
    @.minOccurs(0) @type(ipf:constant)
    option tpfile_support
@.minOccurs(0) @name
config emmc
    @.minOccurs(0) @type(ipf:constant)
    option emmc_support
@.minOccurs(0) @name
config server_ports
    @.minOccurs(0) @type(ipf:string)
    option openvpn_server_port
    @.minOccurs(0) @type(ipf:string)
    option wireguard_server_port
@.minOccurs(0) @name
config region
    @.minOccurs(0) @type(ipf:string)
    option exclude_160M_5GH
