
@.minOccurs(0) @.maxOccurs(unbounded) @name
config global
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option alias
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option info
@.minOccurs(0) @.maxOccurs(unbounded) @name
config client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option prio_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option schedule_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option owner_id
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option real_mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_period
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option source
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wire_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option time_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enableLimit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option online
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ip
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_type_changed
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option slots
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option slots_next_day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option repeats
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option repeats_next_day
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option no_repeat_exec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option family
