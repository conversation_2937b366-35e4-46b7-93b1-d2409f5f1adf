
@.minOccurs(0) @.maxOccurs(unbounded) @name
config state
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_hostname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_params
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dhcp_vendor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option http_user_agent
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hua_status
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option last_query_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mdns_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netbios_domain
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option netbios_name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option params_error
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac_num
@.minOccurs(0) @.maxOccurs(unbounded) @name
config setting
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option setting
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pp_state
