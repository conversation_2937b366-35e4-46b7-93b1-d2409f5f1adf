
@.minOccurs(0) @.maxOccurs(unbounded)
config global 'settings'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access_mode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option guest_enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest_limit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option new_device_notify
@.minOccurs(0) @.maxOccurs(unbounded)
config black_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
@.minOccurs(0) @.maxOccurs(unbounded)
config white_list
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option real_mac
