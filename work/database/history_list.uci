
@.minOccurs(0) @.maxOccurs(unbounded) @name
config list
    @.minOccurs(0) @type(ipf:string)
    option mac
    @.minOccurs(0) @type(ipf:string)
    option access_host
    @.minOccurs(0) @type(ipf:string)
    option first_access_time
    @.minOccurs(0) @type(ipf:string)
    option access_time
    @.minOccurs(0) @type(ipf:string)
    option hostname
    @.minOccurs(0) @type(ipf:string)
    option type
    @.minOccurs(0) @type(ipf:string)
    option nickname
    @.minOccurs(0) @type(ipf:string)
    option leave_time
    @.minOccurs(0) @type(ipf:string)
    option brand
    @.minOccurs(0) @type(ipf:string)
    option systemVersion
    @.minOccurs(0) @type(ipf:string)
    option http_upload
