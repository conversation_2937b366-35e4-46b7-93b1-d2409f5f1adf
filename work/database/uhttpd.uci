
@.minOccurs(0)
config replicate
    @type(ipf:string)
    option uhttpd
@.minOccurs(0)
config uhttpd 'main'
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option cgi_prefix
    @.minOccurs(0) @.maxOccurs(2) @type(ipf:string)
    list listen_https
    @.minOccurs(0) @.maxOccurs(2) @type(ipf:string)
    list listen_http
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option cert
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option tcp_keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option network_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option rfc1918_filter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option script_timeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option ciphers
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option max_requests
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option home
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:constant)
    option no_dirlists
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option error_page
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option index_page
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option interpreter
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lua_handler
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lua_prefix
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option no_symlinks
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option realm
@.minOccurs(0) @name
config cert
    @.minOccurs(0) @type(ipf:constant)
    option country
    @.minOccurs(0) @type(ipf:constant)
    option commonname
    @.minOccurs(0) @type(ipf:constant)
    option state
    @.minOccurs(0) @type(ipf:constant)
    option location
    @.minOccurs(0) @type(ipf:constant)
    option days
    @.minOccurs(0) @type(ipf:constant)
    option bits
