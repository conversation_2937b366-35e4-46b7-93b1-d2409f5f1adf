
@.minOccurs(0)
config dhcp6c 'state'
    @.minOccurs(0) @type(ipf:string)
    option bcmcs_server_address
    @.minOccurs(0) @type(ipf:string)
    option bcmcs_server_domain_name
    @.minOccurs(0) @type(ipf:string)
    option domain_name
    @.minOccurs(0) @type(ipf:string)
    option domain_name_servers
    @.minOccurs(0) @type(ipf:string)
    option nis_domain_name
    @.minOccurs(0) @type(ipf:string)
    option nis_server_address
    @.minOccurs(0) @type(ipf:string)
    option nisp_domain_name
    @.minOccurs(0) @type(ipf:string)
    option nisp_server_address
    @.minOccurs(0) @type(ipf:string)
    option ntp_servers
    @.minOccurs(0) @type(ipf:string)
    option sip_server_address
    @.minOccurs(0) @type(ipf:string)
    option sip_server_domain_name
