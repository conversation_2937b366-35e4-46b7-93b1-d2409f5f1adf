
@.minOccurs(0) @name
config plc
    @.minOccurs(0) @type(ipf:string)
    option Enabled
    @.minOccurs(0) @type(ipf:string)
    option bridgeIfname
    @.minOccurs(0) @type(ipf:string)
    option PlcIfname
    @.minOccurs(0) @type(ipf:string)
    option AggrLinkRate
    @.minOccurs(0) @type(ipf:string)
    option FwPib_Download
    @.minOccurs(0) @type(ipf:string)
    option NmkSelected
    @.minOccurs(0) @type(ipf:string)
    option Nmk
    @.minOccurs(0) @type(ipf:string)
    option NetworkPassWd
    @.minOccurs(0) @type(ipf:string)
    option MaxRemoteDevices
    @.minOccurs(0) @type(ipf:string)
    option TopoDiscovTimeOut
    @.minOccurs(0) @type(ipf:string)
    option VLANEnabled
    @.minOccurs(0) @type(ipf:string)
    option PibPath
@.minOccurs(0) @name
config AdvancedPlc
    @.minOccurs(0) @type(ipf:string)
    option DebugLevel
    @.minOccurs(0) @type(ipf:string)
    option RemDevSecurityEnabled
    @.minOccurs(0) @type(ipf:string)
    option CrashScopeEnabled
