
@.minOccurs(0) @name
config phyint
    @.maxOccurs(unbounded) @type(ipf:string)
    list altnet
    @.maxOccurs(unbounded) @type(ipf:string)
    list altnet1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option direction
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option network
@.minOccurs(0) @name
config igmpproxy
    @.minOccurs(0) @type(ipf:string)
    option quickleave
    @type(ipf:string)
    list altnet
