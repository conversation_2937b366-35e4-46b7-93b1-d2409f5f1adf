
@.minOccurs(0) @.maxOccurs(unbounded) @name
config info
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option clientInfo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option wps
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option hostNetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guestNetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option system
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option nightMode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkStatistic
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option led
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option networkPermission
@.minOccurs(0) @.maxOccurs(unbounded) @name
config system
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option lastFwUpdateTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option needUpload
@.minOccurs(0) @.maxOccurs(unbounded) @name
config client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cause
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option start_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option stop_time
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option pause
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option type
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
