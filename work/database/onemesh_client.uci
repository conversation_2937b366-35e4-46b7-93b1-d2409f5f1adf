
@.minOccurs(0) @.maxOccurs(unbounded) @name
config device
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option location
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mac
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option name
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tmp_username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option tmp_password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option joined
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option algo_version
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option custom_location
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option backupWCfgTransNeeded
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option mainWCfgTransNeeded
