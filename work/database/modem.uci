
@.minOccurs(0) @.maxOccurs(unbounded) @name
config usbmodeminfo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option defaultvid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option defaultpid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option targetvid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option targetpid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option modeswitch
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option proto
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option cport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dport
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ifname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option message1
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option message2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option message3
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option modem_type
@.minOccurs(0) @.maxOccurs(unbounded) @name
config usbmodemisp
    @.minOccurs(0) @.maxOccurs(unbounded)
    option setisp
    @.minOccurs(0) @.maxOccurs(unbounded)
    option locindex
    @.minOccurs(0) @.maxOccurs(unbounded)
    option ispindex
    @.minOccurs(0) @.maxOccurs(unbounded)
    option pinlock
    @.minOccurs(0) @.maxOccurs(unbounded)
    option pincode
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dial_num
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option apn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option authentype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option modem_signal
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ispstatus
    @.minOccurs(0) @.maxOccurs(unbounded)
    option modemstatus
    @.minOccurs(0) @.maxOccurs(unbounded)
    option mode
    @.minOccurs(0) @.maxOccurs(unbounded)
    option connectmode
    @.minOccurs(0) @.maxOccurs(unbounded)
    option maxidletime
    @.minOccurs(0) @.maxOccurs(unbounded)
    option mtu
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option manualdns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option primarydns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option seconddns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option connectstatus
