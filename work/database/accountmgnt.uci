
@.minOccurs(0)
config rsa 'keys'
    @type(ipf:constant)
    option e
    @type(ipf:constant)
    option d
    @type(ipf:constant)
    option n
@.minOccurs(0)
config meshrsa 'meshkeys'
    @type(ipf:constant)
    option e
    @type(ipf:constant)
    option d
    @type(ipf:constant)
    option n
@.minOccurs(0)
config account 'admin'
    @.minOccurs(0) @type(ipf:bool)
    option set_by_user
    @.minOccurs(0) @type(ipf:constant)
    option username
    @.minOccurs(0) @type(ipf:string)
    option password
    @.minOccurs(0) @type(ipf:bool)
    option ssh_is_enable
@.minOccurs(0)
config cloud_account 'admin'
    @.minOccurs(0) @type(ipf:string)
    option password
    @.minOccurs(0) @type(ipf:string)
    option accountid
    @.minOccurs(0) @type(ipf:string)
    option username
