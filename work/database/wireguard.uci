
@.minOccurs(0) @.maxOccurs(unbounded) @name
config service
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option persistent_keepalive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option listen_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dns
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option public_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option max_clients
@.minOccurs(0) @.maxOccurs(unbounded) @name
config peer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allowed_client
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option allowed_server
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_address
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_private_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option client_public_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option preshared_key
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option psk_enabled
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
