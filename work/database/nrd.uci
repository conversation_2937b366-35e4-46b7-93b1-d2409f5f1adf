
@.minOccurs(0) @.maxOccurs(unbounded) @name
config config
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:bool)
    option Enable
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PHYBasedPrioritization
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AgeLimit
@.minOccurs(0) @.maxOccurs(unbounded) @name
config wlanif
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option radio
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option vaps
@.minOccurs(0) @.maxOccurs(unbounded) @name
config IdleSteer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option OverloadInactTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSISteeringPoint_UG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option InactCheckInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSISteeringPoint_DG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NormalInactTimeout
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AuthAllow
@.minOccurs(0) @.maxOccurs(unbounded) @name
config ActiveSteer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RateRSSIXingThreshold_DG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RateRSSIXingThreshold_UG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TxRateXingThreshold_UG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TxRateXingThreshold_DG
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RateRSSIXingThreshold_DG_FromW6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RateRSSIXingThreshold_UG_FromW5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TxRateXingThreshold_DG_FromW6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TxRateXingThreshold_UG_FromW5
@.minOccurs(0) @.maxOccurs(unbounded) @name
config APSteer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_RE_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_RE_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option APSteerToRootMinRSSIIncThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_CAP_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_CAP_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdFloor_CAP_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdFloor_CAP_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DownlinkRSSIThreshold_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CliSteerRCPIThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option CliRCPIBtmThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option APSteerToPeerMinRSSIIncThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option APSteerToLeafMinRSSIIncThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BandRcpiDeltaThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DownlinkRSSIThreshold_W6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_CAP
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_CAP_W6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThreshold_RE
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdFloor_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdFloor_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_CAP_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_CAP_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_CAP_W6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_RE_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_RE_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIAPSteerThresholdIni_RE_W6
@.minOccurs(0) @.maxOccurs(unbounded) @name
config NetDB
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option DiscoverByScan
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ScanFrequency
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MinChannelTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxChannelTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AgeFrequency
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RemoteBSSMaxAge
@.minOccurs(0) @.maxOccurs(unbounded) @name
config StaDB
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option IncludeOutOfNetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option OutOfNetworkMaxAge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option InNetworkMaxAge
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AgingFrequency
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PollFrequency
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option NumRemoteBSSes
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AgingSizeThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PopulateNonServingPHYInfo
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MarkAdvClientAsDualBand
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AgeLimit
@.minOccurs(0) @.maxOccurs(unbounded) @name
config TriggerMonitor
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIMeasureSamples_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIMeasureSamples_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ThresholdEstimator
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowAPSteerThresholdFloor_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowAPSteerThresholdFloor_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowAPSteerThresholdFloor_W6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIMeasureSamples_W6
@.minOccurs(0) @.maxOccurs(unbounded) @name
config Estimator_Adv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ProbeCountThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StatsSampleInterval
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StatsSampleIntervalUsec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIDiff_EstW2FromW5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option EnableContinuousThroughput
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIDiff_EstW5FromW2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option PhyRateScalingForAirtime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BcnrptActiveDuration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ILLEGAL_FIRST_CHAR_11kProhibitTimeShort
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ILLEGAL_FIRST_CHAR_11kProhibitTimeLong
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BcnrptPassiveDuration
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option InterferenceAgeLimit
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIDiff_EstW5FromW6
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIDiff_EstW6FromW2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option RSSIDiff_EstW6FromW5
@.minOccurs(0) @.maxOccurs(unbounded) @name
config SteerExec
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option SteeringProhibitTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BTMSteeringProhibitShortTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MinRSSIBestEffort
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option InitialAuthRejCoalesceTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BlacklistTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option AuthRejMax
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxBTMUnfriendly
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option LowRSSIXingThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option SteeringUnfriendlyTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BTMResponseTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BTMAlsoBlacklist
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TargetLowRSSIThreshold_W2
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxBTMActiveUnfriendly
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BTMUnfriendlyTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxSteeringUnfriendly
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TSteering
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TargetLowRSSIThreshold_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option BTMAssociationTime
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option StartInBTMActiveState
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxConsecutiveBTMFailuresAsActive
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Delay24GProbeMinReqCount
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Delay24GProbeRSSIThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Delay24GProbeTimeWindow
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option TargetLowRSSIThreshold_W6
@.minOccurs(0) @.maxOccurs(unbounded) @name
config SteerAlg_Adv
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MinTxRateIncreaseThreshold
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option MaxSteeringTargetCount
@.minOccurs(0) @.maxOccurs(unbounded) @name
config IAS
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Enable_W5
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option Enable_W6
