
@.minOccurs(0) @.maxOccurs(unbounded) @name
config usbshare
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option svrname
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option dlna
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ftp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ftpex
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option ftpex_port
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option printer
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option samba
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option share_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option auth_all
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option guest_access
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option is_first
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option password
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option username
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sftp
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sftpex
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sftpex_port
@.minOccurs(0) @.maxOccurs(unbounded)
config volumn
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option serial
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option label
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option capacity
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option used
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
@.minOccurs(0) @.maxOccurs(unbounded) @name
config folder
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option index
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option uuid
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sharename
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option path
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option read_users
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option write_users
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option sharetype
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option gnetwork
    @.minOccurs(0) @.maxOccurs(unbounded) @type(ipf:string)
    option enable
