#ifndef XSD_EDITOR_UI_GTK_MAIN_WINDOW_H
#define XSD_EDITOR_UI_GTK_MAIN_WINDOW_H

#include <gtkmm.h>
#include <map>
#include "../base_window.h"
#include "element_tab.h"
#include "type_tab.h"
#include "device_tab.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

/**
 * @brief GTK主窗口类
 * 
 * 实现基于GTK的用户界面，
 * 包含菜单栏、工具栏、状态栏和三个主要页面。
 */
class MainWindow : public BaseWindow {
public:
    /**
     * @brief 构造函数
     * @param app 应用程序对象
     */
    MainWindow(const Glib::RefPtr<Gtk::Application>& app);

    /**
     * @brief 析构函数
     */
    virtual ~MainWindow() = default;

    // 实现基类的纯虚函数
    void show() override;
    void hide() override;
    void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items) override;
    void updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) override;
    void updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices) override;
    void showError(const std::string& message) override;
    void showInfo(const std::string& message) override;
    bool showConfirmDialog(const std::string& message) override;
    size_t addEventListener(UIEventListener listener) override;
    void removeEventListener(size_t id) override;

protected:
    void triggerEvent(UIEventType type, const std::string& data) override;
    void createMenuBar() override;
    void createToolBar() override;
    void createStatusBar() override;
    void createElementTab() override;
    void createTypeTab() override;
    void createDeviceTab() override;

private:
    /**
     * @brief 创建动作
     */
    void createActions();

    /**
     * @brief 创建快捷键
     */
    void createAccelerators();

    /**
     * @brief 处理文件菜单事件
     */
    void onFileMenuAction(const Glib::ustring& action);

    /**
     * @brief 处理编辑菜单事件
     */
    void onEditMenuAction(const Glib::ustring& action);

    /**
     * @brief 处理帮助菜单事件
     */
    void onHelpMenuAction(const Glib::ustring& action);

    /**
     * @brief 处理标签页切换事件
     */
    void onTabChanged(Gtk::Widget* page);

    // 菜单项动作
    void addMenuAction(const Glib::ustring& name,
                      const Glib::ustring& label,
                      const Glib::ustring& accel = "");

    // 工具栏按钮
    void addToolButton(const Glib::ustring& icon_name,
                      const Glib::ustring& label,
                      const Glib::ustring& action_name);

public:
    /**
     * @brief 获取当前选中的项目
     * @return 当前选中的项目的共享指针
     */
    std::shared_ptr<core::model::Item> getSelectedItem() const;
    std::vector<std::shared_ptr<core::model::Item>> getElements() const {
        return element_tab_->getElements();
    }

    void updateExtras(const std::vector<std::string>& extras) override;
    
    /**
     * @brief 获取extras字段
     * @return extras字段的内容
     */
    std::vector<std::string> getTypeExtras() const;
    
    /**
     * @brief 获取所有类型
     * @return 类型列表
     */
    std::vector<std::shared_ptr<core::model::AdvancedType>> getTypes() const;

    /**
     * @brief 获取所有设备
     * @return 设备列表
     */
    std::vector<std::shared_ptr<core::model::Device>> getDevices() const;

    /**
     * @brief 获取GTK窗口对象
     * @return GTK窗口对象的引用
     */
    Gtk::ApplicationWindow& getWindow();

private:
    Gtk::ApplicationWindow window_;            // 主窗口
    Gtk::Box main_box_;                       // 主布局
    Glib::RefPtr<Gtk::Builder> builder_;      // UI构建器
    Glib::RefPtr<Gio::Menu> menu_bar_;        // 菜单栏
    Gtk::Box toolbar_box_;                    // 工具栏容器
    Gtk::Label status_label_;                 // 状态标签
    Gtk::Notebook notebook_;                  // 标签页容器

    std::unique_ptr<ElementTab> element_tab_;  // 元素页面
    std::unique_ptr<TypeTab> type_tab_;        // 类型页面
    std::unique_ptr<DeviceTab> device_tab_;    // 设备页面

    // 动作组
    Glib::RefPtr<Gio::SimpleActionGroup> action_group_;

    // 事件监听器管理
    size_t next_listener_id_ = 0;
    std::map<size_t, UIEventListener> listeners_;

    // 状态管理
    bool is_modified_ = false;  // 是否有未保存的修改
};

} // namespace gtk
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_GTK_MAIN_WINDOW_H