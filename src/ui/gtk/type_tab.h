#ifndef XSD_EDITOR_UI_GTK_TYPE_TAB_H
#define XSD_EDITOR_UI_GTK_TYPE_TAB_H

#include <gtkmm.h>
#include <gtkmm/box.h>
#include <gtkmm/button.h>
#include <gtkmm/entry.h>
#include <gtkmm/grid.h>
#include <gtkmm/notebook.h>
#include <gtkmm/scrolledwindow.h>
#include <gtkmm/treeview.h>
#include <gtkmm/liststore.h>
#include <gtkmm/treeselection.h>
#include <gtkmm/comboboxtext.h>
#include <gtkmm/spinbutton.h>
#include <gtkmm/stringlist.h>
#include <gtkmm/columnview.h>
#include <gtkmm/singleselection.h>
#include <gtkmm/frame.h>
#include <gtkmm/image.h>
#include <memory>
#include "../../core/model/type.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

/**
 * @brief 类型页面类
 * 
 * 实现高级类型的列表显示和编辑功能，
 * 包括类型约束和额外属性的管理。
 */
class TypeTab : public Gtk::Box {
public:
    /**
     * @brief 构造函数
     */
    TypeTab();

    /**
     * @brief 析构函数
     */
    virtual ~TypeTab() = default;

    /**
     * @brief 更新类型列表
     * @param types 类型列表
     */
    void updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types);

    /**
     * @brief 获取当前选中的类型
     * @return 选中的类型指针
     */
    std::shared_ptr<core::model::AdvancedType> getSelectedType() const;

    /**
     * @brief 设置类型变更回调
     * @param callback 回调函数
     */
    void setChangeCallback(std::function<void()> callback);

    /**
     * @brief 获取所有类型
     * @return 类型列表
     */
    std::vector<std::shared_ptr<core::model::AdvancedType>> getTypes() const;
    
    /**
     * @brief 获取extras字段
     * @return extras字段的内容
     */
    std::vector<std::string> getExtras() const;
    
    /**
     * @brief 设置extras字段
     * @param extras extras字段的内容
     */
    void setExtras(const std::vector<std::string>& extras);
private:
    /**
     * @brief 创建类型列表
     */
    void createTypeList();

    /**
     * @brief 创建编辑面板
     */
    void createEditPanel();

    /**
     * @brief 创建约束编辑区域
     */
    void createConstraintPanel();

    /**
     * @brief 创建额外属性编辑区域
     */
    void createExtraAttrPanel();
    
    /**
     * @brief 创建extras编辑区域
     */
    void createExtrasPanel();

    /**
     * @brief 更新编辑面板
     * @param type 要编辑的类型
     */
    void updateEditPanel(const std::shared_ptr<core::model::AdvancedType>& type);

    /**
     * @brief 处理类型选择变更
     */
    void onSelectionChanged();

    /**
     * @brief 处理添加类型按钮点击
     */
    void onAddTypeClicked();

    /**
     * @brief 处理删除类型按钮点击
     */
    void onDeleteTypeClicked();

    /**
     * @brief 处理添加枚举值按钮点击
     */
    void onAddEnumClicked();

    /**
     * @brief 处理删除枚举值按钮点击
     */
    void onDeleteEnumClicked();

    /**
     * @brief 处理添加属性按钮点击
     */
    void onAddAttrClicked();

    /**
     * @brief 处理删除属性按钮点击
     */
    void onDeleteAttrClicked();

    /**
     * @brief 添加列表列
     * @param title 列标题
     * @param column 列模型
     */
    void addListColumn(const Glib::ustring& title,
                      const Gtk::TreeModelColumn<Glib::ustring>& column);

    /**
     * @brief 更新类型在列表中的显示
     * @param type 要更新的类型
     */
    void updateTypeInList(const std::shared_ptr<core::model::AdvancedType>& type);

    /**
      * @brief 设置按钮的图标和标签
      * @param button 要设置的按钮
      * @param icon_name 图标名称
      * @param label 按钮文字
      */
     void setupButton(Gtk::Button& button, const Glib::ustring& icon_name,
                     const Glib::ustring& label);

    // 类型列表相关
    class ModelColumns : public Gtk::TreeModel::ColumnRecord {
    public:
        ModelColumns() {
            add(col_name);
            add(col_base_type);
            add(col_type);
        }

        Gtk::TreeModelColumn<Glib::ustring> col_name;       // 类型名称
        Gtk::TreeModelColumn<Glib::ustring> col_base_type;  // 基础类型
        Gtk::TreeModelColumn<std::shared_ptr<core::model::AdvancedType>> col_type;  // 类型对象
    };

    ModelColumns columns_;
    std::shared_ptr<Gtk::TreeView> type_list_;
    Glib::RefPtr<Gtk::ListStore> list_store_;
    Glib::RefPtr<Gtk::TreeSelection> selection_;

    // 编辑面板相关
    Gtk::Box edit_box_;                  // 垂直布局盒子
    Gtk::Grid edit_grid_;                // 网格布局
    std::shared_ptr<Gtk::Entry> name_entry_;        // 类型名称输入框
    std::shared_ptr<Gtk::DropDown> base_type_combo_; // 基础类型下拉框
    std::shared_ptr<Gtk::DropDown> parent_combo_;    // 父类型下拉框

    // 约束编辑区域
    std::shared_ptr<Gtk::Notebook> constraint_notebook_;  // 约束标签页
    
    // 枚举值编辑
    std::shared_ptr<Gtk::ColumnView> enum_list_;    // 枚举值列表
    std::shared_ptr<Gtk::Button> add_enum_button_;  // 添加枚举值按钮
    std::shared_ptr<Gtk::Button> del_enum_button_;  // 删除枚举值按钮

    // 正则表达式编辑
    std::shared_ptr<Gtk::Entry> regex_entry_;       // 正则表达式输入框
    std::shared_ptr<Gtk::Button> test_regex_button_; // 测试正则按钮

    // 数值范围编辑
    std::shared_ptr<Gtk::SpinButton> min_spin_;     // 最小值输入框
    std::shared_ptr<Gtk::SpinButton> max_spin_;     // 最大值输入框

    // 属性列表模型
    class AttrColumns : public Gtk::TreeModel::ColumnRecord {
    public:
        AttrColumns() {
            add(col_key);
            add(col_value);
        }

        Gtk::TreeModelColumn<Glib::ustring> col_key;
        Gtk::TreeModelColumn<Glib::ustring> col_value;
    };
    AttrColumns attr_columns_;

    // 额外属性编辑
    Glib::RefPtr<Gtk::ListStore> attr_store_;
    Gtk::TreeView* attr_list_;    // 属性列表
    std::shared_ptr<Gtk::Button> add_attr_button_;  // 添加属性按钮
    std::shared_ptr<Gtk::Button> del_attr_button_;  // 删除属性按钮

    // 类型管理按钮
    Gtk::Box button_box_;                // 水平按钮盒子
    std::shared_ptr<Gtk::Button> add_type_button_;    // 添加类型按钮
    std::shared_ptr<Gtk::Button> delete_type_button_; // 删除类型按钮
    
    // extras编辑相关
    std::shared_ptr<Gtk::TextView> extras_text_view_;  // extras文本编辑框
    Glib::RefPtr<Gtk::TextBuffer> extras_buffer_;      // extras文本缓冲区
    std::vector<std::string> extras_;                  // extras内容

    std::function<void()> change_callback_;  // 变更回调函数
    bool updating_ = false;  // 是否正在更新UI（防止循环触发）
};

} // namespace gtk
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_GTK_TYPE_TAB_H