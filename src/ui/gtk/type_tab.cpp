#include "type_tab.h"
#include <iostream>
#include "../../utils/error.h"


namespace xsd_editor {
namespace ui {
namespace gtk {

TypeTab::TypeTab()
    : Gtk::Box()
    , edit_box_(Gtk::Orientation::VERTICAL)
    , button_box_(Gtk::Orientation::HORIZONTAL)
{
    // 设置主容器属性
    set_orientation(Gtk::Orientation::HORIZONTAL);
    set_spacing(5);
    set_margin(5);
    set_vexpand(true);
    set_hexpand(true);

    // 创建界面元素
    createTypeList();
    createEditPanel();
    createConstraintPanel();
    createExtraAttrPanel();
    createExtrasPanel();

    // 左侧布局：类型列表 + 按钮组
    auto left_box = Gtk::make_managed<Gtk::Box>();
    left_box->set_orientation(Gtk::Orientation::VERTICAL);
    left_box->set_spacing(5);
    left_box->set_hexpand(false);  // 不要水平扩展
    left_box->set_size_request(200, -1);  // 设置固定宽度

    // 类型列表应该垂直扩展
    type_list_->set_vexpand(true);
    type_list_->set_hexpand(true);

    // 滚动窗口设置
    auto scroll = Gtk::make_managed<Gtk::ScrolledWindow>();
    scroll->set_policy(Gtk::PolicyType::AUTOMATIC, Gtk::PolicyType::AUTOMATIC);
    scroll->set_has_frame(true);
    scroll->set_vexpand(true);
    scroll->set_hexpand(true);
    scroll->set_child(*type_list_);
    scroll->set_size_request(-1, 300);  // 设置合理的默认高度

    left_box->append(*scroll);
    left_box->append(button_box_);

    // 右侧编辑面板
    edit_box_.set_spacing(5);
    edit_box_.set_margin(5);
    edit_box_.set_hexpand(true);
    edit_box_.set_vexpand(true);
    edit_box_.set_size_request(400, -1);  // 设置最小宽度

    // 添加到主布局
    append(*left_box);
    append(edit_box_);

    // 初始状态：禁用编辑面板
    edit_box_.set_sensitive(false);
}

void TypeTab::createTypeList() {
    using namespace Gtk;
    using namespace Glib;

    // 创建列表存储
    list_store_ = ListStore::create(columns_);

    // 创建列视图
    type_list_ = std::make_shared<TreeView>();
    type_list_->set_model(list_store_);
    type_list_->append_column("Name", columns_.col_name);
    type_list_->append_column("Base Type", columns_.col_base_type);

    // 设置选择模式
    selection_ = type_list_->get_selection();
    selection_->signal_changed().connect(
        sigc::mem_fun(*this, &TypeTab::onSelectionChanged));

    // 创建滚动窗口
    auto scroll = std::make_shared<ScrolledWindow>();
    scroll->set_policy(PolicyType::AUTOMATIC, PolicyType::AUTOMATIC);
    scroll->set_has_frame(true);
    scroll->set_child(*type_list_);
    scroll->set_vexpand(true);
}

void TypeTab::addListColumn(const Glib::ustring& title,
                          const Gtk::TreeModelColumn<Glib::ustring>& column) {
    auto view_column = Gtk::make_managed<Gtk::TreeViewColumn>();
    auto cell = Gtk::make_managed<Gtk::CellRendererText>();
    view_column->pack_start(*cell);
    view_column->set_title(title);
    view_column->add_attribute(cell->property_text(), column);
    view_column->set_resizable(true);
    view_column->set_expand(true);
    type_list_->append_column(*view_column);
}

void TypeTab::createEditPanel() {
    edit_box_.set_spacing(5);
    edit_box_.set_margin(5);

    // 基本属性编辑网格
    edit_grid_.set_column_spacing(10);
    edit_grid_.set_row_spacing(5);
    edit_grid_.set_margin(5);
    edit_grid_.set_vexpand(false);  // 不要垂直扩展

    int row = 0;

    // 类型名称
    auto name_label = std::make_shared<Gtk::Label>("Name:");
    edit_grid_.attach(*name_label, 0, row, 1, 1);
    name_entry_ = std::make_shared<Gtk::Entry>();
    edit_grid_.attach(*name_entry_, 1, row++, 1, 1);

    // 基础类型
    auto base_type_label = std::make_shared<Gtk::Label>("Base Type:");
    edit_grid_.attach(*base_type_label, 0, row, 1, 1);
    
    auto base_type_model = Gtk::StringList::create({"string", "int", "bool"});
    base_type_combo_ = std::make_shared<Gtk::DropDown>(base_type_model);
    edit_grid_.attach(*base_type_combo_, 1, row++, 1, 1);

    // 父类型
    auto parent_label = std::make_shared<Gtk::Label>("Parent Type:");
    edit_grid_.attach(*parent_label, 0, row, 1, 1);
    parent_combo_ = std::make_shared<Gtk::DropDown>();
    edit_grid_.attach(*parent_combo_, 1, row++, 1, 1);

    edit_box_.append(edit_grid_);

    // 绑定事件
    name_entry_->signal_changed().connect([this]() {
        if (!updating_ && getSelectedType()) {
            auto type = getSelectedType();
            type->setName(name_entry_->get_text());
            updateTypeInList(type);
            if (change_callback_) change_callback_();
        }
    });

    base_type_combo_->property_selected().signal_changed().connect([this]() {
        if (!updating_ && getSelectedType()) {
            auto type = getSelectedType();
            auto selected = base_type_combo_->get_selected();
            auto model = std::dynamic_pointer_cast<Gtk::StringList>(base_type_combo_->get_model());
            if (model) {
                type->setBaseType(model->get_string(selected));
                updateTypeInList(type);
                if (change_callback_) change_callback_();
            }
        }
    });

    parent_combo_->property_selected().signal_changed().connect([this]() {
        if (!updating_ && getSelectedType()) {
            auto type = getSelectedType();
            auto selected = parent_combo_->get_selected();
            auto model = std::dynamic_pointer_cast<Gtk::StringList>(parent_combo_->get_model());
            if (model) {
                type->setParentType(model->get_string(selected));
                if (change_callback_) change_callback_();
            }
        }
    });
}

void TypeTab::createConstraintPanel() {
    // 约束标签页
    auto notebook = Gtk::make_managed<Gtk::Notebook>();
    notebook->set_vexpand(true);
    notebook->set_hexpand(true);
    
    // 设置合理的默认大小
    notebook->set_size_request(-1, 300);
    
    edit_box_.append(*notebook);

    // 枚举值页面
    auto enum_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    enum_box->set_margin(5);
    
    // 枚举列表
    enum_list_ = std::make_shared<Gtk::ColumnView>();
    enum_list_->set_vexpand(true);
    enum_list_->set_hexpand(true);
    
    auto enum_scroll = Gtk::make_managed<Gtk::ScrolledWindow>();
    enum_scroll->set_policy(Gtk::PolicyType::AUTOMATIC, Gtk::PolicyType::AUTOMATIC);
    enum_scroll->set_vexpand(true);
    enum_scroll->set_hexpand(true);
    enum_scroll->set_child(*enum_list_);
    enum_scroll->set_size_request(-1, 200);  // 设置合理的默认高度
    
    enum_box->append(*enum_scroll);
    
    // 按钮盒子不应该扩展
    auto enum_button_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::HORIZONTAL, 5);
    enum_button_box->set_vexpand(false);
    enum_button_box->set_margin(5);
    add_enum_button_ = std::make_shared<Gtk::Button>();
    del_enum_button_ = std::make_shared<Gtk::Button>();
    setupButton(*add_enum_button_, "list-add", "Add Value");
    setupButton(*del_enum_button_, "list-remove", "Delete Value");
    enum_button_box->append(*add_enum_button_);
    enum_button_box->append(*del_enum_button_);
    enum_box->append(*enum_button_box);

    notebook->append_page(*enum_box, "Enum Values");

    // 正则表达式页面
    auto regex_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    regex_entry_ = std::make_shared<Gtk::Entry>();
    test_regex_button_ = std::make_shared<Gtk::Button>();
    setupButton(*test_regex_button_, "system-run", "Test Expression");
    regex_box->append(*regex_entry_);
    regex_box->append(*test_regex_button_);

    notebook->append_page(*regex_box, "Regular Expression");

    // 数值范围页面
    auto range_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    
    auto min_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::HORIZONTAL, 5);
    auto min_label = Gtk::make_managed<Gtk::Label>("Minimum:");
    min_spin_ = std::make_shared<Gtk::SpinButton>();
    min_spin_->set_range(std::numeric_limits<int>::min(), 
                        std::numeric_limits<int>::max());
    min_box->append(*min_label);
    min_box->append(*min_spin_);
    range_box->append(*min_box);

    auto max_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::HORIZONTAL, 5);
    auto max_label = Gtk::make_managed<Gtk::Label>("Maximum:");
    max_spin_ = std::make_shared<Gtk::SpinButton>();
    max_spin_->set_range(std::numeric_limits<int>::min(), 
                        std::numeric_limits<int>::max());
    max_box->append(*max_label);
    max_box->append(*max_spin_);
    range_box->append(*max_box);

    notebook->append_page(*range_box, "Value Range");

    // 设置按钮事件
    add_enum_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onAddEnumClicked));
    del_enum_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onDeleteEnumClicked));
    test_regex_button_->signal_clicked().connect([]() {
        // TODO: 实现正则表达式测试
    });
}
void TypeTab::createExtraAttrPanel() {
    // 额外属性框架
    auto attr_frame = Gtk::make_managed<Gtk::Frame>("Extra Attributes");
    auto attr_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    
    attr_list_ = Gtk::make_managed<Gtk::TreeView>();
    attr_store_ = Gtk::ListStore::create(attr_columns_);
    //attr_selection = Gtk::SingleSelection::create(attr_store);
    attr_list_->set_model(attr_store_);
    attr_list_->append_column("Key", attr_columns_.col_key);
    attr_list_->append_column("Value", attr_columns_.col_value);
        // 设置列可编辑
    auto key_renderer = dynamic_cast<Gtk::CellRendererText*>(
        attr_list_->get_column(0)->get_first_cell());
    if (key_renderer) {
        key_renderer->property_editable() = true;
        key_renderer->signal_edited().connect(
            [this](const Glib::ustring& path, const Glib::ustring& new_text) {
                auto iter = attr_store_->get_iter(path);
                if (iter && !updating_ && getSelectedType()) {
                    auto element = getSelectedType();
                    Glib::ustring old_key = (*iter)[attr_columns_.col_key];
                    Glib::ustring value = (*iter)[attr_columns_.col_value];
                    
                    element->setExtraAttr(std::string(new_text), value);
                    
                    // 更新列表
                    (*iter)[attr_columns_.col_key] = new_text;
                    
                    if (change_callback_) change_callback_();
                }
            });
    }
    auto attr_scroll = Gtk::make_managed<Gtk::ScrolledWindow>();
    attr_scroll->set_child(*attr_list_);
    attr_scroll->set_vexpand(true);
    attr_box->append(*attr_scroll);
    
    auto attr_button_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::HORIZONTAL, 5);
    
    add_attr_button_ = std::make_shared<Gtk::Button>();
    setupButton(*add_attr_button_, "list-add", "Add");
    del_attr_button_ = std::make_shared<Gtk::Button>();
    setupButton(*del_attr_button_, "list-remove", "Delete");
    attr_button_box->append(*add_attr_button_);
    attr_button_box->append(*del_attr_button_);
    attr_box->append(*attr_button_box);

    attr_frame->set_child(*attr_box);
    edit_box_.append(*attr_frame);

    // 设置按钮事件
    add_attr_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onAddAttrClicked));
    del_attr_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onDeleteAttrClicked));

    // 创建类型管理按钮
    button_box_.set_spacing(5);
    button_box_.set_margin(5);

    add_type_button_ = std::make_shared<Gtk::Button>();
    setupButton(*add_type_button_, "list-add", "Add Type");
    add_type_button_->set_tooltip_text("Add new type");
    add_type_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onAddTypeClicked));
    button_box_.append(*add_type_button_);

    delete_type_button_ = std::make_shared<Gtk::Button>();
    setupButton(*delete_type_button_, "list-remove", "Delete Type");
    delete_type_button_->set_tooltip_text("Delete selected type");
    delete_type_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &TypeTab::onDeleteTypeClicked));
    button_box_.append(*delete_type_button_);
    return;
}
void TypeTab::createExtrasPanel() {
    // XSD Extras 编辑框架
    auto extras_frame = Gtk::make_managed<Gtk::Frame>("XSD Extras (Advanced)");
    auto extras_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    
    // 创建文本视图和滚动窗口
    extras_text_view_ = std::make_shared<Gtk::TextView>();
    extras_buffer_ = extras_text_view_->get_buffer();
    extras_text_view_->set_wrap_mode(Gtk::WrapMode::WORD);
    
    auto extras_scroll = Gtk::make_managed<Gtk::ScrolledWindow>();
    extras_scroll->set_child(*extras_text_view_);
    extras_scroll->set_vexpand(true);
    extras_box->append(*extras_scroll);
    
    // 添加说明标签
    auto help_label = Gtk::make_managed<Gtk::Label>();
    help_label->set_markup("<small>Add custom XSD content here. Each line will be added to the XSD schema.</small>");
    help_label->set_halign(Gtk::Align::START);
    extras_box->append(*help_label);
    
    extras_frame->set_child(*extras_box);
    edit_box_.append(*extras_frame);
    
    // 绑定文本变更事件
    extras_buffer_->signal_changed().connect([this]() {
        if (!updating_) {
            // 更新 extras_ 内容
            auto text = extras_buffer_->get_text();
            extras_.clear();
            
            // 按行分割
            size_t pos = 0;
            std::string line;
            std::string text_str = text;
            while ((pos = text_str.find('\n')) != std::string::npos) {
                line = text_str.substr(0, pos);
                if (!line.empty()) {
                    extras_.push_back(line);
                }
                text_str.erase(0, pos + 1);
            }
            if (!text_str.empty()) {
                extras_.push_back(text_str);
            }
            
            if (change_callback_) change_callback_();
        }
    });
}



void TypeTab::setupButton(Gtk::Button& button, const Glib::ustring& icon_name,
                         const Glib::ustring& label) {
    auto box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 2);
    
    auto image = Gtk::make_managed<Gtk::Image>();
    image->set_from_icon_name(icon_name);
    box->append(*image);
    
    auto label_widget = Gtk::make_managed<Gtk::Label>(label);
    box->append(*label_widget);

    button.set_child(*box);
}

void TypeTab::updateTypes(
    const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) {
    
    updating_ = true;

    // 清除现有数据
    list_store_->clear();

    // 创建父类型模型
    auto parent_model = Gtk::StringList::create();
    parent_model->append("");  // 空选项表示无父类型

    // 添加所有类型
    for (const auto& type : types) {
        auto iter = list_store_->append();
        (*iter)[columns_.col_name] = Glib::ustring(type->getName());
        (*iter)[columns_.col_base_type] = Glib::ustring(type->getBaseType());
        (*iter)[columns_.col_type] = type;

        // 添加到父类型下拉框
        parent_model->append(type->getName());
    }

    parent_combo_->set_model(parent_model);

    updating_ = false;
}

void TypeTab::updateTypeInList(const std::shared_ptr<core::model::AdvancedType>& type) {
    for (auto row : list_store_->children()) {
        std::shared_ptr<core::model::AdvancedType> current_type;
        current_type = row[columns_.col_type];
        if (current_type == type) {
            row[columns_.col_name] = Glib::ustring(type->getName());
            row[columns_.col_base_type] = Glib::ustring(type->getBaseType());
            break;
        }
    }
}

std::shared_ptr<core::model::AdvancedType> TypeTab::getSelectedType() const {
    auto selected = selection_->get_selected();
    if (selected) {
        return (*selected)[columns_.col_type];
    }
    return nullptr;
}

std::vector<std::shared_ptr<core::model::AdvancedType>> TypeTab::getTypes() const {
    std::vector<std::shared_ptr<core::model::AdvancedType>> types;
    for (const auto& row : list_store_->children()) {
        types.push_back(row[columns_.col_type]);
    }
    return types;
}

void TypeTab::setChangeCallback(std::function<void()> callback) {
    change_callback_ = std::move(callback);
}

void TypeTab::onSelectionChanged() {
    auto type = getSelectedType();
    edit_box_.set_sensitive(type != nullptr);

    if (type) {
        updating_ = true;
        updateEditPanel(type);
        updating_ = false;
    }
}

void TypeTab::updateEditPanel(const std::shared_ptr<core::model::AdvancedType>& type) {
    name_entry_->set_text(type->getName());
    
    // 更新基础类型下拉框
    auto base_type_model = std::dynamic_pointer_cast<Gtk::StringList>(base_type_combo_->get_model());
    if (base_type_model) {
        for (guint i = 0; i < base_type_model->get_n_items(); ++i) {
            if (base_type_model->get_string(i) == Glib::ustring(type->getBaseType())) {
                base_type_combo_->set_selected(i);
                break;
            }
        }
    }

    // 更新父类型下拉框
    auto parent_model = std::dynamic_pointer_cast<Gtk::StringList>(parent_combo_->get_model());
    if (parent_model) {
        for (guint i = 0; i < parent_model->get_n_items(); ++i) {
            if (parent_model->get_string(i) == Glib::ustring(type->getParentType())) {
                parent_combo_->set_selected(i);
                break;
            }
        }
    }

    // 更新约束
    // 1. 更新正则表达式
    regex_entry_->set_text(type->getRegexPattern());
    
    // 2. 更新数值范围
    if (type->getMinValue().has_value()) {
        min_spin_->set_value(type->getMinValue().value());
    } else {
        min_spin_->set_value(0);
    }
    
    if (type->getMaxValue().has_value()) {
        max_spin_->set_value(type->getMaxValue().value());
    } else {
        max_spin_->set_value(0);
    }
    
    // 3. 更新枚举值列表
    // 首先检查是否已经创建了枚举值列表的模型
    auto enum_selection = std::dynamic_pointer_cast<Gtk::SingleSelection>(enum_list_->get_model());
    if (!enum_selection) {
        // 创建枚举值列表模型
        auto enum_store = Gtk::StringList::create();
        enum_selection = Gtk::SingleSelection::create(enum_store);
        enum_list_->set_model(enum_selection);
        
        // 添加值列
        auto factory = Gtk::SignalListItemFactory::create();
        factory->signal_setup().connect([](const Glib::RefPtr<Gtk::ListItem>& list_item) {
            auto label = Gtk::make_managed<Gtk::Label>();
            list_item->set_child(*label);
        });
        
        factory->signal_bind().connect([](const Glib::RefPtr<Gtk::ListItem>& list_item) {
            auto label = dynamic_cast<Gtk::Label*>(list_item->get_child());
            if (label) {
                auto item = std::dynamic_pointer_cast<Gtk::StringObject>(list_item->get_item());
                if (item) {
                    label->set_text(item->get_string());
                }
            }
        });
        
        auto column = Gtk::ColumnViewColumn::create("Value", factory);
        column->set_resizable(true);
        column->set_expand(true);
        enum_list_->append_column(column);
    }
    
    // 更新枚举值列表内容
    auto enum_store = std::dynamic_pointer_cast<Gtk::StringList>(
        std::dynamic_pointer_cast<Gtk::SingleSelection>(enum_list_->get_model())->get_model());
    
    if (enum_store) {
        enum_store->splice(0,enum_store->get_n_items(),{});
        for (const auto& value : type->getEnumValues()) {
            enum_store->append(value);
        }
    }
    
    // 4. 更新额外属性列表
    
    // 更新额外属性列表内容
    // auto attr_store = std::dynamic_pointer_cast<Glib::RefPtr<Gtk::ListStore>>(
    //     std::dynamic_pointer_cast<Gtk::SingleSelection>(attr_list_->get_model())->get_model());
    attr_store_->clear();
    // 添加到列表中
    for (const auto& [key, value] : type->getExtraAttrs()) {
        auto iter = attr_store_->append();
        (*iter)[attr_columns_.col_key] = key;
        (*iter)[attr_columns_.col_value] = value;
    }
}

void TypeTab::onAddTypeClicked() {
    auto new_type = std::make_shared<core::model::AdvancedType>("new_type", "string");
    
    auto iter = list_store_->append();
    (*iter)[columns_.col_name] = new_type->getName();
    (*iter)[columns_.col_base_type] = new_type->getBaseType();
    (*iter)[columns_.col_type] = new_type;

    // 添加到父类型下拉框
    auto model = std::dynamic_pointer_cast<Gtk::StringList>(parent_combo_->get_model());
    if (model) {
        model->append(new_type->getName());
    }

    if (change_callback_) change_callback_();
}

void TypeTab::onDeleteTypeClicked() {
    auto selected = selection_->get_selected();
    if (!selected) return;

    std::shared_ptr<core::model::AdvancedType> type;
    type = (*selected)[columns_.col_type];
    list_store_->erase(selected);

    // 从父类型下拉框中移除
    auto model = std::dynamic_pointer_cast<Gtk::StringList>(parent_combo_->get_model());
    if (model) {
        for (guint i = 0; i < model->get_n_items(); ++i) {
            if (model->get_string(i) == Glib::ustring(type->getName())) {
                model->remove(i);
                break;
            }
        }
    }

    if (change_callback_) change_callback_();
}

void TypeTab::onAddEnumClicked() {
    // TODO: 实现添加枚举值
}

void TypeTab::onDeleteEnumClicked() {
    // TODO: 实现删除枚举值
}

void TypeTab::onAddAttrClicked() {
    // TODO: 实现添加属性
}

void TypeTab::onDeleteAttrClicked() {
    // TODO: 实现删除属性
}

std::vector<std::string> TypeTab::getExtras() const {
    return extras_;
}

void TypeTab::setExtras(const std::vector<std::string>& extras) {
    updating_ = true;
    extras_ = extras;
    
    // 更新文本视图
    if (extras_buffer_) {
        std::string text;
        for (const auto& line : extras_) {
            text += line + "\n";
        }
        extras_buffer_->set_text(text);
    }
    updating_ = false;
}

} // namespace gtk
} // namespace ui
} // namespace xsd_editor
