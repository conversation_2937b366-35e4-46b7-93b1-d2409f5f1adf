#pragma once

#include <gtkmm.h>
#include "../../core/model/item.h"
#include "../../core/model/type.h"
#include "../../core/model/device.h"
#include <memory>
#include <functional>

namespace xsd_editor {
namespace ui {
namespace gtk {

// 辅助函数：递归添加设备到树
void addDeviceToTree(
    const Gtk::TreeModel::iterator& parent,
    const std::shared_ptr<core::model::Device>& device,
    Gtk::TreeModelColumn<Glib::ustring>& col_name,
    Gtk::TreeModelColumn<std::shared_ptr<core::model::Device>>& col_device,
    Glib::RefPtr<Gtk::TreeStore>& tree_store);

class ElementTab : public Gtk::Box {
public:
    ElementTab(class MainWindow *parent);

    // 更新元素树
    void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items);
    void updateElements(const std::vector<std::shared_ptr<core::model::Item>>& items) { updateItems(items); }
    
    // 获取当前元素列表
    std::vector<std::shared_ptr<core::model::Item>> getElements() const;
    
    // 获取选中的元素
    std::shared_ptr<core::model::Item> getSelectedItem() const { return getSelectedElement(); }
    
    // 设置变更回调
    void setChangeCallback(std::function<void()> callback);

    // 设置类型和设备列表
    void setTypesList(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types);
    void setDevicesList(const std::vector<std::shared_ptr<core::model::Device>>& devices);

private:
    // UI元素
    class MainWindow *parent_;
    Gtk::Box edit_box_;
    Gtk::Box button_box_;
    Gtk::ScrolledWindow tree_scroll_;
    Gtk::TreeView tree_view_;
    Gtk::Grid edit_grid_;
    
    // 编辑控件
    Gtk::Entry name_entry_;
    Gtk::Entry type_entry_;
    Gtk::ComboBoxText adv_type_combo_;
    Gtk::Entry file_path_entry_;
    Gtk::Entry devices_entry_;
    Gtk::ComboBoxText devices_combo_;
    Gtk::Entry versions_entry_;
    Gtk::CheckButton is_list_check_;
    
    // 属性编辑控件
    Gtk::TreeView* attr_tree_view_ = nullptr;
    std::shared_ptr<Gtk::Button> add_attr_button_;
    std::shared_ptr<Gtk::Button> del_attr_button_;
    Glib::RefPtr<Gtk::ListStore> attr_store_;
    
    // 属性列表模型
    class AttrColumns : public Gtk::TreeModel::ColumnRecord {
    public:
        AttrColumns() {
            add(col_key);
            add(col_value);
        }

        Gtk::TreeModelColumn<Glib::ustring> col_key;
        Gtk::TreeModelColumn<Glib::ustring> col_value;
    };
    
    AttrColumns attr_columns_;
    
    // 按钮控件
    Gtk::Button add_button_;
    Gtk::Button delete_button_;
    Gtk::Button move_up_button_;
    Gtk::Button move_down_button_;
    Gtk::Button level_up_button_;
    Gtk::Button level_down_button_;

    // 树模型列
    class ModelColumns : public Gtk::TreeModel::ColumnRecord {
    public:
        ModelColumns() {
            add(col_name);
            add(col_type);
            add(col_item);
        }

        Gtk::TreeModelColumn<Glib::ustring> col_name;
        Gtk::TreeModelColumn<Glib::ustring> col_type;
        Gtk::TreeModelColumn<std::shared_ptr<core::model::Item>> col_item;
    };

    ModelColumns columns_;
    Glib::RefPtr<Gtk::TreeStore> tree_store_;
    
    // 变更回调
    std::function<void()> change_callback_;
    
    // 标记是否正在更新UI
    bool updating_ = false;
    
    // 类型和设备列表
    std::vector<std::shared_ptr<core::model::AdvancedType>> types_list_;
    std::vector<std::shared_ptr<core::model::Device>> devices_list_;

    // 私有辅助方法
    void showError(const Glib::ustring& message);

    void createTreeView();
    void createEditPanel();
    void createAttributesPanel();
    void createButtonBox();
    void setupButton(Gtk::Button& button,
                    const Glib::ustring& icon_name,
                    const Glib::ustring& label);
    
    void buildElementTree(const Gtk::TreeModel::iterator& parent,
                         const std::shared_ptr<core::model::Item>& item);
    void updateElementInTree(const std::shared_ptr<core::model::Item>& item);
    Gtk::TreePath findElementPath(const std::shared_ptr<core::model::Item>& item);
    bool findElementPathRecursive(const Gtk::TreeModel::iterator& parent,
                                const std::shared_ptr<core::model::Item>& item,
                                Gtk::TreePath& path);
    
    std::shared_ptr<core::model::Item> getSelectedElement() const;
    void updateEditPanel(const std::shared_ptr<core::model::Item>& item);
    void updateAttributesList(const std::shared_ptr<core::model::Item>& item);
    
    // 事件处理
    void onSelectionChanged();
    void onAddClicked();
    void onDeleteClicked();
    void onMoveUpClicked();
    void onMoveDownClicked();
    void onLevelUpClicked();
    void onLevelDownClicked();
    void onAddAttrClicked();
    void onDeleteAttrClicked();
    
    // 填充下拉框
    void fillAdvTypeCombo();
    void fillDevicesCombo();
    void addDeviceToCombo(const std::shared_ptr<core::model::Device>& device, int level);
    
    
    bool isValidMove(const std::shared_ptr<core::model::Item>& source,
                    const std::shared_ptr<core::model::Item>& target,
                    bool as_sibling) const;
};

} // namespace gtk
} // namespace ui
} // namespace xsd_editor