#ifndef XSD_EDITOR_UI_GTK_DEVICE_TAB_H
#define XSD_EDITOR_UI_GTK_DEVICE_TAB_H

#include <gtkmm.h>
#include <memory>
#include "../../core/model/device.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

// 前向声明
class MainWindow;

/**
 * @brief 设备页面类
 * 
 * 实现设备家族的树状显示和编辑功能，
 * 包括设备的添加、删除、移动等操作。
 */
class DeviceTab : public Gtk::Box {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    DeviceTab(MainWindow *parent);

    /**
     * @brief 析构函数
     */
    virtual ~DeviceTab() = default;

    /**
     * @brief 更新设备树
     * @param devices 设备列表
     */
    void updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices);

    /**
     * @brief 获取当前选中的设备
     * @return 选中的设备指针
     */
    std::shared_ptr<core::model::Device> getSelectedDevice() const;

    /**
     * @brief 设置设备变更回调
     * @param callback 回调函数
     */
    void setChangeCallback(std::function<void()> callback);

    std::vector<std::shared_ptr<core::model::Device>> getDevices() const;
private:
    /**
     * @brief 创建树状视图
     */
    void createTreeView();

    /**
     * @brief 创建编辑面板
     */
    void createEditPanel();

    /**
     * @brief 创建按钮组
     */
    void createButtonBox();

    /**
     * @brief 更新编辑面板
     * @param device 要编辑的设备
     */
    void updateEditPanel(const std::shared_ptr<core::model::Device>& device);

    /**
     * @brief 处理设备选择变更
     */
    void onSelectionChanged();

    /**
     * @brief 处理添加按钮点击
     */
    void onAddClicked();

    /**
     * @brief 处理删除按钮点击
     */
    void onDeleteClicked();

    /**
     * @brief 处理上移按钮点击
     */
    void onMoveUpClicked();

    /**
     * @brief 处理下移按钮点击
     */
    void onMoveDownClicked();

    /**
     * @brief 处理层级上移按钮点击
     */
    void onLevelUpClicked();

    /**
     * @brief 处理层级下移按钮点击
     */
    void onLevelDownClicked();

    /**
     * @brief 创建按钮
     */
    void setupButton(Gtk::Button& button, const Glib::ustring& icon_name,
                    const Glib::ustring& label);

    /**
     * @brief 添加树列
     */
    void addTreeColumn(const Glib::ustring& title,
                      const Gtk::TreeModelColumn<Glib::ustring>& column);

    /**
     * @brief 更新设备在树中的显示
     */
    void updateDeviceInTree(const std::shared_ptr<core::model::Device>& device);

    /**
     * @brief 递归查找设备路径
     */
    bool findDevicePathRecursive(const Gtk::TreeModel::iterator& parent,
                                const std::shared_ptr<core::model::Device>& device,
                                Gtk::TreePath& path);

    // 树状视图相关
    class ModelColumns : public Gtk::TreeModel::ColumnRecord {
    public:
        ModelColumns() {
            add(col_name);
            add(col_device);
        }

        Gtk::TreeModelColumn<Glib::ustring> col_name;  // 设备名称
        Gtk::TreeModelColumn<std::shared_ptr<core::model::Device>> col_device;  // 设备对象
    };

    // 成员变量按照构造函数初始化顺序声明
    ModelColumns columns_;
    MainWindow *parent_;             // 父窗口指针
    Gtk::Box edit_box_;
    Gtk::Box button_box_;
    Gtk::ScrolledWindow tree_scroll_;
    Gtk::TreeView tree_view_;
    Gtk::Grid edit_grid_;
    Gtk::Entry name_entry_;          // 名称输入框
    Gtk::Entry description_entry_;   // 描述输入框
    Gtk::Button add_button_;         // 添加按钮
    Gtk::Button delete_button_;      // 删除按钮
    Gtk::Button move_up_button_;     // 上移按钮
    Gtk::Button move_down_button_;   // 下移按钮
    Gtk::Button level_up_button_;    // 层级上移按钮
    Gtk::Button level_down_button_;  // 层级下移按钮
    
    Glib::RefPtr<Gtk::TreeStore> tree_store_;
    Glib::RefPtr<Gtk::TreeSelection> selection_;
    std::function<void()> change_callback_;  // 变更回调函数
    bool updating_ = false;  // 是否正在更新UI（防止循环触发）

    /**
     * @brief 递归构建设备树
     * 
     * @param parent_iter 父节点迭代器
     * @param device 当前设备
     */
    void buildDeviceTree(const Gtk::TreeModel::iterator& parent_iter,
                        const std::shared_ptr<core::model::Device>& device);

    /**
     * @brief 获取设备在树中的路径
     * 
     * @param device 设备对象
     * @return 树路径，如果未找到返回空路径
     */
    Gtk::TreePath findDevicePath(const std::shared_ptr<core::model::Device>& device);

    /**
     * @brief 检查移动操作是否有效
     * 
     * @param source 源设备
     * @param target 目标设备
     * @param as_sibling 是否作为兄弟节点（false表示作为子节点）
     * @return 是否允许移动
     */
    bool isValidMove(const std::shared_ptr<core::model::Device>& source,
                    const std::shared_ptr<core::model::Device>& target,
                    bool as_sibling) const;
};

} // namespace gtk
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_GTK_DEVICE_TAB_H