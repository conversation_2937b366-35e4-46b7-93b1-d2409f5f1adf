#include "main_window.h"
#include <iostream>
#include <future>
#include "../../utils/error.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

MainWindow::MainWindow(const Glib::RefPtr<Gtk::Application>& app)
    : main_box_(Gtk::Orientation::VERTICAL)
    , toolbar_box_(Gtk::Orientation::HORIZONTAL)
{
    // 设置窗口属性
    window_.set_title("XSD Editor");
    window_.set_default_size(1024, 768);
    window_.set_child(main_box_);
    
    // 将窗口与应用程序关联
    window_.set_application(app);

    // 创建界面元素
    createActions();
    createMenuBar();
    createToolBar();
    createStatusBar();

    // 创建标签页
    createTypeTab();
    createDeviceTab();
    createElementTab();
    notebook_.append_page(*element_tab_, "Elements");
    notebook_.append_page(*type_tab_, "Types");
    notebook_.append_page(*device_tab_, "Devices");

    // 设置标签页切换事件
    notebook_.signal_switch_page().connect(
        [this](Gtk::Widget* page, unsigned int) {
            onTabChanged(page);
        });

    // 添加到主布局
    main_box_.append(toolbar_box_);  // 工具栏包含菜单栏
    main_box_.append(notebook_);
    main_box_.append(status_label_);

    // 创建快捷键
    createAccelerators();
    
    // 注意：不在构造函数中显示窗口
    // 窗口应该在应用程序的activate信号处理程序中显示
}

void MainWindow::show() {
    window_.show();
}

void MainWindow::hide() {
    window_.hide();
}

void MainWindow::updateItems(
    const std::vector<std::shared_ptr<core::model::Item>>& items) {
    
    if (element_tab_) {
        element_tab_->updateItems(items);
    }
}

void MainWindow::updateTypes(
    const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) {
    
    if (type_tab_) {
        type_tab_->updateTypes(types);
    }
    
    // 同步更新 ElementTab 中的类型列表
    if (element_tab_) {
        element_tab_->setTypesList(types);
    }
}

void MainWindow::updateExtras(const std::vector<std::string>& extras) {
    if (type_tab_) {
        type_tab_->setExtras(extras);
    }
}

std::vector<std::string> MainWindow::getTypeExtras() const {
    if (type_tab_) {
        return type_tab_->getExtras();
    }
    return {};
}

void MainWindow::updateDevices(
    const std::vector<std::shared_ptr<core::model::Device>>& devices) {
    
    if (device_tab_) {
        device_tab_->updateDevices(devices);
    }
    
    // 同步更新 ElementTab 中的设备列表
    if (element_tab_) {
        element_tab_->setDevicesList(devices);
    }
}

void MainWindow::showError(const std::string& message) {
    auto dialog = new Gtk::MessageDialog(window_, message,
        false, Gtk::MessageType::ERROR, Gtk::ButtonsType::OK, true);
    dialog->set_modal(true);
    dialog->set_transient_for(window_);
    dialog->signal_response().connect([dialog](int) -> void {
        dialog->hide();
        delete dialog;
    });
    dialog->show();
}

void MainWindow::showInfo(const std::string& message) {
    // 每次都创建一个新的独立对话框
    auto dialog = new Gtk::MessageDialog(
        window_,
        message,
        false,
        Gtk::MessageType::INFO,
        Gtk::ButtonsType::OK,
        true
    );
    
    dialog->set_modal(true);
    dialog->set_transient_for(window_);
    
    // 对话框关闭时自动删除
    dialog->signal_response().connect([dialog](int) {
        dialog->hide();
        delete dialog;
    });
    
    dialog->show();
}

bool MainWindow::showConfirmDialog(const std::string& message) {
    // 创建一个 promise 来存储结果
    std::promise<bool> response_promise;
    auto response_future = response_promise.get_future();
    
    // 创建对话框
    auto dialog = new Gtk::MessageDialog(
        window_,
        message,
        false,
        Gtk::MessageType::QUESTION,
        Gtk::ButtonsType::YES_NO,
        true
    );
    
    dialog->set_modal(true);
    dialog->set_transient_for(window_);
    
    // 设置响应处理
    dialog->signal_response().connect([dialog, &response_promise](int response) {
        bool result = (response == Gtk::ResponseType::YES);
        response_promise.set_value(result);
        dialog->hide();
        delete dialog;
    });
    
    // 显示对话框
    dialog->show();
    
    // 等待并返回结果
    return response_future.get();
}

size_t MainWindow::addEventListener(UIEventListener listener) {
    if (!listener) {
        throw std::invalid_argument("Null listener");
    }

    size_t id = next_listener_id_++;
    listeners_[id] = std::move(listener);
    return id;
}

void MainWindow::removeEventListener(size_t id) {
    listeners_.erase(id);
}

void MainWindow::triggerEvent(UIEventType type, const std::string& data) {
    auto listeners = listeners_;
    for (const auto& [id, listener] : listeners) {
        try {
            listener(type, data);
        }
        catch (const std::exception& e) {
            std::cerr << "Error in UI event listener: " << e.what() << std::endl;
        }
    }
}

void MainWindow::createActions() {
    action_group_ = Gio::SimpleActionGroup::create();

    // 文件菜单动作
    addMenuAction("file.save", "_Save", "<Control>s");
    addMenuAction("file.quit", "_Quit", "<Control>q");

    // 编辑菜单动作
    // addMenuAction("edit.undo", "_Undo", "<Control>z");
    // addMenuAction("edit.redo", "_Redo", "<Control>y");
    // addMenuAction("preferences", "_Preferences");

    // 帮助菜单动作
    addMenuAction("about", "_About");

    window_.insert_action_group("win", action_group_);
}

void MainWindow::addMenuAction(const Glib::ustring& name,
                             const Glib::ustring& label,
                             const Glib::ustring& accel) {
    (void)label;
    auto action = Gio::SimpleAction::create(name);
    auto handler = [this, name](const Glib::VariantBase& /*variant*/) {
        auto action_name = name.substr(name.find('.') + 1);
        if (name.find("file") == 0) {
            onFileMenuAction(action_name);
        }
        else if (name.find("edit") == 0) {
            onEditMenuAction(action_name);
        }
        else if (name.find("help") == 0) {
            onHelpMenuAction(action_name);
        }
    };
    action->signal_activate().connect(handler);
    action_group_->add_action(action);

    if (!accel.empty()) {
        window_.get_application()->set_accel_for_action(
            Glib::ustring::compose("win.%1", name), accel);
    }
}

void MainWindow::createMenuBar() {
    menu_bar_ = Gio::Menu::create();

    // 文件菜单
    auto file_menu = Gio::Menu::create();
    file_menu->append("_New", "win.new");
    file_menu->append("_Open", "win.open");
    file_menu->append("_Save", "win.save");
    file_menu->append("Save _All", "win.save_all");
    file_menu->append("_Quit", "win.quit");
    menu_bar_->append_submenu("_File", file_menu);

    // 编辑菜单
    auto edit_menu = Gio::Menu::create();
    edit_menu->append("_Undo", "win.undo");
    edit_menu->append("_Redo", "win.redo");
    edit_menu->append("_Preferences", "win.preferences");
    menu_bar_->append_submenu("_Edit", edit_menu);

    // 帮助菜单
    auto help_menu = Gio::Menu::create();
    help_menu->append("_About", "win.about");
    menu_bar_->append_submenu("_Help", help_menu);

    window_.set_show_menubar(true);
}

void MainWindow::createToolBar() {
    toolbar_box_.set_spacing(5);
    toolbar_box_.set_margin(5);

    // 添加工具栏按钮
    // addToolButton("document-new", "New", "win.new");
    // addToolButton("document-open", "Open", "win.open");
    addToolButton("document-save", "Save", "win.file.save");

    // auto separator = std::make_shared<Gtk::Separator>(Gtk::Orientation::VERTICAL);
    // toolbar_box_.append(*separator);

    // addToolButton("edit-undo", "Undo", "win.undo");
    // addToolButton("edit-redo", "Redo", "win.redo");
}

void MainWindow::addToolButton(const Glib::ustring& icon_name,
                             const Glib::ustring& label,
                             const Glib::ustring& action_name) {
    auto button = std::make_shared<Gtk::Button>();
    auto box = std::make_shared<Gtk::Box>(Gtk::Orientation::VERTICAL, 2);
    
    auto image = std::make_shared<Gtk::Image>();
    image->set_from_icon_name(icon_name);
    box->append(*image);
    
    auto label_widget = std::make_shared<Gtk::Label>(label);
    box->append(*label_widget);

    button->set_child(*box);
    button->set_action_name(action_name);
    
    toolbar_box_.append(*button);
}

void MainWindow::createStatusBar() {
    status_label_.set_halign(Gtk::Align::START);
    status_label_.set_margin(5);
    status_label_.set_text("Ready");
}

void MainWindow::createElementTab() {
    element_tab_ = std::make_unique<ElementTab>(this);
    element_tab_->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::ITEM_CHANGED, "");
    });
    
    // 设置类型列表和设备列表
    if (type_tab_) {
        element_tab_->setTypesList(type_tab_->getTypes());
    }
    
    if (device_tab_) {
        element_tab_->setDevicesList(device_tab_->getDevices());
    }
    
}

void MainWindow::createTypeTab() {
    type_tab_ = std::make_unique<TypeTab>();
    type_tab_->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::TYPE_CHANGED, "");
    });
}

void MainWindow::createDeviceTab() {
    device_tab_ = std::make_unique<DeviceTab>(this);
    device_tab_->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::DEVICE_CHANGED, "");
    });
}

void MainWindow::createAccelerators() {
    // 在 gtkmm-4.0 中，加速键通过 Application 的 set_accel_for_action 设置
    // 已在 addMenuAction 中处理
}

void MainWindow::onFileMenuAction(const Glib::ustring& action) {
    std::string action_str = action.raw();
    if (action_str == "new") {
        triggerEvent(UIEventType::NEW_ITEM_REQUESTED, "");
    }
    else if (action_str == "open") {
        triggerEvent(UIEventType::LOAD_REQUESTED, "");
    }
    else if (action_str == "save" || action_str == "save_all") {
        triggerEvent(UIEventType::SAVE_REQUESTED, "");
    }
    else if (action_str == "quit") {
        if (!is_modified_ || showConfirmDialog("Save changes before quitting?")) {
            window_.close();
        }
    }
}

void MainWindow::onEditMenuAction(const Glib::ustring& action) {
    // TODO: 实现编辑菜单动作
    (void) action;
}

void MainWindow::onHelpMenuAction(const Glib::ustring& action) {
    if (action == "about") {
        auto dialog = Gtk::AboutDialog();
        dialog.set_transient_for(window_);
        dialog.set_modal(true);
        dialog.set_program_name("XSD Editor");
        dialog.set_version("1.0.0");
        dialog.set_copyright("Copyright © 2025");
        dialog.set_comments("A configuration definition editor");
        dialog.signal_hide().connect([&dialog]() {
            dialog.set_visible(false);
        });
        dialog.show();
    }
}

void MainWindow::onTabChanged(Gtk::Widget* page) {
    // 更新状态栏显示当前页面
    if (page == element_tab_.get()) {
        status_label_.set_text("Editing Elements");
    }
    else if (page == type_tab_.get()) {
        status_label_.set_text("Editing Types");
    }
    else if (page == device_tab_.get()) {
        status_label_.set_text("Editing Devices");
    }
}

std::shared_ptr<core::model::Item> MainWindow::getSelectedItem() const {
    if (element_tab_) {
        return element_tab_->getSelectedItem();
    }
    return nullptr;
}

std::vector<std::shared_ptr<core::model::AdvancedType>> MainWindow::getTypes() const {
    if (type_tab_) {
        // 由于 getTypes() 是私有的，我们需要通过其他方式获取类型列表
        return type_tab_->getTypes();
    }
    return {};
}

std::vector<std::shared_ptr<core::model::Device>> MainWindow::getDevices() const {
    if (device_tab_) {
        return device_tab_->getDevices();
    }
    return {};
}

Gtk::ApplicationWindow& MainWindow::getWindow() {
    return window_;
}

} // namespace gtk
} // namespace ui
} // namespace xsd_editor
