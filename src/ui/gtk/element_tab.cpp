#include "element_tab.h"
#include "core/model/item.h"
#include "glibmm/ustring.h"
#include "ui/gtk/main_window.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

ElementTab::ElementTab(MainWindow *parent)
    : Gtk::Box(Gtk::Orientation::HORIZONTAL)
    , parent_(parent)
    , edit_box_(Gtk::Orientation::VERTICAL)
    , button_box_(Gtk::Orientation::HORIZONTAL)
    , tree_scroll_()
    , tree_view_()
    , edit_grid_()
    , name_entry_()
    , type_entry_()
    , adv_type_combo_()
    , file_path_entry_()
    , devices_entry_()
    , devices_combo_()
    , versions_entry_()
    , is_list_check_("Is List")
    , add_button_()
    , delete_button_()
    , move_up_button_()
    , move_down_button_()
    , level_up_button_()
    , level_down_button_()
{
    set_spacing(5);
    set_margin(5);

    // 创建界面元素
    createTreeView();
    createEditPanel();
    createAttributesPanel();
    createButtonBox();

    // 左侧布局：树状视图 + 按钮组
    auto left_box = Gtk::Box(Gtk::Orientation::VERTICAL);
    left_box.set_spacing(5);
    left_box.append(tree_scroll_);
    left_box.append(button_box_);

    // 添加到主布局
    append(left_box);
    append(edit_box_);

    // 初始状态：禁用编辑面板和按钮
    edit_box_.set_sensitive(false);
    button_box_.set_sensitive(false);
}

void ElementTab::createTreeView() {
    // 创建树存储
    tree_store_ = Gtk::TreeStore::create(columns_);

    // 创建树视图
    tree_view_.set_model(tree_store_);
    
    // 添加列
    auto type_cell = Gtk::make_managed<Gtk::CellRendererText>();
    auto type_column = Gtk::make_managed<Gtk::TreeViewColumn>("Type", *type_cell);
    type_column->add_attribute(type_cell->property_text(), columns_.col_type);
    tree_view_.append_column(*type_column);

    auto name_cell = Gtk::make_managed<Gtk::CellRendererText>();
    auto name_column = Gtk::make_managed<Gtk::TreeViewColumn>("Name", *name_cell);
    name_column->add_attribute(name_cell->property_text(), columns_.col_name);
    tree_view_.append_column(*name_column);

    // 设置选择模式
    auto selection = tree_view_.get_selection();
    selection->set_mode(Gtk::SelectionMode::SINGLE);
    selection->signal_changed().connect(
        sigc::mem_fun(*this, &ElementTab::onSelectionChanged));

    // 配置滚动窗口
    tree_scroll_.set_policy(Gtk::PolicyType::AUTOMATIC, Gtk::PolicyType::AUTOMATIC);
    tree_scroll_.set_has_frame(true);
    tree_scroll_.set_child(tree_view_);
    tree_scroll_.set_vexpand(true);
}
void ElementTab::createEditPanel() {
    edit_box_.set_spacing(5);
    edit_box_.set_margin(5);

    // 编辑网格
    edit_grid_.set_column_spacing(10);
    edit_grid_.set_row_spacing(5);

    int row = 0;

    // 名称
    auto name_label = Gtk::Label("Name:");
    edit_grid_.attach(name_label, 0, row, 1, 1);
    edit_grid_.attach(name_entry_, 1, row++, 1, 1);

    // 类型
    auto type_label = Gtk::Label("Type:");
    edit_grid_.attach(type_label, 0, row, 1, 1);
    edit_grid_.attach(type_entry_, 1, row++, 1, 1);

    // 高级类型
    auto adv_type_label = Gtk::Label("Advanced Type:");
    edit_grid_.attach(adv_type_label, 0, row, 1, 1);
    
    // 设置高级类型下拉框
    adv_type_combo_.set_hexpand(true);
    adv_type_combo_.set_tooltip_text("Select from Type list");
    fillAdvTypeCombo();
    edit_grid_.attach(adv_type_combo_, 1, row++, 1, 1);

    // 文件路径（仅module元素可见）
    auto file_path_label = Gtk::Label("File Loc:");
    edit_grid_.attach(file_path_label, 0, row, 1, 1);
    edit_grid_.attach(file_path_entry_, 1, row++, 1, 1);

    // 设备家族
    auto devices_label = Gtk::Label("Devices:");
    edit_grid_.attach(devices_label, 0, row, 1, 1);
    
    // 设置设备下拉框
    devices_combo_.set_hexpand(true);
    devices_combo_.set_tooltip_text("Select from Device list");
    fillDevicesCombo();
    edit_grid_.attach(devices_entry_, 1, row, 1, 1);
    edit_grid_.attach(devices_combo_, 2, row++, 1, 1);

    // auto devices_add_label = Gtk::Label("Add Device:");
    // edit_grid_.attach(devices_add_label, 0, row, 1, 1);

    // 版本范围
    auto versions_label = Gtk::Label("Versions:");
    edit_grid_.attach(versions_label, 0, row, 1, 1);
    edit_grid_.attach(versions_entry_, 1, row++, 1, 1);

    // 列表类型
    edit_grid_.attach(is_list_check_, 1, row++, 1, 1);

    edit_box_.append(edit_grid_);

    // 绑定事件
    name_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setName(name_entry_.get_text());
            updateElementInTree(element);
            if (change_callback_) change_callback_();
        }
    });

    type_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setType(type_entry_.get_text());
            updateElementInTree(element);
            if (change_callback_) change_callback_();
        }
    });

    adv_type_combo_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setAdvType(adv_type_combo_.get_active_text());
            if (change_callback_) change_callback_();
        }
    });

    file_path_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            if (element->getLevel() == 0) { // 仅module元素
                element->setFileLoc(file_path_entry_.get_text());
                if (change_callback_) change_callback_();
            }
        }
    });

    devices_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setAffectDevicesString(devices_entry_.get_text());
            if (change_callback_) change_callback_();
        }
    });

    devices_combo_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            updating_ = true;
            if (devices_entry_.get_text().empty())
            {
                devices_entry_.set_text(devices_combo_.get_active_text());
            }else{
                devices_entry_.set_text(devices_entry_.get_text() + ',' + devices_combo_.get_active_text());
            }
            element->setAffectDevicesString(devices_entry_.get_text());
            devices_combo_.set_active(-1);
            updating_ = false;
            if (change_callback_) change_callback_();
        }
    });

    versions_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setVersionsString(versions_entry_.get_text());
            if (change_callback_) change_callback_();
        }
    });

    is_list_check_.signal_toggled().connect([this]() {
        if (!updating_ && getSelectedElement()) {
            auto element = getSelectedElement();
            element->setIsList(is_list_check_.get_active());
            updateElementInTree(element);
            if (change_callback_) change_callback_();
        }
    });
}

void ElementTab::createAttributesPanel() {
    // 创建属性框架
    auto attr_frame = Gtk::make_managed<Gtk::Frame>("Attributes");
    auto attr_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::VERTICAL, 5);
    attr_box->set_margin(5);
    
    // 创建属性列表存储
    attr_store_ = Gtk::ListStore::create(attr_columns_);
    
    // 创建属性列表视图
    auto attr_tree_view = Gtk::make_managed<Gtk::TreeView>();
    attr_tree_view_ = attr_tree_view;  // 保存TreeView引用
    attr_tree_view->set_model(attr_store_);
    attr_tree_view->append_column("Key", attr_columns_.col_key);
    attr_tree_view->append_column("Value", attr_columns_.col_value);
    
    // 设置列可编辑
    auto key_renderer = dynamic_cast<Gtk::CellRendererText*>(
        attr_tree_view->get_column(0)->get_first_cell());
    if (key_renderer) {
        key_renderer->property_editable() = true;
        key_renderer->signal_edited().connect(
            [this](const Glib::ustring& path, const Glib::ustring& new_text) {
                auto iter = attr_store_->get_iter(path);
                if (iter && !updating_ && getSelectedElement()) {
                    auto element = getSelectedElement();
                    Glib::ustring old_key = (*iter)[attr_columns_.col_key];
                    Glib::ustring value = (*iter)[attr_columns_.col_value];
                    
                    // 移除旧属性，添加新属性
                    element->removeAttribute(old_key);
                    element->setAttribute(std::string(new_text), value);
                    
                    // 更新列表
                    (*iter)[attr_columns_.col_key] = new_text;
                    
                    if (change_callback_) change_callback_();
                }
            });
    }
    
    auto value_renderer = dynamic_cast<Gtk::CellRendererText*>(
        attr_tree_view->get_column(1)->get_first_cell());
    if (value_renderer) {
        value_renderer->property_editable() = true;
        value_renderer->signal_edited().connect(
            [this](const Glib::ustring& path, const Glib::ustring& new_text) {
                auto iter = attr_store_->get_iter(path);
                if (iter && !updating_ && getSelectedElement()) {
                    auto element = getSelectedElement();
                    Glib::ustring key = (*iter)[attr_columns_.col_key];
                    
                    // 更新属性值
                    element->setAttribute(key, std::string(new_text));
                    
                    // 更新列表
                    (*iter)[attr_columns_.col_value] = new_text;
                    
                    if (change_callback_) change_callback_();
                }
            });
    }
    
    // 创建滚动窗口
    auto attr_scroll = Gtk::make_managed<Gtk::ScrolledWindow>();
    attr_scroll->set_policy(Gtk::PolicyType::AUTOMATIC, Gtk::PolicyType::AUTOMATIC);
    attr_scroll->set_has_frame(true);
    attr_scroll->set_child(*attr_tree_view);
    attr_scroll->set_vexpand(true);
    attr_box->append(*attr_scroll);
    
    // 创建按钮盒子
    auto attr_button_box = Gtk::make_managed<Gtk::Box>(Gtk::Orientation::HORIZONTAL, 5);
    
    // 创建添加和删除按钮
    add_attr_button_ = std::make_shared<Gtk::Button>();
    del_attr_button_ = std::make_shared<Gtk::Button>();
    setupButton(*add_attr_button_, "list-add", "Add");
    setupButton(*del_attr_button_, "list-remove", "Delete");
    
    // 设置提示文本
    add_attr_button_->set_tooltip_text("Add new attribute");
    del_attr_button_->set_tooltip_text("Delete selected attribute");
    
    // 绑定事件
    add_attr_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onAddAttrClicked));
    del_attr_button_->signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onDeleteAttrClicked));
    
    // 添加按钮到按钮盒子
    attr_button_box->append(*add_attr_button_);
    attr_button_box->append(*del_attr_button_);
    attr_box->append(*attr_button_box);
    
    // 设置框架内容
    attr_frame->set_child(*attr_box);
    
    // 添加到编辑盒子
    edit_box_.append(*attr_frame);
}

void ElementTab::createButtonBox() {
    button_box_.set_spacing(5);
    button_box_.set_margin(5);

    // 设置按钮
    setupButton(add_button_, "list-add", "Add");
    setupButton(delete_button_, "list-remove", "Delete");
    setupButton(move_up_button_, "go-up", "Move Up");
    setupButton(move_down_button_, "go-down", "Move Down");
    setupButton(level_up_button_, "go-previous", "Level Up");
    setupButton(level_down_button_, "go-next", "Level Down");

    // 设置提示文本
    add_button_.set_tooltip_text("Add new element");
    delete_button_.set_tooltip_text("Delete selected element");
    move_up_button_.set_tooltip_text("Move element up");
    move_down_button_.set_tooltip_text("Move element down");
    level_up_button_.set_tooltip_text("Decrease element level");
    level_down_button_.set_tooltip_text("Increase element level");

    // 绑定事件
    add_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onAddClicked));
    delete_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onDeleteClicked));
    move_up_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onMoveUpClicked));
    move_down_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onMoveDownClicked));
    level_up_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onLevelUpClicked));
    level_down_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &ElementTab::onLevelDownClicked));

    // 添加到按钮盒子
    button_box_.append(add_button_);
    button_box_.append(delete_button_);
    button_box_.append(move_up_button_);
    button_box_.append(move_down_button_);
    button_box_.append(level_up_button_);
    button_box_.append(level_down_button_);
}

void ElementTab::setupButton(Gtk::Button& button,
                           const Glib::ustring& icon_name,
                           const Glib::ustring& label) {
    auto box = Gtk::Box(Gtk::Orientation::VERTICAL);
    box.set_spacing(2);
    
    auto image = Gtk::Image();
    image.set_from_icon_name(icon_name);
    box.append(image);
    
    auto label_widget = Gtk::Label(label);
    box.append(label_widget);

    button.set_child(box);
}

void ElementTab::updateItems(
    const std::vector<std::shared_ptr<core::model::Item>>& items) {
    
    updating_ = true;

    // 清除现有数据
    tree_store_->clear();

    // 添加所有元素
    for (const auto& item : items) {
        buildElementTree(tree_store_->children().end(), item);
    }

    updating_ = false;
}

void ElementTab::buildElementTree(
    const Gtk::TreeModel::iterator& parent,
    const std::shared_ptr<core::model::Item>& item) {
    
    Gtk::TreeModel::iterator iter;
    if (parent != tree_store_->children().end()) {
        iter = tree_store_->append(parent->children());
    } else {
        iter = tree_store_->append();
    }

    (*iter)[columns_.col_name] = item->getName();
    (*iter)[columns_.col_type] = item->getType();
    (*iter)[columns_.col_item] = item;

    // 递归添加子元素
    for (const auto& child : item->getChildren()) {
        buildElementTree(iter, child);
    }
}

void ElementTab::updateElementInTree(
    const std::shared_ptr<core::model::Item>& item) {
    auto path = findElementPath(item);
    if (!path.empty()) {
        auto iter = tree_store_->get_iter(path);
        if (iter) {
            (*iter)[columns_.col_name] = item->getName();
            (*iter)[columns_.col_type] = item->getType();
        }
    }
}

Gtk::TreePath ElementTab::findElementPath(
    const std::shared_ptr<core::model::Item>& item) {
    
    Gtk::TreePath path;
    for (auto iter = tree_store_->children().begin(); 
         iter != tree_store_->children().end(); ++iter) {
        if (findElementPathRecursive(iter, item, path)) {
            return path;
        }
    }
    return path;
}

bool ElementTab::findElementPathRecursive(
    const Gtk::TreeModel::iterator& parent,
    const std::shared_ptr<core::model::Item>& item,
    Gtk::TreePath& path) {
    
    auto row_item = parent->get_value(columns_.col_item);
    if (row_item == item) {
        path = tree_store_->get_path(parent);
        return true;
    }

    for (auto iter = parent->children().begin(); 
         iter != parent->children().end(); ++iter) {
        if (findElementPathRecursive(iter, item, path)) {
            return true;
        }
    }

    return false;
}

std::shared_ptr<core::model::Item> ElementTab::getSelectedElement() const {
    auto selection = tree_view_.get_selection();
    if (!selection) return nullptr;

    auto iter = selection->get_selected();
    if (!iter) return nullptr;

    return (*iter)[columns_.col_item];
}

void ElementTab::setChangeCallback(std::function<void()> callback) {
    change_callback_ = std::move(callback);
}

void ElementTab::onSelectionChanged() {
    auto element = getSelectedElement();
    
    // 更新编辑面板和按钮状态
    edit_box_.set_sensitive(element != nullptr);
    button_box_.set_sensitive(element != nullptr);

    if (element) {
        updating_ = true;
        updateEditPanel(element);
        
        // 根据元素层级更新按钮状态
        level_up_button_.set_sensitive(element->getLevel() > 0);
        level_down_button_.set_sensitive(element->getLevel() < 2);
        
        // 文件路径输入框仅对module元素可见
        file_path_entry_.set_visible(element->getLevel() == 0);
        
        updating_ = false;
    }
}

void ElementTab::updateEditPanel(
    const std::shared_ptr<core::model::Item>& item) {
    name_entry_.set_text(item->getName());
    type_entry_.set_text(item->getType());
    // 设置高级类型下拉框
    Glib::ustring adv_type = item->getAdvType();
    bool found = false;
    
    // 在gtkmm-4.0中，我们需要使用不同的方法来遍历ComboBoxText中的项
    int n_items = 0;
    Glib::ustring current_text;
    // 尝试获取每个索引的文本，直到失败
    while (true) {
        adv_type_combo_.set_active(n_items);
        current_text = adv_type_combo_.get_active_text();
        if (current_text.empty() && n_items > 0) {
            break; // 没有更多项了
        }
        
        if (current_text == adv_type) {
            found = true;
            break;
        }
        
        n_items++;
    }
    
    if (!found && !adv_type.empty()) {
        adv_type_combo_.append(adv_type);
        adv_type_combo_.set_active_text(adv_type);
    }
    
    file_path_entry_.set_text(item->getFileLoc());
    
    devices_entry_.set_text(item->getAffectDevicesString());

    // 设置设备下拉框
    devices_combo_.set_active(-1);
    
    versions_entry_.set_text(item->getVersionsString());
    
    is_list_check_.set_active(item->isList());
    
    // 更新属性列表
    updateAttributesList(item);
}
void ElementTab::updateAttributesList(const std::shared_ptr<core::model::Item>& item)
{
    // 清除现有属性
    attr_store_->clear();
    
    if (!item) return;
    
    // 获取元素的所有属性
    const auto& attributes = item->getAttributes();
    
    // 添加到列表中
    for (const auto& [key, value] : attributes) {
        auto iter = attr_store_->append();
        (*iter)[attr_columns_.col_key] = key;
        (*iter)[attr_columns_.col_value] = value;
    }
}

void ElementTab::onAddAttrClicked() {
    auto item = getSelectedElement();
    if (!item) return;
    
    // 添加新的空属性
    const std::string new_key = "new_attr";
    const std::string new_value = "";
    
    // 确保键名唯一
    std::string unique_key = new_key;
    int counter = 1;
    while (!item->getAttribute(unique_key).empty()) {
        unique_key = new_key + std::to_string(counter++);
    }
    
    // 设置属性
    item->setAttribute(unique_key, new_value);
    
    // 更新属性列表
    updateAttributesList(item);
    
    if (change_callback_) change_callback_();
}

void ElementTab::onDeleteAttrClicked() {
    auto item = getSelectedElement();
    if (!item || !attr_tree_view_) return;
    
    // 获取选中的属性
    auto selection = attr_tree_view_->get_selection();
    if (!selection) return;
    
    auto iter = selection->get_selected();
    if (!iter) return;
    
    // 获取属性键
    Glib::ustring key = (*iter)[attr_columns_.col_key];
    
    // 从元素中移除属性
    item->removeAttribute(key);
    
    // 从列表中移除
    attr_store_->erase(iter);
    
    if (change_callback_) change_callback_();
}


void ElementTab::onAddClicked() {
    auto parent = getSelectedElement();
    auto new_item = core::model::Item::create("new_item","option",0);
    
    if (parent) {
        auto selection = tree_view_.get_selection();
        Gtk::TreeModel::iterator iter;
        if (selection) {
            iter = selection->get_selected();
        }
        new_item->setLevel(parent->getLevel() + 1);
        if (new_item->getLevel() > 2) {
            new_item->setLevel(2);
            parent = parent->getParent().lock();
            iter = iter->parent();
        }
        parent->addChild(new_item);
        if (iter) {
            buildElementTree(iter, new_item);
        }
    } else {
        new_item->setLevel(0);
        new_item->setType("module");
        buildElementTree(tree_store_->children().end(), new_item);
    }

    if (change_callback_) change_callback_();
}

void ElementTab::onDeleteClicked() {
    auto item = getSelectedElement();
    if (!item) return;

    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    // 如果有父元素，从父元素中移除
    if (auto parent = item->getParent().lock()) {
        parent->removeChild(item);
    }

    tree_store_->erase(iter);

    if (change_callback_) change_callback_();
}

void ElementTab::onMoveUpClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    auto prev = iter;
    if (prev && prev != tree_store_->children().begin()) {
        --prev;
        
        // 检查是否在同一层级（同一父节点下）
        auto iter_parent = iter->parent();
        auto prev_parent = prev->parent();
        
        // 如果一个有父节点而另一个没有，或者父节点不同，则它们不在同一层级
        if ((iter_parent && !prev_parent) || (!iter_parent && prev_parent) ||
            (iter_parent && prev_parent && iter_parent != prev_parent)) {
            parent_->showError("不能移动不同层级的元素");
            return;
        }
        
        auto src = (*iter)[columns_.col_item];
        auto target = (*prev)[columns_.col_item];

        core::model::Item::swapItem(src, target);
        
        if (isValidMove(src, target, true)) {
            tree_store_->iter_swap(iter, prev);
            if (change_callback_) change_callback_();
        }
    }
}

void ElementTab::onMoveDownClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    auto next = iter;
    ++next;
    if (next && next != tree_store_->children().end()) {
        // 检查是否在同一层级（同一父节点下）
        auto iter_parent = iter->parent();
        auto next_parent = next->parent();
        

        // 如果一个有父节点而另一个没有，或者父节点不同，则它们不在同一层级
        if ((iter_parent && !next_parent) || (!iter_parent && next_parent) ||
            (iter_parent && next_parent && iter_parent != next_parent)) {
            parent_->showError("不能移动不同层级的元素");
            return;
        }
        
        auto src = (*iter)[columns_.col_item];
        auto target = (*next)[columns_.col_item];
        
        core::model::Item::swapItem(src, target);
        
        if (isValidMove(src, target, true)) {
            tree_store_->iter_swap(iter, next);
            if (change_callback_) change_callback_();
        }
    }else{
        parent_->showError("不能移动不同层级的元素");
        
    }
}

void ElementTab::onLevelUpClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;
    auto iter = selection->get_selected();
    if (!iter) return;
    

    auto item = getSelectedElement();
    auto parent_item = item->getParent().lock();
    if (!item || item->getLevel() == 0 || !parent_item)
    {
        parent_->showError("无法移动");
        return;
    }

    auto parent_iter = iter->parent();

    parent_item->removeChild(item);
    tree_store_->erase(iter);

    item->setLevelR(item->getLevel() - 1);
    parent_item = parent_item->getParent().lock();
    if (parent_item)
        parent_item->addChild(item);
    auto parent_parent_iter = parent_iter->parent();
    if (!parent_parent_iter || parent_iter == parent_parent_iter)
        buildElementTree(tree_store_->children().end(), item);
    else
        buildElementTree(parent_parent_iter, item);
    if (change_callback_) change_callback_();
}

void ElementTab::onLevelDownClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;
    auto iter = selection->get_selected();
    if (!iter) return;

    auto item = getSelectedElement();
    if (!item || item->getLevel() >= 2)
    {
        parent_->showError("无法移动");
        return;
    }

    // 获取当前元素的前一个兄弟元素作为新的父元素
    auto prev = iter;
    if (prev && prev != tree_store_->children().begin()) {
        --prev;

        std::shared_ptr<core::model::Item> prev_item = (*prev)[columns_.col_item];
        if (!prev_item || prev_item->getLevel() != item->getLevel()) {
            parent_->showError("需要同级元素作为新的父元素");
            return;
        }
        
        // 从当前父元素中移除
        if (auto parent_item = item->getParent().lock()) {
            parent_item->removeChild(item);
        }
        tree_store_->erase(iter);
        
        // 设置新的层级并添加到新父元素
        item->setLevelR(item->getLevel() + 1);
        prev_item->addChild(item);
        
        // 重建树结构
        buildElementTree(prev, item);
        
        if (change_callback_) change_callback_();
    } else {
        parent_->showError("需要前面有同级元素作为新的父元素");
    }
}

bool ElementTab::isValidMove(
    const std::shared_ptr<core::model::Item>& source,
    const std::shared_ptr<core::model::Item>& target,
    bool as_sibling) const {
    
    if (!source || !target) return false;

    // 检查是否会形成循环引用
    if (!as_sibling) {
        // 检查target是否是source的子孙节点
        auto current = target;
        while (auto parent = current->getParent().lock()) {
            if (parent == source) {
                return false;
            }
            current = parent;
        }
    }

    return true;
}

std::vector<std::shared_ptr<core::model::Item>> ElementTab::getElements() const {
    std::vector<std::shared_ptr<core::model::Item>> items;
    for (const auto& row : tree_store_->children()) {
        items.push_back(row[columns_.col_item]);
    }
    return items;
}

void ElementTab::setTypesList(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) {
    types_list_ = types;
    fillAdvTypeCombo();
}

void ElementTab::setDevicesList(const std::vector<std::shared_ptr<core::model::Device>>& devices) {
    devices_list_ = devices;
    fillDevicesCombo();
}

void ElementTab::fillAdvTypeCombo() {
    // 清空下拉框
    adv_type_combo_.remove_all();
    
    // 添加空选项
    adv_type_combo_.append("");
    
    // 添加所有类型
    for (const auto& type : types_list_) {
        adv_type_combo_.append(type->getName());
    }
}

void ElementTab::fillDevicesCombo() {
    // 清空下拉框
    devices_combo_.remove_all();
    
    // 添加空选项
    devices_combo_.append("");
    
    // 添加所有设备
    for (const auto& device : devices_list_) {
        devices_combo_.append(device->getName());
        
        // 递归添加子设备
        addDeviceToCombo(device, 1);
    }
}

// 辅助方法：递归添加设备到下拉框
void ElementTab::addDeviceToCombo(const std::shared_ptr<core::model::Device>& device, int level) {
    // 为子设备添加缩进
    Glib::ustring indent;
    for (int i = 0; i < level; ++i) {
        indent += "  ";
    }
    
    // 添加子设备
    for (const auto& child : device->getChildren()) {
        devices_combo_.append(indent + child->getName());
        
        // 递归添加子设备的子设备
        addDeviceToCombo(child, level + 1);
    }
}

} // namespace gtk
} // namespace ui
} // namespace xsd_editor