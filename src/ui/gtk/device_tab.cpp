#include "device_tab.h"
#include <iostream>
#include "../../utils/error.h"
#include "ui/gtk/main_window.h"

namespace xsd_editor {
namespace ui {
namespace gtk {

DeviceTab::DeviceTab(MainWindow *parent)
    : Gtk::Box(Gtk::Orientation::HORIZONTAL)
    , parent_(parent)
    , edit_box_(Gtk::Orientation::VERTICAL)
    , button_box_(Gtk::Orientation::HORIZONTAL)
    , tree_scroll_()
    , tree_view_()
    , edit_grid_()
    , name_entry_()
    , description_entry_()
    , add_button_()
    , delete_button_()
    , move_up_button_()
    , move_down_button_()
    , level_up_button_()
    , level_down_button_()
{
    set_spacing(5);
    set_margin(5);

    // 创建界面元素
    createTreeView();
    createEditPanel();
    createButtonBox();

    // 左侧布局：树状视图 + 按钮组
    auto left_box = Gtk::Box(Gtk::Orientation::VERTICAL);
    left_box.set_spacing(5);
    left_box.append(tree_scroll_);
    left_box.append(button_box_);

    // 添加到主布局
    append(left_box);
    append(edit_box_);

    // 初始状态：禁用编辑面板和按钮
    edit_box_.set_sensitive(false);
    button_box_.set_sensitive(false);
}

void DeviceTab::createTreeView() {
    // 创建树存储
    tree_store_ = Gtk::TreeStore::create(columns_);

    // 创建树视图
    tree_view_.set_model(tree_store_);
    
    // 添加列
    auto cell = Gtk::make_managed<Gtk::CellRendererText>();
    auto column = Gtk::make_managed<Gtk::TreeViewColumn>("Name", *cell);
    column->add_attribute(cell->property_text(), columns_.col_name);
    tree_view_.append_column(*column);

    // 设置选择模式
    auto selection = tree_view_.get_selection();
    selection->set_mode(Gtk::SelectionMode::SINGLE);
    selection->signal_changed().connect(
        sigc::mem_fun(*this, &DeviceTab::onSelectionChanged));

    // 配置滚动窗口
    tree_scroll_.set_policy(Gtk::PolicyType::AUTOMATIC, Gtk::PolicyType::AUTOMATIC);
    tree_scroll_.set_has_frame(true);
    tree_scroll_.set_child(tree_view_);
    tree_scroll_.set_vexpand(true);
}

void DeviceTab::addTreeColumn(const Glib::ustring& title,
                            const Gtk::TreeModelColumn<Glib::ustring>& column) {
    auto cell = Gtk::make_managed<Gtk::CellRendererText>();
    auto tree_column = Gtk::make_managed<Gtk::TreeViewColumn>(title, *cell);
    tree_column->add_attribute(cell->property_text(), column);
    tree_column->set_resizable(true);
    tree_column->set_expand(true);
    tree_view_.append_column(*tree_column);
}

void DeviceTab::createEditPanel() {
    edit_box_.set_spacing(5);
    edit_box_.set_margin(5);

    // 编辑网格
    edit_grid_.set_column_spacing(10);
    edit_grid_.set_row_spacing(5);

    int row = 0;

    // 设备名称
    auto name_label = Gtk::Label("Name:");
    edit_grid_.attach(name_label, 0, row, 1, 1);
    edit_grid_.attach(name_entry_, 1, row++, 1, 1);

    // 设备描述
    auto desc_label = Gtk::Label("Description:");
    edit_grid_.attach(desc_label, 0, row, 1, 1);
    edit_grid_.attach(description_entry_, 1, row++, 1, 1);

    edit_box_.append(edit_grid_);

    // 绑定事件
    name_entry_.signal_changed().connect([this]() {
        if (!updating_ && getSelectedDevice()) {
            auto device = getSelectedDevice();
            device->setName(name_entry_.get_text());
            updateDeviceInTree(device);
            if (change_callback_) change_callback_();
        }
    });
}

void DeviceTab::createButtonBox() {
    button_box_.set_spacing(5);
    button_box_.set_margin(5);

    // 设置按钮
    setupButton(add_button_, "list-add", "Add");
    setupButton(delete_button_, "list-remove", "Delete");
    setupButton(move_up_button_, "go-up", "Move Up");
    setupButton(move_down_button_, "go-down", "Move Down");
    setupButton(level_up_button_, "go-previous", "Level Up");
    setupButton(level_down_button_, "go-next", "Level Down");

    // 设置提示文本
    add_button_.set_tooltip_text("Add new device");
    delete_button_.set_tooltip_text("Delete selected device");
    move_up_button_.set_tooltip_text("Move device up");
    move_down_button_.set_tooltip_text("Move device down");
    level_up_button_.set_tooltip_text("Decrease device level");
    level_down_button_.set_tooltip_text("Increase device level");

    // 绑定事件
    add_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onAddClicked));
    delete_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onDeleteClicked));
    move_up_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onMoveUpClicked));
    move_down_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onMoveDownClicked));
    level_up_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onLevelUpClicked));
    level_down_button_.signal_clicked().connect(
        sigc::mem_fun(*this, &DeviceTab::onLevelDownClicked));

    // 添加到按钮盒子
    button_box_.append(add_button_);
    button_box_.append(delete_button_);
    button_box_.append(move_up_button_);
    button_box_.append(move_down_button_);
    button_box_.append(level_up_button_);
    button_box_.append(level_down_button_);
}

void DeviceTab::setupButton(Gtk::Button& button,
                          const Glib::ustring& icon_name,
                          const Glib::ustring& label) {
    auto box = Gtk::Box(Gtk::Orientation::VERTICAL);
    box.set_spacing(2);
    
    auto image = Gtk::Image();
    image.set_from_icon_name(icon_name);
    box.append(image);
    
    auto label_widget = Gtk::Label(label);
    box.append(label_widget);

    button.set_child(box);
}

void DeviceTab::updateDevices(
    const std::vector<std::shared_ptr<core::model::Device>>& devices) {
    
    updating_ = true;

    // 清除现有数据
    tree_store_->clear();

    // 添加所有设备
    for (const auto& device : devices) {
        buildDeviceTree(tree_store_->children().end(), device);
    }

    updating_ = false;
}

void DeviceTab::buildDeviceTree(
    const Gtk::TreeModel::iterator& parent,
    const std::shared_ptr<core::model::Device>& device) {
    
    Gtk::TreeModel::iterator iter;
    if (parent != tree_store_->children().end()) {
        iter = tree_store_->append(parent->children());
    } else {
        iter = tree_store_->append();
    }

    (*iter)[columns_.col_name] = device->getName();
    (*iter)[columns_.col_device] = device;

    // 递归添加子设备
    for (const auto& child : device->getChildren()) {
        buildDeviceTree(iter, child);
    }
}

void DeviceTab::updateDeviceInTree(const std::shared_ptr<core::model::Device>& device) {
    auto path = findDevicePath(device);
    if (!path.empty()) {
        auto iter = tree_store_->get_iter(path);
        if (iter) {
            (*iter)[columns_.col_name] = device->getName();
        }
    }
}

Gtk::TreePath DeviceTab::findDevicePath(
    const std::shared_ptr<core::model::Device>& device) {
    
    Gtk::TreePath path;
    for (auto iter = tree_store_->children().begin(); iter != tree_store_->children().end(); ++iter) {
        if (findDevicePathRecursive(iter, device, path)) {
            return path;
        }
    }
    return path;
}

bool DeviceTab::findDevicePathRecursive(
    const Gtk::TreeModel::iterator& parent,
    const std::shared_ptr<core::model::Device>& device,
    Gtk::TreePath& path) {
    
    auto row_device = parent->get_value(columns_.col_device);
    if (row_device == device) {
        path = tree_store_->get_path(parent);
        return true;
    }

    for (auto iter = parent->children().begin(); iter != parent->children().end(); ++iter) {
        if (findDevicePathRecursive(iter, device, path)) {
            return true;
        }
    }

    return false;
}

std::shared_ptr<core::model::Device> DeviceTab::getSelectedDevice() const {
    auto selection = tree_view_.get_selection();
    if (!selection) return nullptr;

    auto iter = selection->get_selected();
    if (!iter) return nullptr;

    return (*iter)[columns_.col_device];
}

void DeviceTab::setChangeCallback(std::function<void()> callback) {
    change_callback_ = std::move(callback);
}

void DeviceTab::onSelectionChanged() {
    auto device = getSelectedDevice();
    
    // 更新编辑面板和按钮状态
    edit_box_.set_sensitive(device != nullptr);
    button_box_.set_sensitive(device != nullptr);

    if (device) {
        updating_ = true;
        updateEditPanel(device);
        updating_ = false;
    }
}

void DeviceTab::updateEditPanel(const std::shared_ptr<core::model::Device>& device) {
    name_entry_.set_text(device->getName());
    // TODO: 添加描述字段到Device类
}

void DeviceTab::onAddClicked() {
    auto parent = getSelectedDevice();
    auto new_device = std::make_shared<core::model::Device>("new_device");

    if (parent) {
        parent->addChild(new_device);
        auto selection = tree_view_.get_selection();
        if (selection) {
            auto iter = selection->get_selected();
            if (iter) {
                buildDeviceTree(iter, new_device);
            }
        }
    } else {
        buildDeviceTree(tree_store_->children().end(), new_device);
    }

    if (change_callback_) change_callback_();
}

void DeviceTab::onDeleteClicked() {
    auto device = getSelectedDevice();
    if (!device) return;

    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    // 如果有父设备，从父设备中移除
    if (auto parent = device->getParent().lock()) {
        parent->removeChild(device);
    }

    tree_store_->erase(iter);

    if (change_callback_) change_callback_();
}

void DeviceTab::onMoveUpClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    auto prev = iter;
    if (prev && prev != tree_store_->children().begin()) {
        --prev;
        
        // 检查是否在同一层级（同一父节点下）
        auto iter_parent = iter->parent();
        auto prev_parent = prev->parent();
        
        // 如果一个有父节点而另一个没有，或者父节点不同，则它们不在同一层级
        if ((iter_parent && !prev_parent) || (!iter_parent && prev_parent) ||
            (iter_parent && prev_parent && iter_parent != prev_parent)) {
            parent_->showError("不能移动不同层级的设备");
            return;
        }
        
        auto src = (*iter)[columns_.col_device];
        auto target = (*prev)[columns_.col_device];
        
        if (isValidMove(src, target, true)) {
            tree_store_->iter_swap(iter, prev);
            if (change_callback_) change_callback_();
        }
    }
}

void DeviceTab::onMoveDownClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;

    auto iter = selection->get_selected();
    if (!iter) return;

    auto next = iter;
    ++next;
    if (next && next != tree_store_->children().end()) {
        // 检查是否在同一层级（同一父节点下）
        auto iter_parent = iter->parent();
        auto next_parent = next->parent();
        
        // 如果一个有父节点而另一个没有，或者父节点不同，则它们不在同一层级
        if ((iter_parent && !next_parent) || (!iter_parent && next_parent) ||
            (iter_parent && next_parent && iter_parent != next_parent)) {
            parent_->showError("不能移动不同层级的设备");
            return;
        }
        
        auto src = (*iter)[columns_.col_device];
        auto target = (*next)[columns_.col_device];
        
        if (isValidMove(src, target, true)) {
            tree_store_->iter_swap(iter, next);
            if (change_callback_) change_callback_();
        }
    } else {
        parent_->showError("不能移动不同层级的设备");
    }
}

void DeviceTab::onLevelUpClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;
    auto iter = selection->get_selected();
    if (!iter) return;
    
    auto device = getSelectedDevice();
    auto parent_device = device->getParent().lock();
    if (!device || !parent_device)
    {
        parent_->showError("无法移动");
        return;
    }

    auto parent_iter = iter->parent();

    parent_device->removeChild(device);
    tree_store_->erase(iter);

    // 将设备移到上一级
    auto parent_parent_device = parent_device->getParent().lock();
    if (parent_parent_device)
        parent_parent_device->addChild(device);
    
    auto parent_parent_iter = parent_iter->parent();
    if (!parent_parent_iter || parent_iter == parent_parent_iter)
        buildDeviceTree(tree_store_->children().end(), device);
    else
        buildDeviceTree(parent_parent_iter, device);
    
    if (change_callback_) change_callback_();
}

void DeviceTab::onLevelDownClicked() {
    auto selection = tree_view_.get_selection();
    if (!selection) return;
    auto iter = selection->get_selected();
    if (!iter) return;

    auto device = getSelectedDevice();
    if (!device)
    {
        parent_->showError("无法移动");
        return;
    }

    // 获取当前元素的前一个兄弟元素作为新的父元素
    auto prev = iter;
    if (prev && prev != tree_store_->children().begin()) {
        --prev;

        std::shared_ptr<core::model::Device> prev_device = (*prev)[columns_.col_device];
        if (!prev_device) {
            parent_->showError("需要同级设备作为新的父元素");
            return;
        }
        
        // 检查是否在同一层级（同一父节点下）
        auto iter_parent = iter->parent();
        auto prev_parent = prev->parent();
        
        // 如果一个有父节点而另一个没有，或者父节点不同，则它们不在同一层级
        if ((iter_parent && !prev_parent) || (!iter_parent && prev_parent) ||
            (iter_parent && prev_parent && iter_parent != prev_parent)) {
            parent_->showError("需要同级设备作为新的父元素");
            return;
        }
        
        // 从当前父元素中移除
        if (auto parent_device = device->getParent().lock()) {
            parent_device->removeChild(device);
        }
        tree_store_->erase(iter);
        
        // 添加到新父元素
        prev_device->addChild(device);
        
        // 重建树结构
        buildDeviceTree(prev, device);
        
        if (change_callback_) change_callback_();
    } else {
        parent_->showError("需要前面有同级设备作为新的父元素");
    }
}


bool DeviceTab::isValidMove(
    const std::shared_ptr<core::model::Device>& source,
    const std::shared_ptr<core::model::Device>& target,
    bool as_sibling) const {
    
    if (!source || !target) return false;

    // 检查是否会形成循环引用
    if (!as_sibling && target->isDescendantOf(source)) {
        return false;
    }

    return true;
}

std::vector<std::shared_ptr<core::model::Device>> DeviceTab::getDevices() const {
    std::vector<std::shared_ptr<core::model::Device>> devices;
    for (const auto& row : tree_store_->children()) {
        devices.push_back(row[columns_.col_device]);
    }
    return devices;
}
} // namespace gtk
} // namespace ui
} // namespace xsd_editor