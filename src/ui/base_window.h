#ifndef XSD_EDITOR_UI_BASE_WINDOW_H
#define XSD_EDITOR_UI_BASE_WINDOW_H

#include <string>
#include <memory>
#include <functional>
#include "../core/model/item.h"
#include "../core/model/type.h"
#include "../core/model/device.h"

namespace xsd_editor {
namespace ui {

/**
 * @brief 界面事件类型
 */
enum class UIEventType {
    ITEM_SELECTED,          // 元素被选中
    ITEM_CHANGED,          // 元素被修改
    TYPE_SELECTED,         // 类型被选中
    TYPE_CHANGED,          // 类型被修改
    DEVICE_SELECTED,       // 设备被选中
    DEVICE_CHANGED,        // 设备被修改
    SAVE_REQUESTED,        // 请求保存
    LOAD_REQUESTED,        // 请求加载
    NEW_ITEM_REQUESTED,    // 请求新建元素
    DELETE_REQUESTED,      // 请求删除
    MOVE_UP_REQUESTED,     // 请求上移
    MOVE_DOWN_REQUESTED,   // 请求下移
    LEVEL_UP_REQUESTED,    // 请求升级
    LEVEL_DOWN_REQUESTED,  // 请求降级
};

/**
 * @brief 界面事件监听器类型
 */
using UIEventListener = std::function<void(UIEventType, const std::string&)>;

/**
 * @brief 界面基类
 * 
 * 定义所有界面实现必须支持的功能。
 */
class BaseWindow {
public:
    /**
     * @brief 默认构造函数
     */
    BaseWindow() = default;

    /**
     * @brief 虚析构函数
     */
    virtual ~BaseWindow() = default;

    /**
     * @brief 显示窗口
     */
    virtual void show() = 0;

    /**
     * @brief 隐藏窗口
     */
    virtual void hide() = 0;

    /**
     * @brief 更新元素树
     * @param items 元素列表
     */
    virtual void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items) = 0;

    /**
     * @brief 更新类型列表
     * @param types 类型列表
     */
    virtual void updateTypes(
        const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) = 0;
        
    /**
     * @brief 更新extras字段
     * @param extras extras字段的内容
     */
    virtual void updateExtras(const std::vector<std::string>& extras) = 0;

    /**
     * @brief 更新设备树
     * @param devices 设备列表
     */
    virtual void updateDevices(
        const std::vector<std::shared_ptr<core::model::Device>>& devices) = 0;

    /**
     * @brief 显示错误消息
     * @param message 错误消息
     */
    virtual void showError(const std::string& message) = 0;

    /**
     * @brief 显示信息消息
     * @param message 信息消息
     */
    virtual void showInfo(const std::string& message) = 0;

    /**
     * @brief 显示确认对话框
     * 
     * @param message 确认消息
     * @return 用户选择结果
     */
    virtual bool showConfirmDialog(const std::string& message) = 0;

    /**
     * @brief 添加事件监听器
     * 
     * @param listener 监听器函数
     * @return 监听器ID
     */
    virtual size_t addEventListener(UIEventListener listener) = 0;

    /**
     * @brief 移除事件监听器
     * @param id 监听器ID
     */
    virtual void removeEventListener(size_t id) = 0;

protected:
    /**
     * @brief 触发界面事件
     * 
     * @param type 事件类型
     * @param data 事件数据
     */
    virtual void triggerEvent(UIEventType type, const std::string& data = "") = 0;

    /**
     * @brief 创建菜单栏
     */
    virtual void createMenuBar() = 0;

    /**
     * @brief 创建工具栏
     */
    virtual void createToolBar() = 0;

    /**
     * @brief 创建状态栏
     */
    virtual void createStatusBar() = 0;

    /**
     * @brief 创建元素页面
     */
    virtual void createElementTab() = 0;

    /**
     * @brief 创建类型页面
     */
    virtual void createTypeTab() = 0;

    /**
     * @brief 创建设备页面
     */
    virtual void createDeviceTab() = 0;
};

} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_BASE_WINDOW_H