#ifndef XSD_EDITOR_UI_WIN32_DEVICE_TAB_H
#define XSD_EDITOR_UI_WIN32_DEVICE_TAB_H

#include <windows.h>
#include <commctrl.h>
#include <vector>
#include <map>
#include <memory>
#include <string>
#include "win32_tab.h"
#include "../../core/model/device.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

// 前向声明
class Win32MainWindow;

/**
 * @brief 设备标签页
 * 
 * 实现设备的树状显示和编辑功能。
 */
class DeviceTab : public Win32Tab {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口句柄
     * @param main_window 主窗口指针
     */
    DeviceTab(HWND parent, Win32MainWindow* main_window);
    
    /**
     * @brief 析构函数
     */
    virtual ~DeviceTab();
    
    // 实现Win32Tab接口
    void show(bool visible) override;
    void resize(const RECT& rect) override;
    void handleNotify(LPNMHDR nmhdr) override;
    void handleCommand(WPARAM wparam, LPARAM lparam) override;
    
    /**
     * @brief 更新设备树
     * @param devices 设备列表
     */
    void updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices);
    
    /**
     * @brief 获取当前设备列表
     * @return 设备列表
     */
    std::vector<std::shared_ptr<core::model::Device>> getDevices() const;
    
    /**
     * @brief 获取选中的设备
     * @return 选中的设备
     */
    std::shared_ptr<core::model::Device> getSelectedDevice() const;
    
private:
    /**
     * @brief 创建树视图
     */
    void createTreeView();
    
    /**
     * @brief 创建编辑面板
     */
    void createEditPanel();
    
    /**
     * @brief 创建按钮面板
     */
    void createButtonPanel();
    
    /**
     * @brief 更新编辑面板
     * @param device 要编辑的设备
     */
    void updateEditPanel(const std::shared_ptr<core::model::Device>& device);
    
    /**
     * @brief 递归构建设备树
     * @param parent_item 父节点句柄
     * @param device 设备
     */
    void buildDeviceTree(HTREEITEM parent_item, const std::shared_ptr<core::model::Device>& device);
    
    /**
     * @brief 查找设备在树中的位置
     * @param device 设备
     * @return 树节点句柄
     */
    HTREEITEM findDeviceInTree(const std::shared_ptr<core::model::Device>& device);
    
    /**
     * @brief 递归查找设备
     * @param parent_item 父节点句柄
     * @param device 要查找的设备
     * @return 树节点句柄
     */
    HTREEITEM findDeviceRecursive(HTREEITEM parent_item, const std::shared_ptr<core::model::Device>& device);
    
    /**
     * @brief 处理树节点选择变更
     */
    void onTreeSelectionChanged();
    
    /**
     * @brief 处理添加按钮点击
     */
    void onAddClicked();
    
    /**
     * @brief 处理删除按钮点击
     */
    void onDeleteClicked();
    
    /**
     * @brief 处理上移按钮点击
     */
    void onMoveUpClicked();
    
    /**
     * @brief 处理下移按钮点击
     */
    void onMoveDownClicked();
    
    /**
     * @brief 处理层级上移按钮点击
     */
    void onLevelUpClicked();
    
    /**
     * @brief 处理层级下移按钮点击
     */
    void onLevelDownClicked();
    
    /**
     * @brief 检查移动操作是否有效
     * @param source 源设备
     * @param target 目标设备
     * @param as_sibling 是否作为兄弟节点
     * @return 是否有效
     */
    bool isValidMove(const std::shared_ptr<core::model::Device>& source,
                    const std::shared_ptr<core::model::Device>& target,
                    bool as_sibling) const;
    
    // 成员变量
    Win32MainWindow* main_window_;  // 主窗口指针
    
    // 控件句柄
    HWND tree_view_;        // 树视图
    HWND edit_panel_;       // 编辑面板
    HWND button_panel_;     // 按钮面板
    
    // 编辑控件
    HWND name_edit_;        // 名称编辑框
    HWND description_edit_; // 描述编辑框
    
    // 按钮控件
    HWND add_button_;       // 添加按钮
    HWND delete_button_;    // 删除按钮
    HWND move_up_button_;   // 上移按钮
    HWND move_down_button_; // 下移按钮
    HWND level_up_button_;  // 层级上移按钮
    HWND level_down_button_;// 层级下移按钮
    
    // 数据
    std::vector<std::shared_ptr<core::model::Device>> devices_;  // 设备列表
    
    // 映射表，用于在树节点和设备之间建立关联
    std::map<HTREEITEM, std::shared_ptr<core::model::Device>> tree_item_map_;
    
    // 标记是否正在更新UI
    bool updating_;
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_DEVICE_TAB_H