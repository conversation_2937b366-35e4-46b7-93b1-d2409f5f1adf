#include "win32_control_factory.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

HWND Win32ControlFactory::createButton(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id)
{
    HWND button = CreateWindow(
        L"BUTTON",                // 窗口类名
        text.c_str(),             // 按钮文本
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!button) {
        throw std::runtime_error("Failed to create button control");
    }
    
    return button;
}

HWND Win32ControlFactory::createEdit(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id, DWORD style)
{
    HWND edit = CreateWindow(
        L"EDIT",                  // 窗口类名
        text.c_str(),             // 初始文本
        WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!edit) {
        throw std::runtime_error("Failed to create edit control");
    }
    
    return edit;
}

HWND Win32ControlFactory::createStatic(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id)
{
    HWND staticCtrl = CreateWindow(
        L"STATIC",                // 窗口类名
        text.c_str(),             // 文本
        WS_VISIBLE | WS_CHILD,    // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!staticCtrl) {
        throw std::runtime_error("Failed to create static control");
    }
    
    return staticCtrl;
}

HWND Win32ControlFactory::createComboBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style)
{
    HWND comboBox = CreateWindow(
        L"COMBOBOX",              // 窗口类名
        L"",                      // 初始文本
        WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!comboBox) {
        throw std::runtime_error("Failed to create combo box control");
    }
    
    return comboBox;
}

HWND Win32ControlFactory::createListBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style)
{
    HWND listBox = CreateWindow(
        L"LISTBOX",               // 窗口类名
        L"",                      // 初始文本
        WS_VISIBLE | WS_CHILD | WS_BORDER | LBS_NOTIFY | WS_VSCROLL | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!listBox) {
        throw std::runtime_error("Failed to create list box control");
    }
    
    return listBox;
}

HWND Win32ControlFactory::createTreeView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style)
{
    // 确保已初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_TREEVIEW_CLASSES;
    InitCommonControlsEx(&icex);
    
    HWND treeView = CreateWindow(
        WC_TREEVIEW,              // 窗口类名
        L"",                      // 初始文本
        WS_VISIBLE | WS_CHILD | WS_BORDER | TVS_HASLINES | TVS_HASBUTTONS | TVS_LINESATROOT | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!treeView) {
        throw std::runtime_error("Failed to create tree view control");
    }
    
    return treeView;
}

HWND Win32ControlFactory::createListView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style)
{
    // 确保已初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES;
    InitCommonControlsEx(&icex);
    
    HWND listView = CreateWindow(
        WC_LISTVIEW,              // 窗口类名
        L"",                      // 初始文本
        WS_VISIBLE | WS_CHILD | WS_BORDER | LVS_REPORT | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!listView) {
        throw std::runtime_error("Failed to create list view control");
    }
    
    return listView;
}

HWND Win32ControlFactory::createTabControl(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style)
{
    // 确保已初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_TAB_CLASSES;
    InitCommonControlsEx(&icex);
    
    HWND tabControl = CreateWindow(
        WC_TABCONTROL,            // 窗口类名
        L"",                      // 初始文本
        WS_VISIBLE | WS_CHILD | style,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!tabControl) {
        throw std::runtime_error("Failed to create tab control");
    }
    
    return tabControl;
}

HWND Win32ControlFactory::createCheckBox(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id, bool checked)
{
    HWND checkBox = CreateWindow(
        L"BUTTON",                // 窗口类名
        text.c_str(),             // 文本
        WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!checkBox) {
        throw std::runtime_error("Failed to create check box control");
    }
    
    // 设置初始状态
    SendMessage(checkBox, BM_SETCHECK, checked ? BST_CHECKED : BST_UNCHECKED, 0);
    
    return checkBox;
}

HWND Win32ControlFactory::createGroupBox(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id)
{
    HWND groupBox = CreateWindow(
        L"BUTTON",                // 窗口类名
        text.c_str(),             // 文本
        WS_VISIBLE | WS_CHILD | BS_GROUPBOX,  // 样式
        x, y, width, height,      // 位置和大小
        parent,                   // 父窗口
        id,                       // 控件ID
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!groupBox) {
        throw std::runtime_error("Failed to create group box control");
    }
    
    return groupBox;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor