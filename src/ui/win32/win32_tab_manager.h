#ifndef XSD_EDITOR_UI_WIN32_TAB_MANAGER_H
#define XSD_EDITOR_UI_WIN32_TAB_MANAGER_H

#include <windows.h>
#include <commctrl.h>
#include <vector>
#include <memory>
#include <string>
#include "win32_tab.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32标签页管理器
 * 
 * 负责管理标签页控件和各个标签页的内容。
 */
class Win32TabManager {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口句柄
     */
    Win32TabManager(HWND parent);
    
    /**
     * @brief 析构函数
     */
    ~Win32TabManager();
    
    /**
     * @brief 添加标签页
     * @param title 标签页标题
     * @param tab 标签页对象
     */
    void addTab(const std::wstring& title, Win32Tab* tab);
    
    /**
     * @brief 选择标签页
     * @param index 标签页索引
     */
    void selectTab(int index);
    
    /**
     * @brief 获取当前选中的标签页索引
     * @return 标签页索引
     */
    int getSelectedTabIndex() const;
    
    /**
     * @brief 调整标签页大小
     * @param rect 新的矩形区域
     */
    void resize(const RECT& rect);
    
    /**
     * @brief 处理通知消息
     * @param nmhdr 通知消息头
     */
    void handleNotify(LPNMHDR nmhdr);
    
    /**
     * @brief 获取标签页控件句柄
     * @return 控件句柄
     */
    HWND getHandle() const;
    
    /**
     * @brief 获取指定索引的标签页
     * @param index 标签页索引
     * @return 标签页指针
     */
    Win32Tab* getTab(int index) const;
    
    /**
     * @brief 获取当前选中的标签页
     * @return 标签页指针
     */
    Win32Tab* getSelectedTab() const;
    
private:
    HWND parent_;                                  // 父窗口句柄
    HWND tab_control_;                             // 标签页控件句柄
    std::vector<std::unique_ptr<Win32Tab>> tabs_;  // 标签页集合
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_TAB_MANAGER_H