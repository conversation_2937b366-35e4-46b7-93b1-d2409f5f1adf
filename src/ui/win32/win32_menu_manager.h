#ifndef XSD_EDITOR_UI_WIN32_MENU_MANAGER_H
#define XSD_EDITOR_UI_WIN32_MENU_MANAGER_H

#include <windows.h>
#include <string>
#include <map>
#include <functional>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32菜单管理器
 * 
 * 负责创建和管理应用程序的菜单栏。
 */
class Win32MenuManager {
public:
    /**
     * @brief 构造函数
     * @param hwnd 窗口句柄
     */
    Win32MenuManager(HWND hwnd);
    
    /**
     * @brief 析构函数
     */
    ~Win32MenuManager();
    
    /**
     * @brief 创建菜单栏
     */
    void createMenuBar();
    
    /**
     * @brief 添加菜单项
     * @param menu_id 菜单ID
     * @param label 菜单标签
     * @param handler 处理函数
     */
    void addMenuItem(int menu_id, const std::wstring& label, std::function<void()> handler);
    
    /**
     * @brief 添加子菜单
     * @param parent_id 父菜单ID
     * @param menu_id 菜单ID
     * @param label 菜单标签
     */
    void addSubMenu(int parent_id, int menu_id, const std::wstring& label);
    
    /**
     * @brief 添加分隔符
     * @param parent_id 父菜单ID
     */
    void addSeparator(int parent_id);
    
    /**
     * @brief 启用或禁用菜单项
     * @param menu_id 菜单ID
     * @param enabled 是否启用
     */
    void enableMenuItem(int menu_id, bool enabled);
    
    /**
     * @brief 处理菜单命令
     * @param menu_id 菜单ID
     * @return 是否处理了命令
     */
    bool handleCommand(int menu_id);
    
private:
    HWND hwnd_;                                      // 窗口句柄
    HMENU menu_bar_;                                 // 菜单栏句柄
    std::map<int, HMENU> sub_menus_;                 // 子菜单映射
    std::map<int, std::function<void()>> handlers_;  // 命令处理函数映射
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_MENU_MANAGER_H