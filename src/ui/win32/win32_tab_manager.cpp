#include "win32_tab_manager.h"
#include "win32_control_factory.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32TabManager::Win32TabManager(HWND parent)
    : parent_(parent)
    , tab_control_(nullptr)
{
    // 创建标签页控件
    tab_control_ = Win32ControlFactory::createTabControl(
        parent,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(1000),
        0
    );
    
    if (!tab_control_) {
        throw std::runtime_error("Failed to create tab control");
    }
}

Win32TabManager::~Win32TabManager()
{
    // 标签页控件会被自动销毁
    // 标签页对象由unique_ptr管理，会自动释放
}

void Win32TabManager::addTab(const std::wstring& title, Win32Tab* tab)
{
    if (!tab) {
        throw std::invalid_argument("Null tab");
    }
    
    // 创建标签页项
    TCITEM tie;
    tie.mask = TCIF_TEXT;
    tie.pszText = const_cast<LPWSTR>(title.c_str());
    
    // 添加标签页
    int index = TabCtrl_GetItemCount(tab_control_);
    TabCtrl_InsertItem(tab_control_, index, &tie);
    
    // 保存标签页对象
    tabs_.push_back(std::unique_ptr<Win32Tab>(tab));
    
    // 如果这是第一个标签页，则显示它
    if (tabs_.size() == 1) {
        tab->show(true);
    } else {
        tab->show(false);
    }
}

void Win32TabManager::selectTab(int index)
{
    if (index < 0 || index >= static_cast<int>(tabs_.size())) {
        throw std::out_of_range("Tab index out of range");
    }
    
    // 设置当前选中的标签页
    TabCtrl_SetCurSel(tab_control_, index);
    
    // 隐藏所有标签页
    for (auto& tab : tabs_) {
        tab->show(false);
    }
    
    // 显示选中的标签页
    tabs_[index]->show(true);
}

int Win32TabManager::getSelectedTabIndex() const
{
    return TabCtrl_GetCurSel(tab_control_);
}

void Win32TabManager::resize(const RECT& rect)
{
    // 调整标签页控件大小
    MoveWindow(tab_control_, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, TRUE);
    
    // 获取标签页控件的客户区
    RECT client_rect;
    GetClientRect(tab_control_, &client_rect);
    
    // 调整标签页区域，留出标签头的空间
    RECT tab_rect = client_rect;
    TabCtrl_AdjustRect(tab_control_, FALSE, &tab_rect);
    
    // 调整所有标签页的大小
    for (auto& tab : tabs_) {
        tab->resize(tab_rect);
    }
}

void Win32TabManager::handleNotify(LPNMHDR nmhdr)
{
    if (nmhdr->hwndFrom == tab_control_) {
        if (nmhdr->code == TCN_SELCHANGE) {
            // 标签页切换
            int index = TabCtrl_GetCurSel(tab_control_);
            if (index >= 0 && index < static_cast<int>(tabs_.size())) {
                // 隐藏所有标签页
                for (auto& tab : tabs_) {
                    tab->show(false);
                }
                
                // 显示选中的标签页
                tabs_[index]->show(true);
            }
        }
    } else {
        // 将通知转发给当前选中的标签页
        int index = TabCtrl_GetCurSel(tab_control_);
        if (index >= 0 && index < static_cast<int>(tabs_.size())) {
            tabs_[index]->handleNotify(nmhdr);
        }
    }
}

HWND Win32TabManager::getHandle() const
{
    return tab_control_;
}

Win32Tab* Win32TabManager::getTab(int index) const
{
    if (index < 0 || index >= static_cast<int>(tabs_.size())) {
        return nullptr;
    }
    
    return tabs_[index].get();
}

Win32Tab* Win32TabManager::getSelectedTab() const
{
    int index = TabCtrl_GetCurSel(tab_control_);
    if (index < 0 || index >= static_cast<int>(tabs_.size())) {
        return nullptr;
    }
    
    return tabs_[index].get();
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor