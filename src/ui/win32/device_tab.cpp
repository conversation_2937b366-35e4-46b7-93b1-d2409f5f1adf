#include "device_tab.h"
#include "win32_main_window.h"
#include "win32_control_factory.h"
#include <stdexcept>
#include <sstream>

namespace xsd_editor {
namespace ui {
namespace win32 {

// 控件ID
enum ControlID {
    ID_TREE_VIEW = 3001,
    ID_NAME_EDIT = 3002,
    ID_DESCRIPTION_EDIT = 3003,
    ID_ADD_BUTTON = 3011,
    ID_DELETE_BUTTON = 3012,
    ID_MOVE_UP_BUTTON = 3013,
    ID_MOVE_DOWN_BUTTON = 3014,
    ID_LEVEL_UP_BUTTON = 3015,
    ID_LEVEL_DOWN_BUTTON = 3016
};

DeviceTab::DeviceTab(HWND parent, Win32MainWindow* main_window)
    : Win32Tab(parent)
    , main_window_(main_window)
    , tree_view_(nullptr)
    , edit_panel_(nullptr)
    , button_panel_(nullptr)
    , updating_(false)
{
    // 创建标签页窗口
    hwnd_ = CreateWindow(
        L"STATIC",                // 窗口类名
        L"",                      // 窗口标题
        WS_CHILD | WS_VISIBLE,    // 样式
        0, 0, 0, 0,               // 位置和大小
        parent,                   // 父窗口
        NULL,                     // 菜单
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!hwnd_) {
        throw std::runtime_error("Failed to create device tab window");
    }
    
    // 创建界面元素
    createTreeView();
    createEditPanel();
    createButtonPanel();
}

DeviceTab::~DeviceTab()
{
    // 窗口会自动销毁
}

void DeviceTab::show(bool visible)
{
    ShowWindow(hwnd_, visible ? SW_SHOW : SW_HIDE);
    
    // 显示或隐藏子控件
    ShowWindow(tree_view_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(edit_panel_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(button_panel_, visible ? SW_SHOW : SW_HIDE);
}

void DeviceTab::resize(const RECT& rect)
{
    // 调整标签页窗口大小
    MoveWindow(hwnd_, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, TRUE);
    
    // 计算各个区域的大小
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    int tree_width = width / 3;
    int edit_width = width - tree_width;
    int button_height = 40;
    int edit_height = height - button_height;
    
    // 调整树视图大小
    MoveWindow(tree_view_, 0, 0, tree_width, height, TRUE);
    
    // 调整编辑面板大小
    MoveWindow(edit_panel_, tree_width, 0, edit_width, edit_height, TRUE);
    
    // 调整按钮面板大小
    MoveWindow(button_panel_, tree_width, edit_height, edit_width, button_height, TRUE);
}

void DeviceTab::handleNotify(LPNMHDR nmhdr)
{
    if (nmhdr->hwndFrom == tree_view_) {
        if (nmhdr->code == TVN_SELCHANGED) {
            // 树节点选择变更
            onTreeSelectionChanged();
        }
    }
}

void DeviceTab::handleCommand(WPARAM wparam, LPARAM lparam)
{
    // 获取控件ID和通知代码
    int control_id = LOWORD(wparam);
    int notify_code = HIWORD(wparam);
    
    // 处理编辑控件变更
    if (notify_code == EN_CHANGE) {
        if (!updating_) {
            // 更新当前选中的设备
            auto device = getSelectedDevice();
            if (device) {
                // 根据控件ID更新设备属性
                if (control_id == ID_NAME_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(name_edit_, buffer, 256);
                    device->setName(std::string(buffer, buffer + wcslen(buffer)));
                    
                    // 更新树节点文本
                    HTREEITEM tree_item = findDeviceInTree(device);
                    if (tree_item) {
                        TVITEM tvi;
                        tvi.mask = TVIF_TEXT;
                        tvi.hItem = tree_item;
                        tvi.pszText = buffer;
                        TreeView_SetItem(tree_view_, &tvi);
                    }
                    
                    triggerChangeCallback();
                }
            }
        }
    }
    // 处理按钮点击
    else if (notify_code == BN_CLICKED) {
        if (control_id == ID_ADD_BUTTON) {
            onAddClicked();
        }
        else if (control_id == ID_DELETE_BUTTON) {
            onDeleteClicked();
        }
        else if (control_id == ID_MOVE_UP_BUTTON) {
            onMoveUpClicked();
        }
        else if (control_id == ID_MOVE_DOWN_BUTTON) {
            onMoveDownClicked();
        }
        else if (control_id == ID_LEVEL_UP_BUTTON) {
            onLevelUpClicked();
        }
        else if (control_id == ID_LEVEL_DOWN_BUTTON) {
            onLevelDownClicked();
        }
    }
}

void DeviceTab::updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices)
{
    // 保存设备列表
    devices_ = devices;
    
    // 清空树视图
    TreeView_DeleteAllItems(tree_view_);
    tree_item_map_.clear();
    
    // 重新构建树
    for (const auto& device : devices) {
        buildDeviceTree(NULL, device);
    }
}

std::vector<std::shared_ptr<core::model::Device>> DeviceTab::getDevices() const
{
    return devices_;
}

std::shared_ptr<core::model::Device> DeviceTab::getSelectedDevice() const
{
    // 获取当前选中的树节点
    HTREEITEM selected = TreeView_GetSelection(tree_view_);
    if (!selected) {
        return nullptr;
    }
    
    // 查找对应的设备
    auto it = tree_item_map_.find(selected);
    if (it != tree_item_map_.end()) {
        return it->second;
    }
    
    return nullptr;
}

void DeviceTab::createTreeView()
{
    // 创建树视图
    tree_view_ = Win32ControlFactory::createTreeView(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_TREE_VIEW),
        TVS_HASBUTTONS | TVS_HASLINES | TVS_LINESATROOT | TVS_SHOWSELALWAYS
    );
}

void DeviceTab::createEditPanel()
{
    // 创建编辑面板
    edit_panel_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Device Properties",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建标签和编辑控件
    int label_width = 80;
    int edit_width = 200;
    int row_height = 25;
    int margin = 10;
    
    // 名称
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Name:",
        margin, margin + 0 * row_height, label_width, row_height,
        NULL
    );
    name_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 0 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_NAME_EDIT)
    );
    
    // 描述
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Description:",
        margin, margin + 1 * row_height, label_width, row_height,
        NULL
    );
    description_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 1 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_DESCRIPTION_EDIT)
    );
}

void DeviceTab::createButtonPanel()
{
    // 创建按钮面板
    button_panel_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Operations",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建按钮
    int button_width = 100;
    int button_height = 25;
    int margin = 10;
    
    add_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Add",
        margin, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_ADD_BUTTON)
    );
    
    delete_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Delete",
        margin + button_width + margin, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_DELETE_BUTTON)
    );
    
    move_up_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Move Up",
        margin + (button_width + margin) * 2, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_MOVE_UP_BUTTON)
    );
    
    move_down_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Move Down",
        margin + (button_width + margin) * 3, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_MOVE_DOWN_BUTTON)
    );
    
    level_up_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Level Up",
        margin + (button_width + margin) * 4, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_LEVEL_UP_BUTTON)
    );
    
    level_down_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Level Down",
        margin + (button_width + margin) * 5, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_LEVEL_DOWN_BUTTON)
    );
}

void DeviceTab::updateEditPanel(const std::shared_ptr<core::model::Device>& device)
{
    if (!device) {
        // 清空编辑控件
        SetWindowText(name_edit_, L"");
        SetWindowText(description_edit_, L"");
        return;
    }
    
    // 设置更新标志，防止触发变更事件
    updating_ = true;
    
    // 更新编辑控件
    SetWindowText(name_edit_, std::wstring(device->getName().begin(), device->getName().end()).c_str());
    
    // 清除更新标志
    updating_ = false;
}

void DeviceTab::buildDeviceTree(HTREEITEM parent_item, const std::shared_ptr<core::model::Device>& device)
{
    // 创建树节点
    TVINSERTSTRUCT tvis;
    tvis.hParent = parent_item;
    tvis.hInsertAfter = TVI_LAST;
    tvis.item.mask = TVIF_TEXT | TVIF_PARAM;
    tvis.item.pszText = const_cast<LPWSTR>(std::wstring(device->getName().begin(), device->getName().end()).c_str());
    tvis.item.lParam = reinterpret_cast<LPARAM>(device.get());
    
    HTREEITEM tree_item = TreeView_InsertItem(tree_view_, &tvis);
    
    // 保存树节点和设备的映射关系
    tree_item_map_[tree_item] = device;
    
    // 递归添加子设备
    for (const auto& child : device->getChildren()) {
        buildDeviceTree(tree_item, child);
    }
    
    // 展开节点
    TreeView_Expand(tree_view_, tree_item, TVE_EXPAND);
}

HTREEITEM DeviceTab::findDeviceInTree(const std::shared_ptr<core::model::Device>& device)
{
    // 从根节点开始查找
    HTREEITEM root = TreeView_GetRoot(tree_view_);
    while (root) {
        HTREEITEM found = findDeviceRecursive(root, device);
        if (found) {
            return found;
        }
        root = TreeView_GetNextSibling(tree_view_, root);
    }
    
    return NULL;
}

HTREEITEM DeviceTab::findDeviceRecursive(HTREEITEM parent_item, const std::shared_ptr<core::model::Device>& device)
{
    // 检查当前节点
    auto it = tree_item_map_.find(parent_item);
    if (it != tree_item_map_.end() && it->second == device) {
        return parent_item;
    }
    
    // 递归查找子节点
    HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
    while (child) {
        HTREEITEM found = findDeviceRecursive(child, device);
        if (found) {
            return found;
        }
        child = TreeView_GetNextSibling(tree_view_, child);
    }
    
    return NULL;
}

void DeviceTab::onTreeSelectionChanged()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    
    // 更新编辑面板
    updateEditPanel(device);
}

void DeviceTab::onAddClicked()
{
    // 获取当前选中的设备
    auto parent = getSelectedDevice();
    
    // 创建新设备
    auto new_device = std::make_shared<core::model::Device>("New Device");
    
    if (parent) {
        // 添加为子设备
        parent->addChild(new_device);
        
        // 更新树视图
        HTREEITEM parent_item = findDeviceInTree(parent);
        if (parent_item) {
            buildDeviceTree(parent_item, new_device);
        }
    } else {
        // 添加为根设备
        devices_.push_back(new_device);
        
        // 更新树视图
        buildDeviceTree(NULL, new_device);
    }
    
    // 触发变更事件
    triggerChangeCallback();
}

void DeviceTab::onDeleteClicked()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    if (!device) {
        return;
    }
    
    // 确认删除
    if (MessageBox(hwnd_, L"Are you sure you want to delete this device?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
        return;
    }
    
    // 从树视图中删除
    HTREEITEM tree_item = findDeviceInTree(device);
    if (tree_item) {
        TreeView_DeleteItem(tree_view_, tree_item);
        
        // 从映射表中移除
        tree_item_map_.erase(tree_item);
        
        // 从父设备中移除
        auto parent = device->getParent().lock();
        if (parent) {
            parent->removeChild(device);
        } else {
            // 从根设备列表中移除
            auto it = std::find(devices_.begin(), devices_.end(), device);
            if (it != devices_.end()) {
                devices_.erase(it);
            }
        }
        
        // 触发变更事件
        triggerChangeCallback();
    }
}

void DeviceTab::onMoveUpClicked()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    if (!device) {
        return;
    }
    
    // 获取父设备
    auto parent = device->getParent().lock();
    if (!parent) {
        // 根设备
        auto it = std::find(devices_.begin(), devices_.end(), device);
        if (it != devices_.begin()) {
            // 交换位置
            auto prev_it = it - 1;
            std::iter_swap(it, prev_it);
            
            // 更新树视图
            updateDevices(devices_);
            
            // 重新选中设备
            HTREEITEM tree_item = findDeviceInTree(device);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    } else {
        // 子设备
        auto& siblings = parent->getChildren();
        auto it = std::find(siblings.begin(), siblings.end(), device);
        if (it != siblings.begin()) {
            // 交换位置
            auto prev_it = it - 1;
            std::iter_swap(it, prev_it);
            
            // 更新树视图
            HTREEITEM parent_item = findDeviceInTree(parent);
            if (parent_item) {
                // 删除所有子节点
                HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
                while (child) {
                    HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                    TreeView_DeleteItem(tree_view_, child);
                    child = next;
                }
                
                // 重新添加子节点
                for (const auto& child : parent->getChildren()) {
                    buildDeviceTree(parent_item, child);
                }
                
                // 展开父节点
                TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
                
                // 重新选中设备
                HTREEITEM tree_item = findDeviceInTree(device);
                if (tree_item) {
                    TreeView_SelectItem(tree_view_, tree_item);
                }
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
}

void DeviceTab::onMoveDownClicked()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    if (!device) {
        return;
    }
    
    // 获取父设备
    auto parent = device->getParent().lock();
    if (!parent) {
        // 根设备
        auto it = std::find(devices_.begin(), devices_.end(), device);
        if (it != devices_.end() - 1) {
            // 交换位置
            auto next_it = it + 1;
            std::iter_swap(it, next_it);
            
            // 更新树视图
            updateDevices(devices_);
            
            // 重新选中设备
            HTREEITEM tree_item = findDeviceInTree(device);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    } else {
        // 子设备
        auto& siblings = parent->getChildren();
        auto it = std::find(siblings.begin(), siblings.end(), device);
        if (it != siblings.end() - 1) {
            // 交换位置
            auto next_it = it + 1;
            std::iter_swap(it, next_it);
            
            // 更新树视图
            HTREEITEM parent_item = findDeviceInTree(parent);
            if (parent_item) {
                // 删除所有子节点
                HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
                while (child) {
                    HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                    TreeView_DeleteItem(tree_view_, child);
                    child = next;
                }
                
                // 重新添加子节点
                for (const auto& child : parent->getChildren()) {
                    buildDeviceTree(parent_item, child);
                }
                
                // 展开父节点
                TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
                
                // 重新选中设备
                HTREEITEM tree_item = findDeviceInTree(device);
                if (tree_item) {
                    TreeView_SelectItem(tree_view_, tree_item);
                }
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
}

void DeviceTab::onLevelUpClicked()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    if (!device) {
        return;
    }
    
    // 获取父设备和祖父设备
    auto parent = device->getParent().lock();
    if (!parent) {
        // 根设备无法上移层级
        return;
    }
    
    auto grandparent = parent->getParent().lock();
    if (!grandparent) {
        // 父设备是根设备，将当前设备移动到根设备列表
        // 从父设备中移除
        parent->removeChild(device);
        
        // 添加到根设备列表
        devices_.push_back(device);
        
        // 更新树视图
        updateDevices(devices_);
        
        // 重新选中设备
        HTREEITEM tree_item = findDeviceInTree(device);
        if (tree_item) {
            TreeView_SelectItem(tree_view_, tree_item);
        }
        
        // 触发变更事件
        triggerChangeCallback();
    } else {
        // 将当前设备移动到祖父设备下
        // 从父设备中移除
        parent->removeChild(device);
        
        // 添加到祖父设备下
        grandparent->addChild(device);
        
        // 更新树视图
        HTREEITEM grandparent_item = findDeviceInTree(grandparent);
        if (grandparent_item) {
            // 删除所有子节点
            HTREEITEM child = TreeView_GetChild(tree_view_, grandparent_item);
            while (child) {
                HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                TreeView_DeleteItem(tree_view_, child);
                child = next;
            }
            
            // 重新添加子节点
            for (const auto& child : grandparent->getChildren()) {
                buildDeviceTree(grandparent_item, child);
            }
            
            // 展开祖父节点
            TreeView_Expand(tree_view_, grandparent_item, TVE_EXPAND);
            
            // 重新选中设备
            HTREEITEM tree_item = findDeviceInTree(device);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
        }
        
        // 触发变更事件
        triggerChangeCallback();
    }
}

void DeviceTab::onLevelDownClicked()
{
    // 获取当前选中的设备
    auto device = getSelectedDevice();
    if (!device) {
        return;
    }
    
    // 获取父设备
    auto parent = device->getParent().lock();
    
    // 获取当前设备在父设备中的位置
    std::vector<std::shared_ptr<core::model::Device>> siblings;
    size_t index = 0;
    
    if (parent) {
        siblings = parent->getChildren();
        auto it = std::find(siblings.begin(), siblings.end(), device);
        if (it != siblings.end()) {
            index = std::distance(siblings.begin(), it);
        }
    } else {
        siblings = devices_;
        auto it = std::find(siblings.begin(), siblings.end(), device);
        if (it != siblings.end()) {
            index = std::distance(siblings.begin(), it);
        }
    }
    
    // 检查是否有前一个兄弟设备
    if (index == 0) {
        // 没有前一个兄弟设备，无法下移层级
        return;
    }
    
    // 获取前一个兄弟设备
    auto prev_sibling = siblings[index - 1];
    
    // 从当前位置移除
    if (parent) {
        parent->removeChild(device);
    } else {
        devices_.erase(std::find(devices_.begin(), devices_.end(), device));
    }
    
    // 添加到前一个兄弟设备下
    prev_sibling->addChild(device);
    
    // 更新树视图
    if (parent) {
        HTREEITEM parent_item = findDeviceInTree(parent);
        if (parent_item) {
            // 删除所有子节点
            HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
            while (child) {
                HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                TreeView_DeleteItem(tree_view_, child);
                child = next;
            }
            
            // 重新添加子节点
            for (const auto& child : parent->getChildren()) {
                buildDeviceTree(parent_item, child);
            }
            
            // 展开父节点
            TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
        }
    } else {
        // 更新根设备
        updateDevices(devices_);
    }
    
    // 展开前一个兄弟设备
    HTREEITEM prev_sibling_item = findDeviceInTree(prev_sibling);
    if (prev_sibling_item) {
        TreeView_Expand(tree_view_, prev_sibling_item, TVE_EXPAND);
    }
    
    // 重新选中设备
    HTREEITEM tree_item = findDeviceInTree(device);
    if (tree_item) {
        TreeView_SelectItem(tree_view_, tree_item);
    }
    
    // 触发变更事件
    triggerChangeCallback();
}

bool DeviceTab::isValidMove(const std::shared_ptr<core::model::Device>& source,
                          const std::shared_ptr<core::model::Device>& target,
                          bool as_sibling) const
{
    // 检查源设备是否为空
    if (!source) {
        return false;
    }
    
    // 检查目标设备是否为空
    if (!target) {
        // 如果目标为空，只有作为根设备才有效
        return as_sibling;
    }
    
    // 检查是否将设备移动到自身下
    if (source == target) {
        return false;
    }
    
    // 检查是否将设备移动到其子设备下
    if (!as_sibling) {
        // 检查目标是否为源的后代
        auto current = target;
        while (current) {
            auto parent = current->getParent().lock();
            if (parent == source) {
                return false;
            }
            current = parent;
        }
    }
    
    return true;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor