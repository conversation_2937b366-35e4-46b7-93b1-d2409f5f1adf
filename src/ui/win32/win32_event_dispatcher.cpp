#include "win32_event_dispatcher.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32EventDispatcher::Win32EventDispatcher(HWND hwnd)
    : hwnd_(hwnd)
{
    // 构造函数只保存窗口句柄
}

Win32EventDispatcher::~Win32EventDispatcher()
{
    // 析构函数不需要特殊处理
}

void Win32EventDispatcher::registerMessageHandler(UINT msg, std::function<void(WPARAM, LPARAM)> handler)
{
    if (!handler) {
        throw std::invalid_argument("Null message handler");
    }
    
    message_handlers_[msg] = std::move(handler);
}

void Win32EventDispatcher::registerCommandHandler(WORD command, std::function<void()> handler)
{
    if (!handler) {
        throw std::invalid_argument("Null command handler");
    }
    
    command_handlers_[command] = std::move(handler);
}

void Win32EventDispatcher::registerNotifyHandler(HWND control, UINT code, std::function<void(LPNMHDR)> handler)
{
    if (!handler) {
        throw std::invalid_argument("Null notify handler");
    }
    
    notify_handlers_[std::make_tuple(control, code)] = std::move(handler);
}

bool Win32EventDispatcher::dispatchMessage(UINT msg, WPARAM wparam, LPARAM lparam)
{
    // 处理WM_COMMAND消息
    if (msg == WM_COMMAND) {
        WORD command_id = LOWORD(wparam);
        auto it = command_handlers_.find(command_id);
        if (it != command_handlers_.end()) {
            it->second();
            return true;
        }
    }
    
    // 处理WM_NOTIFY消息
    else if (msg == WM_NOTIFY) {
        LPNMHDR nmhdr = reinterpret_cast<LPNMHDR>(lparam);
        auto key = std::make_tuple(nmhdr->hwndFrom, nmhdr->code);
        auto it = notify_handlers_.find(key);
        if (it != notify_handlers_.end()) {
            it->second(nmhdr);
            return true;
        }
        
        // 如果没有找到精确匹配，尝试使用通配符匹配（控件句柄为NULL）
        key = std::make_tuple(NULL, nmhdr->code);
        it = notify_handlers_.find(key);
        if (it != notify_handlers_.end()) {
            it->second(nmhdr);
            return true;
        }
    }
    
    // 处理其他消息
    auto it = message_handlers_.find(msg);
    if (it != message_handlers_.end()) {
        it->second(wparam, lparam);
        return true;
    }
    
    return false;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor