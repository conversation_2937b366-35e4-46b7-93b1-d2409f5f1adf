#ifndef XSD_EDITOR_UI_WIN32_ELEMENT_TAB_H
#define XSD_EDITOR_UI_WIN32_ELEMENT_TAB_H

#include <windows.h>
#include <commctrl.h>
#include <vector>
#include <map>
#include <memory>
#include <string>
#include "win32_tab.h"
#include "../../core/model/item.h"
#include "../../core/model/type.h"
#include "../../core/model/device.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

// 前向声明
class Win32MainWindow;

/**
 * @brief 元素标签页
 * 
 * 实现元素的树状显示和编辑功能。
 */
class ElementTab : public Win32Tab {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口句柄
     * @param main_window 主窗口指针
     */
    ElementTab(HWND parent, Win32MainWindow* main_window);
    
    /**
     * @brief 析构函数
     */
    virtual ~ElementTab();
    
    // 实现Win32Tab接口
    void show(bool visible) override;
    void resize(const RECT& rect) override;
    void handleNotify(LPNMHDR nmhdr) override;
    void handleCommand(WPARAM wparam, LPARAM lparam) override;
    
    /**
     * @brief 更新元素树
     * @param items 元素列表
     */
    void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items);
    
    /**
     * @brief 获取当前元素列表
     * @return 元素列表
     */
    std::vector<std::shared_ptr<core::model::Item>> getElements() const;
    
    /**
     * @brief 获取选中的元素
     * @return 选中的元素
     */
    std::shared_ptr<core::model::Item> getSelectedItem() const;
    
    /**
     * @brief 设置类型列表
     * @param types 类型列表
     */
    void setTypesList(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types);
    
    /**
     * @brief 设置设备列表
     * @param devices 设备列表
     */
    void setDevicesList(const std::vector<std::shared_ptr<core::model::Device>>& devices);
    
private:
    /**
     * @brief 创建树视图
     */
    void createTreeView();
    
    /**
     * @brief 创建编辑面板
     */
    void createEditPanel();
    
    /**
     * @brief 创建属性编辑面板
     */
    void createAttributesPanel();
    
    /**
     * @brief 创建按钮面板
     */
    void createButtonPanel();
    
    /**
     * @brief 更新编辑面板
     * @param item 要编辑的元素
     */
    void updateEditPanel(const std::shared_ptr<core::model::Item>& item);
    
    /**
     * @brief 更新属性列表
     * @param item 要编辑的元素
     */
    void updateAttributesList(const std::shared_ptr<core::model::Item>& item);
    
    /**
     * @brief 填充高级类型下拉框
     */
    void fillAdvTypeCombo();
    
    /**
     * @brief 填充设备下拉框
     */
    void fillDevicesCombo();
    
    /**
     * @brief 递归添加设备到下拉框
     * @param device 设备
     * @param level 层级
     */
    void addDeviceToCombo(const std::shared_ptr<core::model::Device>& device, int level);
    
    /**
     * @brief 递归构建元素树
     * @param parent_item 父节点句柄
     * @param item 元素
     */
    void buildElementTree(HTREEITEM parent_item, const std::shared_ptr<core::model::Item>& item);
    
    /**
     * @brief 查找元素在树中的位置
     * @param item 元素
     * @return 树节点句柄
     */
    HTREEITEM findElementInTree(const std::shared_ptr<core::model::Item>& item);
    
    /**
     * @brief 递归查找元素
     * @param parent_item 父节点句柄
     * @param item 要查找的元素
     * @return 树节点句柄
     */
    HTREEITEM findElementRecursive(HTREEITEM parent_item, const std::shared_ptr<core::model::Item>& item);
    
    /**
     * @brief 处理树节点选择变更
     */
    void onTreeSelectionChanged();
    
    /**
     * @brief 处理添加按钮点击
     */
    void onAddClicked();
    
    /**
     * @brief 处理删除按钮点击
     */
    void onDeleteClicked();
    
    /**
     * @brief 处理上移按钮点击
     */
    void onMoveUpClicked();
    
    /**
     * @brief 处理下移按钮点击
     */
    void onMoveDownClicked();
    
    /**
     * @brief 处理层级上移按钮点击
     */
    void onLevelUpClicked();
    
    /**
     * @brief 处理层级下移按钮点击
     */
    void onLevelDownClicked();
    
    /**
     * @brief 处理添加属性按钮点击
     */
    void onAddAttrClicked();
    
    /**
     * @brief 处理删除属性按钮点击
     */
    void onDeleteAttrClicked();
    
    /**
     * @brief 检查移动操作是否有效
     * @param source 源元素
     * @param target 目标元素
     * @param as_sibling 是否作为兄弟节点
     * @return 是否有效
     */
    bool isValidMove(const std::shared_ptr<core::model::Item>& source,
                    const std::shared_ptr<core::model::Item>& target,
                    bool as_sibling) const;
    
    // 成员变量
    Win32MainWindow* main_window_;  // 主窗口指针
    
    // 控件句柄
    HWND tree_view_;        // 树视图
    HWND edit_panel_;       // 编辑面板
    HWND attr_list_view_;   // 属性列表
    HWND button_panel_;     // 按钮面板
    
    // 编辑控件
    HWND name_edit_;        // 名称编辑框
    HWND type_edit_;        // 类型编辑框
    HWND adv_type_combo_;   // 高级类型下拉框
    HWND file_path_edit_;   // 文件路径编辑框
    HWND devices_edit_;     // 设备编辑框
    HWND devices_combo_;    // 设备下拉框
    HWND versions_edit_;    // 版本编辑框
    HWND is_list_check_;    // 是否列表复选框
    
    // 按钮控件
    HWND add_button_;       // 添加按钮
    HWND delete_button_;    // 删除按钮
    HWND move_up_button_;   // 上移按钮
    HWND move_down_button_; // 下移按钮
    HWND level_up_button_;  // 层级上移按钮
    HWND level_down_button_;// 层级下移按钮
    HWND add_attr_button_;  // 添加属性按钮
    HWND del_attr_button_;  // 删除属性按钮
    
    // 数据
    std::vector<std::shared_ptr<core::model::Item>> items_;  // 元素列表
    std::vector<std::shared_ptr<core::model::AdvancedType>> types_list_;  // 类型列表
    std::vector<std::shared_ptr<core::model::Device>> devices_list_;  // 设备列表
    
    // 映射表，用于在树节点和元素之间建立关联
    std::map<HTREEITEM, std::shared_ptr<core::model::Item>> tree_item_map_;
    
    // 标记是否正在更新UI
    bool updating_;
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_ELEMENT_TAB_H