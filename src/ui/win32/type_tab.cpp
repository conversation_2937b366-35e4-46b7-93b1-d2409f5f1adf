#include "type_tab.h"
#include "win32_control_factory.h"
#include <stdexcept>
#include <sstream>
#include <regex>

namespace xsd_editor {
namespace ui {
namespace win32 {

// 控件ID
enum ControlID {
    ID_TYPE_LIST = 2001,
    ID_NAME_EDIT = 2002,
    ID_BASE_TYPE_COMBO = 2003,
    ID_PARENT_COMBO = 2004,
    ID_REGEX_EDIT = 2005,
    ID_TEST_REGEX_BUTTON = 2006,
    ID_MIN_EDIT = 2007,
    ID_MAX_EDIT = 2008,
    ID_ENUM_LIST = 2009,
    ID_ATTR_LIST = 2010,
    ID_EXTRAS_EDIT = 2011,
    ID_ADD_TYPE_BUTTON = 2012,
    ID_DELETE_TYPE_BUTTON = 2013,
    ID_ADD_ENUM_BUTTON = 2014,
    ID_DEL_ENUM_BUTTON = 2015,
    ID_ADD_ATTR_BUTTON = 2016,
    ID_DEL_ATTR_BUTTON = 2017
};

TypeTab::TypeTab(HWND parent)
    : Win32Tab(parent)
    , updating_(false)
{
    // 创建标签页窗口
    hwnd_ = CreateWindow(
        L"STATIC",                // 窗口类名
        L"",                      // 窗口标题
        WS_CHILD | WS_VISIBLE,    // 样式
        0, 0, 0, 0,               // 位置和大小
        parent,                   // 父窗口
        NULL,                     // 菜单
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!hwnd_) {
        throw std::runtime_error("Failed to create type tab window");
    }
    
    // 创建界面元素
    createTypeList();
    createEditPanel();
    createConstraintPanel();
    createExtraAttrPanel();
    createExtrasPanel();
}

TypeTab::~TypeTab()
{
    // 窗口会自动销毁
}

void TypeTab::show(bool visible)
{
    ShowWindow(hwnd_, visible ? SW_SHOW : SW_HIDE);
    
    // 显示或隐藏子控件
    ShowWindow(type_list_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(edit_panel_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(constraint_tab_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(enum_list_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(attr_list_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(extras_edit_, visible ? SW_SHOW : SW_HIDE);
}

void TypeTab::resize(const RECT& rect)
{
    // 调整标签页窗口大小
    MoveWindow(hwnd_, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, TRUE);
    
    // 计算各个区域的大小
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    int list_width = width / 3;
    int edit_width = width - list_width;
    
    // 调整类型列表大小
    MoveWindow(type_list_, 0, 0, list_width, height, TRUE);
    
    // 调整编辑面板大小
    MoveWindow(edit_panel_, list_width, 0, edit_width, height / 4, TRUE);
    
    // 调整约束标签页大小
    MoveWindow(constraint_tab_, list_width, height / 4, edit_width, height / 4, TRUE);
    
    // 调整枚举列表大小
    MoveWindow(enum_list_, list_width, height / 2, edit_width / 2, height / 4, TRUE);
    
    // 调整属性列表大小
    MoveWindow(attr_list_, list_width + edit_width / 2, height / 2, edit_width / 2, height / 4, TRUE);
    
    // 调整extras编辑框大小
    MoveWindow(extras_edit_, list_width, height * 3 / 4, edit_width, height / 4, TRUE);
}

void TypeTab::handleNotify(LPNMHDR nmhdr)
{
    if (nmhdr->hwndFrom == type_list_) {
        if (nmhdr->code == LVN_ITEMCHANGED) {
            LPNMLISTVIEW pnmv = (LPNMLISTVIEW)nmhdr;
            if ((pnmv->uNewState & LVIS_SELECTED) && !(pnmv->uOldState & LVIS_SELECTED)) {
                // 选择变更
                onSelectionChanged();
            }
        }
    }
    else if (nmhdr->hwndFrom == enum_list_) {
        // 处理枚举列表通知
    }
    else if (nmhdr->hwndFrom == attr_list_) {
        // 处理属性列表通知
    }
}

void TypeTab::handleCommand(WPARAM wparam, LPARAM lparam)
{
    // 获取控件ID和通知代码
    int control_id = LOWORD(wparam);
    int notify_code = HIWORD(wparam);
    
    // 处理编辑控件变更
    if (notify_code == EN_CHANGE) {
        if (!updating_) {
            // 更新当前选中的类型
            auto type = getSelectedType();
            if (type) {
                // 根据控件ID更新类型属性
                if (control_id == ID_NAME_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(name_edit_, buffer, 256);
                    type->setName(std::string(buffer, buffer + wcslen(buffer)));
                    updateTypeInList(type);
                    triggerChangeCallback();
                }
                else if (control_id == ID_REGEX_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(regex_edit_, buffer, 256);
                    type->setRegexPattern(std::string(buffer, buffer + wcslen(buffer)));
                    triggerChangeCallback();
                }
                else if (control_id == ID_MIN_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(min_edit_, buffer, 256);
                    std::string min_str(buffer, buffer + wcslen(buffer));
                    if (!min_str.empty()) {
                        try {
                            int min_value = std::stoi(min_str);
                            type->setMinValue(min_value);
                            triggerChangeCallback();
                        }
                        catch (const std::exception&) {
                            // 忽略无效输入
                        }
                    }
                }
                else if (control_id == ID_MAX_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(max_edit_, buffer, 256);
                    std::string max_str(buffer, buffer + wcslen(buffer));
                    if (!max_str.empty()) {
                        try {
                            int max_value = std::stoi(max_str);
                            type->setMaxValue(max_value);
                            triggerChangeCallback();
                        }
                        catch (const std::exception&) {
                            // 忽略无效输入
                        }
                    }
                }
                else if (control_id == ID_EXTRAS_EDIT) {
                    wchar_t buffer[1024];
                    GetWindowText(extras_edit_, buffer, 1024);
                    std::string extras_str(buffer, buffer + wcslen(buffer));
                    
                    // 解析extras字符串
                    extras_.clear();
                    std::istringstream iss(extras_str);
                    std::string line;
                    while (std::getline(iss, line)) {
                        if (!line.empty()) {
                            extras_.push_back(line);
                        }
                    }
                    
                    triggerChangeCallback();
                }
            }
        }
    }
    // 处理下拉框选择变更
    else if (notify_code == CBN_SELCHANGE) {
        if (!updating_) {
            // 更新当前选中的类型
            auto type = getSelectedType();
            if (type) {
                // 根据控件ID更新类型属性
                if (control_id == ID_BASE_TYPE_COMBO) {
                    int index = ComboBox_GetCurSel(base_type_combo_);
                    if (index != CB_ERR) {
                        wchar_t buffer[256];
                        ComboBox_GetLBText(base_type_combo_, index, buffer);
                        type->setBaseType(std::string(buffer, buffer + wcslen(buffer)));
                        updateTypeInList(type);
                        triggerChangeCallback();
                    }
                }
                else if (control_id == ID_PARENT_COMBO) {
                    int index = ComboBox_GetCurSel(parent_combo_);
                    if (index != CB_ERR) {
                        if (index == 0) {
                            // 选择了"None"
                            type->setParentType("");
                        }
                        else {
                            wchar_t buffer[256];
                            ComboBox_GetLBText(parent_combo_, index, buffer);
                            type->setParentType(std::string(buffer, buffer + wcslen(buffer)));
                        }
                        triggerChangeCallback();
                    }
                }
            }
        }
    }
    // 处理按钮点击
    else if (notify_code == BN_CLICKED) {
        if (control_id == ID_ADD_TYPE_BUTTON) {
            onAddTypeClicked();
        }
        else if (control_id == ID_DELETE_TYPE_BUTTON) {
            onDeleteTypeClicked();
        }
        else if (control_id == ID_ADD_ENUM_BUTTON) {
            onAddEnumClicked();
        }
        else if (control_id == ID_DEL_ENUM_BUTTON) {
            onDeleteEnumClicked();
        }
        else if (control_id == ID_ADD_ATTR_BUTTON) {
            onAddAttrClicked();
        }
        else if (control_id == ID_DEL_ATTR_BUTTON) {
            onDeleteAttrClicked();
        }
        else if (control_id == ID_TEST_REGEX_BUTTON) {
            onTestRegexClicked();
        }
    }
}

void TypeTab::updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types)
{
    // 保存类型列表
    types_ = types;
    
    // 清空类型列表
    ListView_DeleteAllItems(type_list_);
    list_item_map_.clear();
    
    // 添加类型到列表
    for (size_t i = 0; i < types.size(); ++i) {
        const auto& type = types[i];
        
        LVITEM lvi;
        lvi.mask = LVIF_TEXT | LVIF_PARAM;
        lvi.iItem = static_cast<int>(i);
        lvi.iSubItem = 0;
        lvi.lParam = static_cast<LPARAM>(i);  // 使用索引作为参数
        lvi.pszText = const_cast<LPWSTR>(std::wstring(type->getName().begin(), type->getName().end()).c_str());
        
        int index = ListView_InsertItem(type_list_, &lvi);
        
        // 设置基础类型列
        ListView_SetItemText(type_list_, index, 1, 
            const_cast<LPWSTR>(std::wstring(type->getBaseType().begin(), type->getBaseType().end()).c_str()));
        
        // 保存映射关系
        list_item_map_[index] = type;
    }
    
    // 更新父类型下拉框
    fillParentCombo();
}

std::vector<std::shared_ptr<core::model::AdvancedType>> TypeTab::getTypes() const
{
    return types_;
}

std::shared_ptr<core::model::AdvancedType> TypeTab::getSelectedType() const
{
    // 获取当前选中的列表项
    int selected = ListView_GetNextItem(type_list_, -1, LVNI_SELECTED);
    if (selected == -1) {
        return nullptr;
    }
    
    // 查找对应的类型
    auto it = list_item_map_.find(selected);
    if (it != list_item_map_.end()) {
        return it->second;
    }
    
    return nullptr;
}

void TypeTab::setExtras(const std::vector<std::string>& extras)
{
    extras_ = extras;
    
    // 更新extras编辑框
    std::string extras_str;
    for (const auto& extra : extras) {
        extras_str += extra + "\n";
    }
    
    SetWindowText(extras_edit_, std::wstring(extras_str.begin(), extras_str.end()).c_str());
}

std::vector<std::string> TypeTab::getExtras() const
{
    return extras_;
}

void TypeTab::createTypeList()
{
    // 创建类型列表
    type_list_ = Win32ControlFactory::createListView(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_TYPE_LIST),
        LVS_REPORT | LVS_SINGLESEL | LVS_SHOWSELALWAYS
    );
    
    // 添加列
    LVCOLUMN lvc;
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;
    
    lvc.iSubItem = 0;
    lvc.cx = 150;
    lvc.pszText = const_cast<LPWSTR>(L"Name");
    ListView_InsertColumn(type_list_, 0, &lvc);
    
    lvc.iSubItem = 1;
    lvc.cx = 100;
    lvc.pszText = const_cast<LPWSTR>(L"Base Type");
    ListView_InsertColumn(type_list_, 1, &lvc);
    
    // 创建类型操作按钮
    add_type_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Add Type",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_ADD_TYPE_BUTTON)
    );
    
    delete_type_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Delete Type",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_DELETE_TYPE_BUTTON)
    );
}

void TypeTab::createEditPanel()
{
    // 创建编辑面板
    edit_panel_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Type Properties",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建标签和编辑控件
    int label_width = 80;
    int edit_width = 200;
    int row_height = 25;
    int margin = 10;
    
    // 名称
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Name:",
        margin, margin + 0 * row_height, label_width, row_height,
        NULL
    );
    name_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 0 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_NAME_EDIT)
    );
    
    // 基础类型
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Base Type:",
        margin, margin + 1 * row_height, label_width, row_height,
        NULL
    );
    base_type_combo_ = Win32ControlFactory::createComboBox(
        edit_panel_,
        margin + label_width, margin + 1 * row_height, edit_width, row_height * 10,
        reinterpret_cast<HMENU>(ID_BASE_TYPE_COMBO)
    );
    
    // 添加基础类型选项
    ComboBox_AddString(base_type_combo_, L"string");
    ComboBox_AddString(base_type_combo_, L"int");
    ComboBox_AddString(base_type_combo_, L"bool");
    
    // 父类型
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Parent Type:",
        margin, margin + 2 * row_height, label_width, row_height,
        NULL
    );
    parent_combo_ = Win32ControlFactory::createComboBox(
        edit_panel_,
        margin + label_width, margin + 2 * row_height, edit_width, row_height * 10,
        reinterpret_cast<HMENU>(ID_PARENT_COMBO)
    );
}

void TypeTab::createConstraintPanel()
{
    // 创建约束面板
    constraint_tab_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Constraints",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建标签和编辑控件
    int label_width = 80;
    int edit_width = 200;
    int row_height = 25;
    int margin = 10;
    
    // 正则表达式
    Win32ControlFactory::createStatic(
        constraint_tab_,
        L"Regex:",
        margin, margin + 0 * row_height, label_width, row_height,
        NULL
    );
    regex_edit_ = Win32ControlFactory::createEdit(
        constraint_tab_,
        L"",
        margin + label_width, margin + 0 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_REGEX_EDIT)
    );
    
    // 测试正则按钮
    test_regex_button_ = Win32ControlFactory::createButton(
        constraint_tab_,
        L"Test",
        margin + label_width + edit_width + 5, margin + 0 * row_height, 50, row_height,
        reinterpret_cast<HMENU>(ID_TEST_REGEX_BUTTON)
    );
    
    // 最小值
    Win32ControlFactory::createStatic(
        constraint_tab_,
        L"Min Value:",
        margin, margin + 1 * row_height, label_width, row_height,
        NULL
    );
    min_edit_ = Win32ControlFactory::createEdit(
        constraint_tab_,
        L"",
        margin + label_width, margin + 1 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_MIN_EDIT)
    );
    
    // 最大值
    Win32ControlFactory::createStatic(
        constraint_tab_,
        L"Max Value:",
        margin, margin + 2 * row_height, label_width, row_height,
        NULL
    );
    max_edit_ = Win32ControlFactory::createEdit(
        constraint_tab_,
        L"",
        margin + label_width, margin + 2 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_MAX_EDIT)
    );
    
    // 枚举值列表
    Win32ControlFactory::createStatic(
        constraint_tab_,
        L"Enum Values:",
        margin, margin + 3 * row_height, label_width, row_height,
        NULL
    );
    
    // 创建枚举列表
    enum_list_ = Win32ControlFactory::createListBox(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_ENUM_LIST),
        LBS_STANDARD
    );
    
    // 创建枚举操作按钮
    add_enum_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Add Enum",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_ADD_ENUM_BUTTON)
    );
    
    del_enum_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Delete Enum",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_DEL_ENUM_BUTTON)
    );
}

void TypeTab::createExtraAttrPanel()
{
    // 创建属性列表
    attr_list_ = Win32ControlFactory::createListView(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_ATTR_LIST),
        LVS_REPORT | LVS_SINGLESEL
    );
    
    // 添加列
    LVCOLUMN lvc;
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;
    
    lvc.iSubItem = 0;
    lvc.cx = 150;
    lvc.pszText = const_cast<LPWSTR>(L"Key");
    ListView_InsertColumn(attr_list_, 0, &lvc);
    
    lvc.iSubItem = 1;
    lvc.cx = 200;
    lvc.pszText = const_cast<LPWSTR>(L"Value");
    ListView_InsertColumn(attr_list_, 1, &lvc);
    
    // 创建属性操作按钮
    add_attr_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Add Attr",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_ADD_ATTR_BUTTON)
    );
    
    del_attr_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Delete Attr",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_DEL_ATTR_BUTTON)
    );
}

void TypeTab::createExtrasPanel()
{
    // 创建extras编辑框
    extras_edit_ = Win32ControlFactory::createEdit(
        hwnd_,
        L"",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_EXTRAS_EDIT),
        ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL
    );
}

void TypeTab::updateEditPanel(const std::shared_ptr<core::model::AdvancedType>& type)
{
    if (!type) {
        // 清空编辑控件
        SetWindowText(name_edit_, L"");
        ComboBox_SetCurSel(base_type_combo_, -1);
        ComboBox_SetCurSel(parent_combo_, -1);
        SetWindowText(regex_edit_, L"");
        SetWindowText(min_edit_, L"");
        SetWindowText(max_edit_, L"");
        
        // 清空枚举列表
        ListBox_ResetContent(enum_list_);
        
        // 清空属性列表
        ListView_DeleteAllItems(attr_list_);
        
        return;
    }
    
    // 设置更新标志，防止触发变更事件
    updating_ = true;
    
    // 更新编辑控件
    SetWindowText(name_edit_, std::wstring(type->getName().begin(), type->getName().end()).c_str());
    
    // 设置基础类型
    int base_type_index = -1;
    if (type->getBaseType() == "string") {
        base_type_index = 0;
    }
    else if (type->getBaseType() == "int") {
        base_type_index = 1;
    }
    else if (type->getBaseType() == "bool") {
        base_type_index = 2;
    }
    ComboBox_SetCurSel(base_type_combo_, base_type_index);
    
    // 设置父类型
    int parent_index = 0;  // 默认为"None"
    if (!type->getParentType().empty()) {
        for (int i = 1; i < ComboBox_GetCount(parent_combo_); ++i) {
            wchar_t buffer[256];
            ComboBox_GetLBText(parent_combo_, i, buffer);
            if (std::wstring(buffer) == std::wstring(type->getParentType().begin(), type->getParentType().end())) {
                parent_index = i;
                break;
            }
        }
    }
    ComboBox_SetCurSel(parent_combo_, parent_index);
    
    // 设置正则表达式
    SetWindowText(regex_edit_, std::wstring(type->getRegexPattern().begin(), type->getRegexPattern().end()).c_str());
    
    // 设置最小值和最大值
    if (type->getMinValue().has_value()) {
        SetWindowText(min_edit_, std::to_wstring(type->getMinValue().value()).c_str());
    }
    else {
        SetWindowText(min_edit_, L"");
    }
    
    if (type->getMaxValue().has_value()) {
        SetWindowText(max_edit_, std::to_wstring(type->getMaxValue().value()).c_str());
    }
    else {
        SetWindowText(max_edit_, L"");
    }
    
    // 更新枚举列表
    ListBox_ResetContent(enum_list_);
    for (const auto& enum_value : type->getEnumValues()) {
        ListBox_AddString(enum_list_, std::wstring(enum_value.begin(), enum_value.end()).c_str());
    }
    
    // 更新属性列表
    ListView_DeleteAllItems(attr_list_);
    int index = 0;
    for (const auto& [key, value] : type->getExtraAttrs()) {
        LVITEM lvi;
        lvi.mask = LVIF_TEXT;
        lvi.iItem = index;
        lvi.iSubItem = 0;
        lvi.pszText = const_cast<LPWSTR>(std::wstring(key.begin(), key.end()).c_str());
        ListView_InsertItem(attr_list_, &lvi);
        
        ListView_SetItemText(attr_list_, index, 1, const_cast<LPWSTR>(std::wstring(value.begin(), value.end()).c_str()));
        
        ++index;
    }
    
    // 清除更新标志
    updating_ = false;
}

void TypeTab::onSelectionChanged()
{
    // 获取当前选中的类型
    auto type = getSelectedType();
    
    // 更新编辑面板
    updateEditPanel(type);
}

void TypeTab::onAddTypeClicked()
{
    // 创建新类型
    auto new_type = std::make_shared<core::model::AdvancedType>("New Type", "string");
    
    // 添加到类型列表
    types_.push_back(new_type);
    
    // 更新列表
    LVITEM lvi;
    lvi.mask = LVIF_TEXT | LVIF_PARAM;
    lvi.iItem = static_cast<int>(types_.size() - 1);
    lvi.iSubItem = 0;
    lvi.lParam = static_cast<LPARAM>(types_.size() - 1);
    lvi.pszText = const_cast<LPWSTR>(L"New Type");
    
    int index = ListView_InsertItem(type_list_, &lvi);
    
    // 设置基础类型列
    ListView_SetItemText(type_list_, index, 1, const_cast<LPWSTR>(L"string"));
    
    // 保存映射关系
    list_item_map_[index] = new_type;
    
    // 选中新类型
    ListView_SetItemState(type_list_, index, LVIS_SELECTED | LVIS_FOCUSED, LVIS_SELECTED | LVIS_FOCUSED);
    
    // 更新父类型下拉框
    fillParentCombo();
    
    // 触发变更事件
    triggerChangeCallback();
}

void TypeTab::onDeleteTypeClicked()
{
    // 获取当前选中的类型
    auto type = getSelectedType();
    if (!type) {
        return;
    }
    
    // 确认删除
    if (MessageBox(hwnd_, L"Are you sure you want to delete this type?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
        return;
    }
    
    // 获取选中的索引
    int selected = ListView_GetNextItem(type_list_, -1, LVNI_SELECTED);
    if (selected == -1) {
        return;
    }
    
    // 从列表中删除
    ListView_DeleteItem(type_list_, selected);
    
    // 从映射表中移除
    list_item_map_.erase(selected);
    
    // 从类型列表中移除
    auto it = std::find(types_.begin(), types_.end(), type);
    if (it != types_.end()) {
        types_.erase(it);
    }
    
    // 更新映射表中的索引
    for (int i = selected; i < static_cast<int>(types_.size()); ++i) {
        LVITEM lvi;
        lvi.mask = LVIF_PARAM;
        lvi.iItem = i;
        lvi.iSubItem = 0;
        ListView_GetItem(type_list_, &lvi);
        
        // 更新映射关系
        list_item_map_[i] = types_[i];
    }
    
    // 更新父类型下拉框
    fillParentCombo();
    
    // 触发变更事件
    triggerChangeCallback();
}
}

void TypeTab::onAddEnumClicked()
{
// 获取当前选中的类型
auto type = getSelectedType();
if (!type) {
    return;
}

// 创建输入对话框
wchar_t value[256] = L"";

// 创建对话框模板
HGLOBAL hgbl = GlobalAlloc(GMEM_ZEROINIT, 1024);
if (!hgbl) {
    return;
}

LPDLGTEMPLATE lpdt = (LPDLGTEMPLATE)GlobalLock(hgbl);

// 设置对话框属性
lpdt->style = WS_POPUP | WS_BORDER | WS_SYSMENU | DS_MODALFRAME | WS_CAPTION;
lpdt->cdit = 4;  // 控件数量
lpdt->x = 10;
lpdt->y = 10;
lpdt->cx = 300;
lpdt->cy = 100;

LPWORD lpw = (LPWORD)(lpdt + 1);
*lpw++ = 0;   // 没有菜单
*lpw++ = 0;   // 使用默认对话框类

// 对话框标题
LPWSTR lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Add Enum Value");
lpw += wcslen(lpwsz) + 1;

// 第一个控件：Value标签
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0082;  // 静态文本
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
lpw[1] = 0;
lpw[2] = 10;
lpw[3] = 20;
lpw[4] = 50;
lpw[5] = 20;
lpw[6] = 1;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Value:");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第二个控件：Value编辑框
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0081;  // 编辑框
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 20;
lpw[4] = 200;
lpw[5] = 20;
lpw[6] = 2;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第三个控件：OK按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | BS_DEFPUSHBUTTON;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 60;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDOK;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"OK");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第四个控件：Cancel按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE;
lpw[1] = 0;
lpw[2] = 160;
lpw[3] = 60;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDCANCEL;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Cancel");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

GlobalUnlock(hgbl);

// 对话框过程函数
auto dlgProc = [](HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam) -> INT_PTR {
    static wchar_t* value_ptr = nullptr;
    
    switch (msg) {
        case WM_INITDIALOG:
            // 保存指针
            value_ptr = (wchar_t*)lparam;
            return TRUE;
            
        case WM_COMMAND:
            if (LOWORD(wparam) == IDOK || LOWORD(wparam) == IDCANCEL) {
                if (LOWORD(wparam) == IDOK) {
                    // 获取输入的值
                    GetDlgItemText(hwnd, 2, value_ptr, 256);
                }
                EndDialog(hwnd, LOWORD(wparam));
                return TRUE;
            }
            break;
    }
    return FALSE;
};

// 显示对话框
if (DialogBoxIndirect(GetModuleHandle(NULL), (LPDLGTEMPLATE)hgbl, hwnd_, (DLGPROC)dlgProc, (LPARAM)value) == IDOK) {
    // 添加枚举值
    if (wcslen(value) > 0) {
        std::string value_str(value, value + wcslen(value));
        
        // 检查是否已存在
        auto& enum_values = type->getEnumValues();
        if (std::find(enum_values.begin(), enum_values.end(), value_str) == enum_values.end()) {
            // 添加枚举值
            type->addEnumValue(value_str);
            
            // 更新列表
            ListBox_AddString(enum_list_, value);
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
}

GlobalFree(hgbl);
}

void TypeTab::onDeleteEnumClicked()
{
// 获取当前选中的类型
auto type = getSelectedType();
if (!type) {
    return;
}

// 获取选中的枚举值
int selected = ListBox_GetCurSel(enum_list_);
if (selected == LB_ERR) {
    return;
}

// 获取枚举值
wchar_t value[256];
ListBox_GetText(enum_list_, selected, value);

// 确认删除
if (MessageBox(hwnd_, L"Are you sure you want to delete this enum value?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
    return;
}

// 删除枚举值
std::string value_str(value, value + wcslen(value));
if (type->removeEnumValue(value_str)) {
    // 从列表中删除
    ListBox_DeleteString(enum_list_, selected);
    
    // 触发变更事件
    triggerChangeCallback();
}
}

void TypeTab::onAddAttrClicked()
{
// 获取当前选中的类型
auto type = getSelectedType();
if (!type) {
    return;
}

// 创建添加属性对话框
wchar_t key[256] = L"";
wchar_t value[256] = L"";

// 创建对话框模板
HGLOBAL hgbl = GlobalAlloc(GMEM_ZEROINIT, 1024);
if (!hgbl) {
    return;
}

LPDLGTEMPLATE lpdt = (LPDLGTEMPLATE)GlobalLock(hgbl);

// 设置对话框属性
lpdt->style = WS_POPUP | WS_BORDER | WS_SYSMENU | DS_MODALFRAME | WS_CAPTION;
lpdt->cdit = 6;  // 控件数量
lpdt->x = 10;
lpdt->y = 10;
lpdt->cx = 300;
lpdt->cy = 150;

LPWORD lpw = (LPWORD)(lpdt + 1);
*lpw++ = 0;   // 没有菜单
*lpw++ = 0;   // 使用默认对话框类

// 对话框标题
LPWSTR lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Add Attribute");
lpw += wcslen(lpwsz) + 1;

// 第一个控件：Key标签
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0082;  // 静态文本
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
lpw[1] = 0;
lpw[2] = 10;
lpw[3] = 20;
lpw[4] = 50;
lpw[5] = 20;
lpw[6] = 1;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Key:");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第二个控件：Key编辑框
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0081;  // 编辑框
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 20;
lpw[4] = 200;
lpw[5] = 20;
lpw[6] = 2;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第三个控件：Value标签
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0082;  // 静态文本
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
lpw[1] = 0;
lpw[2] = 10;
lpw[3] = 50;
lpw[4] = 50;
lpw[5] = 20;
lpw[6] = 3;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Value:");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第四个控件：Value编辑框
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0081;  // 编辑框
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 50;
lpw[4] = 200;
lpw[5] = 20;
lpw[6] = 4;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第五个控件：OK按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | BS_DEFPUSHBUTTON;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 90;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDOK;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"OK");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第六个控件：Cancel按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE;
lpw[1] = 0;
lpw[2] = 160;
lpw[3] = 90;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDCANCEL;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Cancel");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

GlobalUnlock(hgbl);

// 对话框过程函数
auto dlgProc = [](HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam) -> INT_PTR {
    static wchar_t* key_ptr = nullptr;
    static wchar_t* value_ptr = nullptr;
    
    switch (msg) {
        case WM_INITDIALOG:
            // 保存指针
            key_ptr = (wchar_t*)lparam;
            value_ptr = key_ptr + 256;
            return TRUE;
            
        case WM_COMMAND:
            if (LOWORD(wparam) == IDOK || LOWORD(wparam) == IDCANCEL) {
                if (LOWORD(wparam) == IDOK) {
                    // 获取输入的值
                    GetDlgItemText(hwnd, 2, key_ptr, 256);
                    GetDlgItemText(hwnd, 4, value_ptr, 256);
                }
                EndDialog(hwnd, LOWORD(wparam));
                return TRUE;
            }
            break;
    }
    return FALSE;
};

// 显示对话框
if (DialogBoxIndirect(GetModuleHandle(NULL), (LPDLGTEMPLATE)hgbl, hwnd_, (DLGPROC)dlgProc, (LPARAM)key) == IDOK) {
    // 添加属性
    if (wcslen(key) > 0) {
        std::string key_str(key, key + wcslen(key));
        std::string value_str(value, value + wcslen(value));
        
        // 设置属性
        type->setExtraAttr(key_str, value_str);
        
        // 更新属性列表
        LVITEM lvi;
        lvi.mask = LVIF_TEXT;
        lvi.iItem = ListView_GetItemCount(attr_list_);
        lvi.iSubItem = 0;
        lvi.pszText = key;
        int index = ListView_InsertItem(attr_list_, &lvi);
        
        ListView_SetItemText(attr_list_, index, 1, value);
        
        // 触发变更事件
        triggerChangeCallback();
    }
}

GlobalFree(hgbl);
}

void TypeTab::onDeleteAttrClicked()
{
// 获取当前选中的类型
auto type = getSelectedType();
if (!type) {
    return;
}

// 获取选中的属性
int selected = ListView_GetNextItem(attr_list_, -1, LVNI_SELECTED);
if (selected == -1) {
    return;
}

// 获取属性键
wchar_t key[256];
ListView_GetItemText(attr_list_, selected, 0, key, 256);

// 确认删除
if (MessageBox(hwnd_, L"Are you sure you want to delete this attribute?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
    return;
}

// 删除属性
std::string key_str(key, key + wcslen(key));
if (type->removeExtraAttr(key_str)) {
    // 从列表中删除
    ListView_DeleteItem(attr_list_, selected);
    
    // 触发变更事件
    triggerChangeCallback();
}
}

void TypeTab::onTestRegexClicked()
{
// 获取当前选中的类型
auto type = getSelectedType();
if (!type) {
    return;
}

// 获取正则表达式
wchar_t regex_buffer[256];
GetWindowText(regex_edit_, regex_buffer, 256);
std::string regex_str(regex_buffer, regex_buffer + wcslen(regex_buffer));

if (regex_str.empty()) {
    MessageBox(hwnd_, L"Please enter a regular expression first.", L"Error", MB_OK | MB_ICONERROR);
    return;
}

// 创建测试对话框
wchar_t test_value[256] = L"";

// 创建对话框模板
HGLOBAL hgbl = GlobalAlloc(GMEM_ZEROINIT, 1024);
if (!hgbl) {
    return;
}

LPDLGTEMPLATE lpdt = (LPDLGTEMPLATE)GlobalLock(hgbl);

// 设置对话框属性
lpdt->style = WS_POPUP | WS_BORDER | WS_SYSMENU | DS_MODALFRAME | WS_CAPTION;
lpdt->cdit = 4;  // 控件数量
lpdt->x = 10;
lpdt->y = 10;
lpdt->cx = 300;
lpdt->cy = 100;

LPWORD lpw = (LPWORD)(lpdt + 1);
*lpw++ = 0;   // 没有菜单
*lpw++ = 0;   // 使用默认对话框类

// 对话框标题
LPWSTR lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Test Regular Expression");
lpw += wcslen(lpwsz) + 1;

// 第一个控件：Value标签
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0082;  // 静态文本
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
lpw[1] = 0;
lpw[2] = 10;
lpw[3] = 20;
lpw[4] = 50;
lpw[5] = 20;
lpw[6] = 1;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Value:");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第二个控件：Value编辑框
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0081;  // 编辑框
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 20;
lpw[4] = 200;
lpw[5] = 20;
lpw[6] = 2;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第三个控件：Test按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE | BS_DEFPUSHBUTTON;
lpw[1] = 0;
lpw[2] = 70;
lpw[3] = 60;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDOK;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Test");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

// 第四个控件：Close按钮
lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
lpw[0] = 0xFFFF;
lpw[1] = 0x0080;  // 按钮
lpw += 2;
lpw[0] = WS_CHILD | WS_VISIBLE;
lpw[1] = 0;
lpw[2] = 160;
lpw[3] = 60;
lpw[4] = 80;
lpw[5] = 25;
lpw[6] = IDCANCEL;  // ID
lpw += 7;
lpwsz = (LPWSTR)lpw;
wcscpy(lpwsz, L"Close");
lpw += wcslen(lpwsz) + 1;
*lpw++ = 0;  // 没有创建数据

GlobalUnlock(hgbl);

// 对话框过程函数
auto dlgProc = [](HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam) -> INT_PTR {
    static std::string* regex_ptr = nullptr;
    
    switch (msg) {
        case WM_INITDIALOG:
            // 保存指针
            regex_ptr = (std::string*)lparam;
            return TRUE;
            
        case WM_COMMAND:
            if (LOWORD(wparam) == IDOK) {
                // 获取输入的值
                wchar_t value[256];
                GetDlgItemText(hwnd, 2, value, 256);
                
                // 测试正则表达式
                try {
                    std::regex regex(*regex_ptr);
                    std::string value_str(value, value + wcslen(value));
                    
                    if (std::regex_match(value_str, regex)) {
                        MessageBox(hwnd, L"The value matches the regular expression.", L"Test Result", MB_OK | MB_ICONINFORMATION);
                    }
                    else {
                        MessageBox(hwnd, L"The value does NOT match the regular expression.", L"Test Result", MB_OK | MB_ICONWARNING);
                    }
                }
                catch (const std::regex_error& e) {
                    std::wstring error_msg = L"Invalid regular expression: ";
                    error_msg += std::wstring(e.what(), e.what() + strlen(e.what()));
                    MessageBox(hwnd, error_msg.c_str(), L"Error", MB_OK | MB_ICONERROR);
                }
                
                return TRUE;
            }
            else if (LOWORD(wparam) == IDCANCEL) {
                EndDialog(hwnd, IDCANCEL);
                return TRUE;
            }
            break;
    }
    return FALSE;
};

// 显示对话框
DialogBoxIndirect(GetModuleHandle(NULL), (LPDLGTEMPLATE)hgbl, hwnd_, (DLGPROC)dlgProc, (LPARAM)&regex_str);

GlobalFree(hgbl);
}

void TypeTab::updateTypeInList(const std::shared_ptr<core::model::AdvancedType>& type)
{
// 查找类型在列表中的位置
for (int i = 0; i < ListView_GetItemCount(type_list_); ++i) {
    auto it = list_item_map_.find(i);
    if (it != list_item_map_.end() && it->second == type) {
        // 更新名称
        ListView_SetItemText(type_list_, i, 0,
            const_cast<LPWSTR>(std::wstring(type->getName().begin(), type->getName().end()).c_str()));
        
        // 更新基础类型
        ListView_SetItemText(type_list_, i, 1,
            const_cast<LPWSTR>(std::wstring(type->getBaseType().begin(), type->getBaseType().end()).c_str()));
        
        break;
    }
}
}

void TypeTab::fillParentCombo()
{
// 清空下拉框
ComboBox_ResetContent(parent_combo_);

// 添加"None"选项
ComboBox_AddString(parent_combo_, L"None");

// 添加类型
for (const auto& type : types_) {
    ComboBox_AddString(parent_combo_, std::wstring(type->getName().begin(), type->getName().end()).c_str());
}
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor
