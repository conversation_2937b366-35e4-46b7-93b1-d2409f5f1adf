#include "win32_statusbar_manager.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32StatusBarManager::Win32StatusBarManager(HWND hwnd, HINSTANCE instance)
    : hwnd_(hwnd)
    , instance_(instance)
    , statusbar_(nullptr)
{
    // 构造函数只保存窗口句柄和实例句柄
}

Win32StatusBarManager::~Win32StatusBarManager()
{
    // 状态栏会随窗口自动销毁，不需要特殊处理
}

void Win32StatusBarManager::createStatusBar()
{
    // 确保已初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_BAR_CLASSES;
    InitCommonControlsEx(&icex);
    
    // 创建状态栏
    statusbar_ = CreateWindowEx(
        0,                          // 扩展样式
        STATUSCLASSNAME,            // 状态栏类名
        NULL,                       // 窗口标题
        WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,  // 样式
        0, 0, 0, 0,                 // 位置和大小
        hwnd_,                      // 父窗口
        reinterpret_cast<HMENU>(3000),  // 控件ID
        instance_,                  // 实例句柄
        NULL                        // 额外参数
    );
    
    if (!statusbar_) {
        throw std::runtime_error("Failed to create status bar");
    }
    
    // 设置默认分区
    std::vector<int> default_parts = {-1};  // 一个占满整个状态栏的分区
    setParts(default_parts);
}

void Win32StatusBarManager::setText(const std::wstring& text, int part)
{
    if (!statusbar_) {
        throw std::runtime_error("Status bar not created");
    }
    
    // 设置状态栏文本
    SendMessage(statusbar_, SB_SETTEXT, part, reinterpret_cast<LPARAM>(text.c_str()));
}

void Win32StatusBarManager::setParts(const std::vector<int>& parts)
{
    if (!statusbar_) {
        throw std::runtime_error("Status bar not created");
    }
    
    if (parts.empty()) {
        return;
    }
    
    // 保存分区信息
    parts_ = parts;
    
    // 设置状态栏分区
    SendMessage(statusbar_, SB_SETPARTS, parts_.size(), reinterpret_cast<LPARAM>(parts_.data()));
}

void Win32StatusBarManager::resize(int width, int height)
{
    if (!statusbar_) {
        return;
    }
    
    // 调整状态栏大小
    SendMessage(statusbar_, WM_SIZE, 0, MAKELPARAM(width, height));
    
    // 如果有多个分区，需要重新计算分区宽度
    if (parts_.size() > 1) {
        std::vector<int> new_parts(parts_.size());
        
        // 计算每个分区的宽度
        int part_width = width / parts_.size();
        for (size_t i = 0; i < parts_.size() - 1; ++i) {
            new_parts[i] = (i + 1) * part_width;
        }
        new_parts[parts_.size() - 1] = -1;  // 最后一个分区占满剩余空间
        
        // 设置新的分区
        setParts(new_parts);
    }
}

int Win32StatusBarManager::getHeight() const
{
    if (!statusbar_) {
        return 0;
    }
    
    // 获取状态栏高度
    RECT rect;
    GetWindowRect(statusbar_, &rect);
    return rect.bottom - rect.top;
}

HWND Win32StatusBarManager::getHandle() const
{
    return statusbar_;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor