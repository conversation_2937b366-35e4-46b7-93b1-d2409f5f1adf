#ifndef XSD_EDITOR_UI_WIN32_CONTROL_FACTORY_H
#define XSD_EDITOR_UI_WIN32_CONTROL_FACTORY_H

#include <windows.h>
#include <commctrl.h>
#include <string>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32控件工厂
 * 
 * 工厂类，用于创建各种Win32控件，简化控件创建过程。
 */
class Win32ControlFactory {
public:
    /**
     * @brief 创建按钮控件
     * @param parent 父窗口句柄
     * @param text 按钮文本
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @return 按钮句柄
     */
    static HWND createButton(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id);
    
    /**
     * @brief 创建编辑框控件
     * @param parent 父窗口句柄
     * @param text 初始文本
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 编辑框句柄
     */
    static HWND createEdit(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建静态文本控件
     * @param parent 父窗口句柄
     * @param text 文本内容
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @return 静态文本句柄
     */
    static HWND createStatic(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id = nullptr);
    
    /**
     * @brief 创建组合框控件
     * @param parent 父窗口句柄
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 组合框句柄
     */
    static HWND createComboBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建列表框控件
     * @param parent 父窗口句柄
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 列表框句柄
     */
    static HWND createListBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建树视图控件
     * @param parent 父窗口句柄
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 树视图句柄
     */
    static HWND createTreeView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建列表视图控件
     * @param parent 父窗口句柄
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 列表视图句柄
     */
    static HWND createListView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建标签页控件
     * @param parent 父窗口句柄
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param style 附加样式
     * @return 标签页句柄
     */
    static HWND createTabControl(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    
    /**
     * @brief 创建复选框控件
     * @param parent 父窗口句柄
     * @param text 复选框文本
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @param checked 是否选中
     * @return 复选框句柄
     */
    static HWND createCheckBox(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id, bool checked = false);
    
    /**
     * @brief 创建分组框控件
     * @param parent 父窗口句柄
     * @param text 分组框文本
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param id 控件ID
     * @return 分组框句柄
     */
    static HWND createGroupBox(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id);
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_CONTROL_FACTORY_H