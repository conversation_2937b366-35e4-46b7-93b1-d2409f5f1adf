#include "win32_toolbar_manager.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32ToolbarManager::Win32ToolbarManager(HWND hwnd, HINSTANCE instance)
    : hwnd_(hwnd)
    , instance_(instance)
    , toolbar_(nullptr)
    , button_count_(0)
{
    // 构造函数只保存窗口句柄和实例句柄
}

Win32ToolbarManager::~Win32ToolbarManager()
{
    // 工具栏会随窗口自动销毁，不需要特殊处理
}

void Win32ToolbarManager::createToolBar()
{
    // 确保已初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_BAR_CLASSES;
    InitCommonControlsEx(&icex);
    
    // 创建工具栏
    toolbar_ = CreateWindowEx(
        0,                          // 扩展样式
        TOOLBARCLASSNAME,           // 工具栏类名
        NULL,                       // 窗口标题
        WS_CHILD | WS_VISIBLE | TBSTYLE_FLAT | TBSTYLE_TOOLTIPS,  // 样式
        0, 0, 0, 0,                 // 位置和大小
        hwnd_,                      // 父窗口
        reinterpret_cast<HMENU>(2000),  // 控件ID
        instance_,                  // 实例句柄
        NULL                        // 额外参数
    );
    
    if (!toolbar_) {
        throw std::runtime_error("Failed to create toolbar");
    }
    
    // 设置工具栏按钮大小和样式
    SendMessage(toolbar_, TB_BUTTONSTRUCTSIZE, sizeof(TBBUTTON), 0);
    SendMessage(toolbar_, TB_SETBUTTONSIZE, 0, MAKELPARAM(24, 24));
    SendMessage(toolbar_, TB_SETMAXTEXTROWS, 0, 0);
}

void Win32ToolbarManager::addButton(int button_id, int icon_id, const std::wstring& tooltip, std::function<void()> handler)
{
    if (!toolbar_) {
        throw std::runtime_error("Toolbar not created");
    }
    
    // 加载图标
    HICON hIcon = LoadIcon(instance_, MAKEINTRESOURCE(icon_id));
    if (!hIcon) {
        // 如果没有找到图标资源，使用系统图标
        hIcon = LoadIcon(NULL, IDI_APPLICATION);
    }
    
    // 添加图标到工具栏的图像列表
    HIMAGELIST hImageList = reinterpret_cast<HIMAGELIST>(SendMessage(toolbar_, TB_GETIMAGELIST, 0, 0));
    if (!hImageList) {
        // 如果工具栏没有图像列表，创建一个
        hImageList = ImageList_Create(16, 16, ILC_COLOR32 | ILC_MASK, 0, 10);
        SendMessage(toolbar_, TB_SETIMAGELIST, 0, reinterpret_cast<LPARAM>(hImageList));
    }
    
    // 添加图标到图像列表
    int image_index = ImageList_AddIcon(hImageList, hIcon);
    
    // 创建按钮
    TBBUTTON tbb;
    ZeroMemory(&tbb, sizeof(TBBUTTON));
    tbb.iBitmap = image_index;
    tbb.idCommand = button_id;
    tbb.fsState = TBSTATE_ENABLED;
    tbb.fsStyle = TBSTYLE_BUTTON;
    tbb.dwData = 0;
    tbb.iString = 0;
    
    // 添加按钮到工具栏
    if (SendMessage(toolbar_, TB_ADDBUTTONS, 1, reinterpret_cast<LPARAM>(&tbb)) == 0) {
        throw std::runtime_error("Failed to add button to toolbar");
    }
    
    // 设置按钮提示文本
    if (!tooltip.empty()) {
        TTTOOLINFO ti;
        ZeroMemory(&ti, sizeof(TTTOOLINFO));
        ti.cbSize = sizeof(TTTOOLINFO);
        ti.hwnd = toolbar_;
        ti.uId = button_id;
        ti.lpszText = const_cast<LPWSTR>(tooltip.c_str());
        ti.hinst = instance_;
        
        HWND hwndTT = reinterpret_cast<HWND>(SendMessage(toolbar_, TB_GETTOOLTIPS, 0, 0));
        SendMessage(hwndTT, TTM_ADDTOOL, 0, reinterpret_cast<LPARAM>(&ti));
    }
    
    // 保存处理函数
    if (handler) {
        handlers_[button_id] = std::move(handler);
    }
    
    // 增加按钮计数
    button_count_++;
}

void Win32ToolbarManager::addSeparator()
{
    if (!toolbar_) {
        throw std::runtime_error("Toolbar not created");
    }
    
    // 创建分隔符
    TBBUTTON tbb;
    ZeroMemory(&tbb, sizeof(TBBUTTON));
    tbb.iBitmap = 0;
    tbb.idCommand = 0;
    tbb.fsState = TBSTATE_ENABLED;
    tbb.fsStyle = TBSTYLE_SEP;
    tbb.dwData = 0;
    tbb.iString = 0;
    
    // 添加分隔符到工具栏
    if (SendMessage(toolbar_, TB_ADDBUTTONS, 1, reinterpret_cast<LPARAM>(&tbb)) == 0) {
        throw std::runtime_error("Failed to add separator to toolbar");
    }
    
    // 增加按钮计数
    button_count_++;
}

void Win32ToolbarManager::enableButton(int button_id, bool enabled)
{
    if (!toolbar_) {
        throw std::runtime_error("Toolbar not created");
    }
    
    // 启用或禁用按钮
    UINT state = enabled ? TBSTATE_ENABLED : TBSTATE_INDETERMINATE;
    SendMessage(toolbar_, TB_SETSTATE, button_id, MAKELPARAM(state, 0));
}

bool Win32ToolbarManager::handleCommand(int button_id)
{
    auto it = handlers_.find(button_id);
    if (it != handlers_.end()) {
        it->second();
        return true;
    }
    
    return false;
}

void Win32ToolbarManager::resize(int width, int height)
{
    if (!toolbar_) {
        return;
    }
    
    // 调整工具栏大小
    MoveWindow(toolbar_, 0, 0, width, height, TRUE);
}

int Win32ToolbarManager::getHeight() const
{
    if (!toolbar_) {
        return 0;
    }
    
    // 获取工具栏高度
    RECT rect;
    GetWindowRect(toolbar_, &rect);
    return rect.bottom - rect.top;
}

HWND Win32ToolbarManager::getHandle() const
{
    return toolbar_;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor