#ifndef XSD_EDITOR_UI_WIN32_MAIN_WINDOW_H
#define XSD_EDITOR_UI_WIN32_MAIN_WINDOW_H

#include <windows.h>
#include <map>
#include <memory>
#include <string>
#include <functional>
#include "../base_window.h"
#include "win32_tab_manager.h"
#include "win32_menu_manager.h"
#include "win32_toolbar_manager.h"
#include "win32_statusbar_manager.h"
#include "win32_event_dispatcher.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32主窗口类
 * 
 * 实现基于Win32的用户界面，
 * 包含菜单栏、工具栏、状态栏和三个主要页面。
 */
class Win32MainWindow : public BaseWindow {
public:
    /**
     * @brief 构造函数
     */
    Win32MainWindow();

    /**
     * @brief 析构函数
     */
    virtual ~Win32MainWindow();

    // 实现BaseWindow接口
    void show() override;
    void hide() override;
    void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items) override;
    void updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) override;
    void updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices) override;
    void updateExtras(const std::vector<std::string>& extras) override;
    void showError(const std::string& message) override;
    void showInfo(const std::string& message) override;
    bool showConfirmDialog(const std::string& message) override;
    size_t addEventListener(UIEventListener listener) override;
    void removeEventListener(size_t id) override;

protected:
    void triggerEvent(UIEventType type, const std::string& data) override;
    void createMenuBar() override;
    void createToolBar() override;
    void createStatusBar() override;
    void createElementTab() override;
    void createTypeTab() override;
    void createDeviceTab() override;

private:
    // 窗口过程函数
    static LRESULT CALLBACK windowProc(HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam);
    
    // 消息处理
    void handleMessage(UINT msg, WPARAM wparam, LPARAM lparam);
    void handleCommand(WPARAM wparam, LPARAM lparam);
    void handleNotify(WPARAM wparam, LPARAM lparam);
    void handleSize(int width, int height);
    
    // 布局管理
    void updateLayout();
    
    // 成员变量
    HWND hwnd_;
    HINSTANCE instance_;
    std::unique_ptr<Win32TabManager> tab_manager_;
    std::unique_ptr<Win32MenuManager> menu_manager_;
    std::unique_ptr<Win32ToolbarManager> toolbar_manager_;
    std::unique_ptr<Win32StatusBarManager> statusbar_manager_;
    std::unique_ptr<Win32EventDispatcher> event_dispatcher_;
    
    // 事件监听器管理
    std::map<size_t, UIEventListener> listeners_;
    size_t next_listener_id_;
    
    // 状态管理
    bool is_modified_;
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_MAIN_WINDOW_H