#ifndef XSD_EDITOR_UI_WIN32_TOOLBAR_MANAGER_H
#define XSD_EDITOR_UI_WIN32_TOOLBAR_MANAGER_H

#include <windows.h>
#include <commctrl.h>
#include <string>
#include <map>
#include <functional>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32工具栏管理器
 * 
 * 负责创建和管理应用程序的工具栏。
 */
class Win32ToolbarManager {
public:
    /**
     * @brief 构造函数
     * @param hwnd 窗口句柄
     * @param instance 应用程序实例
     */
    Win32ToolbarManager(HWND hwnd, HINSTANCE instance);
    
    /**
     * @brief 析构函数
     */
    ~Win32ToolbarManager();
    
    /**
     * @brief 创建工具栏
     */
    void createToolBar();
    
    /**
     * @brief 添加工具栏按钮
     * @param button_id 按钮ID
     * @param icon_id 图标资源ID
     * @param tooltip 提示文本
     * @param handler 处理函数
     */
    void addButton(int button_id, int icon_id, const std::wstring& tooltip, std::function<void()> handler);
    
    /**
     * @brief 添加分隔符
     */
    void addSeparator();
    
    /**
     * @brief 启用或禁用按钮
     * @param button_id 按钮ID
     * @param enabled 是否启用
     */
    void enableButton(int button_id, bool enabled);
    
    /**
     * @brief 处理工具栏命令
     * @param button_id 按钮ID
     * @return 是否处理了命令
     */
    bool handleCommand(int button_id);
    
    /**
     * @brief 调整工具栏大小
     * @param width 宽度
     * @param height 高度
     */
    void resize(int width, int height);
    
    /**
     * @brief 获取工具栏高度
     * @return 工具栏高度
     */
    int getHeight() const;
    
    /**
     * @brief 获取工具栏句柄
     * @return 工具栏句柄
     */
    HWND getHandle() const;
    
private:
    HWND hwnd_;                                      // 窗口句柄
    HINSTANCE instance_;                             // 应用程序实例
    HWND toolbar_;                                   // 工具栏句柄
    std::map<int, std::function<void()>> handlers_;  // 命令处理函数映射
    int button_count_;                               // 按钮计数
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_TOOLBAR_MANAGER_H