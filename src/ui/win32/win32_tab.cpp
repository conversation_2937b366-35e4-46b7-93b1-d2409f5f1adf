#include "win32_tab.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32Tab::Win32Tab(HWND parent)
    : parent_(parent)
    , hwnd_(nullptr)
    , change_callback_(nullptr)
{
    // 基类构造函数只保存父窗口句柄
    // 具体的标签页窗口创建由子类实现
}

Win32Tab::~Win32Tab()
{
    // 基类析构函数不需要特殊处理
    // 窗口销毁由Win32系统自动处理
}

void Win32Tab::setChangeCallback(std::function<void()> callback)
{
    change_callback_ = std::move(callback);
}

HWND Win32Tab::getHandle() const
{
    return hwnd_;
}

void Win32Tab::triggerChangeCallback()
{
    if (change_callback_) {
        change_callback_();
    }
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor