#include "win32_main_window.h"
#include "element_tab.h"
#include "type_tab.h"
#include "device_tab.h"
#include <stdexcept>
#include <iostream>

namespace xsd_editor {
namespace ui {
namespace win32 {

// 窗口类名
const wchar_t* WINDOW_CLASS_NAME = L"XsdEditorMainWindow";

// 菜单ID
enum MenuID {
    ID_FILE_NEW = 101,
    ID_FILE_OPEN = 102,
    ID_FILE_SAVE = 103,
    ID_FILE_SAVE_ALL = 104,
    ID_FILE_QUIT = 105,
    
    ID_EDIT_UNDO = 201,
    ID_EDIT_REDO = 202,
    ID_EDIT_PREFERENCES = 203,
    
    ID_HELP_ABOUT = 301
};

// 工具栏按钮ID
enum ToolbarID {
    ID_TOOLBAR_SAVE = 1001
};

// 注册窗口类
bool registerWindowClass(HINSTANCE instance)
{
    WNDCLASSEX wcex;
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = Win32MainWindow::windowProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = 0;
    wcex.hInstance = instance;
    wcex.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wcex.hCursor = LoadCursor(NULL, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = NULL;
    wcex.lpszClassName = WINDOW_CLASS_NAME;
    wcex.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
    
    return RegisterClassEx(&wcex) != 0;
}

Win32MainWindow::Win32MainWindow()
    : hwnd_(nullptr)
    , instance_(GetModuleHandle(NULL))
    , next_listener_id_(0)
    , is_modified_(false)
{
    // 注册窗口类
    if (!registerWindowClass(instance_)) {
        throw std::runtime_error("Failed to register window class");
    }
    
    // 创建窗口
    hwnd_ = CreateWindow(
        WINDOW_CLASS_NAME,         // 窗口类名
        L"XSD Editor",             // 窗口标题
        WS_OVERLAPPEDWINDOW,       // 窗口样式
        CW_USEDEFAULT, CW_USEDEFAULT, 1024, 768,  // 位置和大小
        NULL,                      // 父窗口
        NULL,                      // 菜单
        instance_,                 // 实例句柄
        this                       // 额外参数
    );
    
    if (!hwnd_) {
        throw std::runtime_error("Failed to create main window");
    }
    
    // 创建事件分发器
    event_dispatcher_ = std::make_unique<Win32EventDispatcher>(hwnd_);
    
    // 创建菜单管理器
    menu_manager_ = std::make_unique<Win32MenuManager>(hwnd_);
    
    // 创建工具栏管理器
    toolbar_manager_ = std::make_unique<Win32ToolbarManager>(hwnd_, instance_);
    
    // 创建状态栏管理器
    statusbar_manager_ = std::make_unique<Win32StatusBarManager>(hwnd_, instance_);
    
    // 创建标签页管理器
    tab_manager_ = std::make_unique<Win32TabManager>(hwnd_);
    
    // 创建界面元素
    createMenuBar();
    createToolBar();
    createStatusBar();
    
    // 创建标签页
    createTypeTab();
    createDeviceTab();
    createElementTab();
    
    // 注册消息处理函数
    event_dispatcher_->registerMessageHandler(WM_SIZE, [this](WPARAM wparam, LPARAM lparam) {
        int width = LOWORD(lparam);
        int height = HIWORD(lparam);
        handleSize(width, height);
    });
    
    // 初始化布局
    RECT client_rect;
    GetClientRect(hwnd_, &client_rect);
    handleSize(client_rect.right, client_rect.bottom);
}

Win32MainWindow::~Win32MainWindow()
{
    // 所有资源会自动释放
}

void Win32MainWindow::show()
{
    ShowWindow(hwnd_, SW_SHOW);
    UpdateWindow(hwnd_);
}

void Win32MainWindow::hide()
{
    ShowWindow(hwnd_, SW_HIDE);
}

void Win32MainWindow::updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items)
{
    // 获取ElementTab
    ElementTab* element_tab = dynamic_cast<ElementTab*>(tab_manager_->getTab(0));
    if (element_tab) {
        element_tab->updateItems(items);
    }
}

void Win32MainWindow::updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types)
{
    // 获取TypeTab
    TypeTab* type_tab = dynamic_cast<TypeTab*>(tab_manager_->getTab(1));
    if (type_tab) {
        type_tab->updateTypes(types);
    }
    
    // 同步更新ElementTab中的类型列表
    ElementTab* element_tab = dynamic_cast<ElementTab*>(tab_manager_->getTab(0));
    if (element_tab) {
        element_tab->setTypesList(types);
    }
}

void Win32MainWindow::updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices)
{
    // 获取DeviceTab
    DeviceTab* device_tab = dynamic_cast<DeviceTab*>(tab_manager_->getTab(2));
    if (device_tab) {
        device_tab->updateDevices(devices);
    }
    
    // 同步更新ElementTab中的设备列表
    ElementTab* element_tab = dynamic_cast<ElementTab*>(tab_manager_->getTab(0));
    if (element_tab) {
        element_tab->setDevicesList(devices);
    }
}

void Win32MainWindow::updateExtras(const std::vector<std::string>& extras)
{
    // 获取TypeTab
    TypeTab* type_tab = dynamic_cast<TypeTab*>(tab_manager_->getTab(1));
    if (type_tab) {
        type_tab->setExtras(extras);
    }
}

void Win32MainWindow::showError(const std::string& message)
{
    // 转换为宽字符串
    std::wstring wmessage(message.begin(), message.end());
    
    // 显示错误对话框
    MessageBox(hwnd_, wmessage.c_str(), L"Error", MB_OK | MB_ICONERROR);
}

void Win32MainWindow::showInfo(const std::string& message)
{
    // 转换为宽字符串
    std::wstring wmessage(message.begin(), message.end());
    
    // 显示信息对话框
    MessageBox(hwnd_, wmessage.c_str(), L"Information", MB_OK | MB_ICONINFORMATION);
}

bool Win32MainWindow::showConfirmDialog(const std::string& message)
{
    // 转换为宽字符串
    std::wstring wmessage(message.begin(), message.end());
    
    // 显示确认对话框
    int result = MessageBox(hwnd_, wmessage.c_str(), L"Confirmation", MB_YESNO | MB_ICONQUESTION);
    
    return result == IDYES;
}

size_t Win32MainWindow::addEventListener(UIEventListener listener)
{
    if (!listener) {
        throw std::invalid_argument("Null listener");
    }
    
    size_t id = next_listener_id_++;
    listeners_[id] = std::move(listener);
    return id;
}

void Win32MainWindow::removeEventListener(size_t id)
{
    listeners_.erase(id);
}

void Win32MainWindow::triggerEvent(UIEventType type, const std::string& data)
{
    auto listeners = listeners_;  // 复制一份，防止回调中修改监听器列表
    for (const auto& [id, listener] : listeners) {
        try {
            listener(type, data);
        }
        catch (const std::exception& e) {
            std::cerr << "Error in UI event listener: " << e.what() << std::endl;
        }
    }
}

void Win32MainWindow::createMenuBar()
{
    menu_manager_->createMenuBar();
    
    // 创建文件菜单
    menu_manager_->addSubMenu(0, 1, L"File");
    menu_manager_->addMenuItem(ID_FILE_NEW, L"New", [this]() {
        triggerEvent(UIEventType::NEW_ITEM_REQUESTED, "");
    });
    menu_manager_->addMenuItem(ID_FILE_OPEN, L"Open", [this]() {
        triggerEvent(UIEventType::LOAD_REQUESTED, "");
    });
    menu_manager_->addMenuItem(ID_FILE_SAVE, L"Save", [this]() {
        triggerEvent(UIEventType::SAVE_REQUESTED, "");
    });
    menu_manager_->addMenuItem(ID_FILE_SAVE_ALL, L"Save All", [this]() {
        triggerEvent(UIEventType::SAVE_REQUESTED, "");
    });
    menu_manager_->addSeparator(1);
    menu_manager_->addMenuItem(ID_FILE_QUIT, L"Quit", [this]() {
        if (!is_modified_ || showConfirmDialog("Save changes before quitting?")) {
            DestroyWindow(hwnd_);
        }
    });
    
    // 创建编辑菜单
    menu_manager_->addSubMenu(0, 2, L"Edit");
    menu_manager_->addMenuItem(ID_EDIT_UNDO, L"Undo", nullptr);
    menu_manager_->addMenuItem(ID_EDIT_REDO, L"Redo", nullptr);
    menu_manager_->addSeparator(2);
    menu_manager_->addMenuItem(ID_EDIT_PREFERENCES, L"Preferences", nullptr);
    
    // 创建帮助菜单
    menu_manager_->addSubMenu(0, 3, L"Help");
    menu_manager_->addMenuItem(ID_HELP_ABOUT, L"About", [this]() {
        MessageBox(hwnd_, L"XSD Editor\nVersion 1.0.0\nCopyright © 2025", L"About", MB_OK | MB_ICONINFORMATION);
    });
}

void Win32MainWindow::createToolBar()
{
    toolbar_manager_->createToolBar();
    
    // 添加工具栏按钮
    toolbar_manager_->addButton(ID_TOOLBAR_SAVE, 0, L"Save", [this]() {
        triggerEvent(UIEventType::SAVE_REQUESTED, "");
    });
}

void Win32MainWindow::createStatusBar()
{
    statusbar_manager_->createStatusBar();
    statusbar_manager_->setText(L"Ready");
}

void Win32MainWindow::createElementTab()
{
    ElementTab* element_tab = new ElementTab(hwnd_, this);
    element_tab->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::ITEM_CHANGED, "");
    });
    
    tab_manager_->addTab(L"Elements", element_tab);
}

void Win32MainWindow::createTypeTab()
{
    TypeTab* type_tab = new TypeTab(hwnd_);
    type_tab->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::TYPE_CHANGED, "");
    });
    
    tab_manager_->addTab(L"Types", type_tab);
}

void Win32MainWindow::createDeviceTab()
{
    DeviceTab* device_tab = new DeviceTab(hwnd_, this);
    device_tab->setChangeCallback([this]() {
        is_modified_ = true;
        triggerEvent(UIEventType::DEVICE_CHANGED, "");
    });
    
    tab_manager_->addTab(L"Devices", device_tab);
}

LRESULT CALLBACK Win32MainWindow::windowProc(HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam)
{
    // 获取窗口实例
    Win32MainWindow* window = nullptr;
    
    if (msg == WM_NCCREATE) {
        // 保存窗口实例指针
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lparam);
        window = static_cast<Win32MainWindow*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(window));
    } else {
        // 获取窗口实例指针
        window = reinterpret_cast<Win32MainWindow*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    // 调用实例方法处理消息
    if (window) {
        window->handleMessage(msg, wparam, lparam);
    }
    
    // 默认窗口过程
    return DefWindowProc(hwnd, msg, wparam, lparam);
}

void Win32MainWindow::handleMessage(UINT msg, WPARAM wparam, LPARAM lparam)
{
    // 先尝试使用事件分发器处理消息
    if (event_dispatcher_ && event_dispatcher_->dispatchMessage(msg, wparam, lparam)) {
        return;
    }
    
    // 处理特定消息
    switch (msg) {
        case WM_COMMAND:
            handleCommand(wparam, lparam);
            break;
            
        case WM_NOTIFY:
            handleNotify(wparam, lparam);
            break;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
    }
}

void Win32MainWindow::handleCommand(WPARAM wparam, LPARAM lparam)
{
    // 获取命令ID
    int command_id = LOWORD(wparam);
    
    // 尝试使用菜单管理器处理命令
    if (menu_manager_ && menu_manager_->handleCommand(command_id)) {
        return;
    }
    
    // 尝试使用工具栏管理器处理命令
    if (toolbar_manager_ && toolbar_manager_->handleCommand(command_id)) {
        return;
    }
    
    // 将命令转发给当前选中的标签页
    if (tab_manager_) {
        Win32Tab* tab = tab_manager_->getSelectedTab();
        if (tab) {
            tab->handleCommand(wparam, lparam);
        }
    }
}

void Win32MainWindow::handleNotify(WPARAM wparam, LPARAM lparam)
{
    LPNMHDR nmhdr = reinterpret_cast<LPNMHDR>(lparam);
    
    // 将通知转发给标签页管理器
    if (tab_manager_ && nmhdr->hwndFrom == tab_manager_->getHandle()) {
        tab_manager_->handleNotify(nmhdr);
        return;
    }
    
    // 将通知转发给当前选中的标签页
    if (tab_manager_) {
        Win32Tab* tab = tab_manager_->getSelectedTab();
        if (tab) {
            tab->handleNotify(nmhdr);
        }
    }
}

void Win32MainWindow::handleSize(int width, int height)
{
    // 计算各个区域的大小
    int toolbar_height = toolbar_manager_ ? toolbar_manager_->getHeight() : 0;
    int statusbar_height = statusbar_manager_ ? statusbar_manager_->getHeight() : 0;
    
    // 调整工具栏大小
    if (toolbar_manager_) {
        toolbar_manager_->resize(width, toolbar_height);
    }
    
    // 调整状态栏大小
    if (statusbar_manager_) {
        statusbar_manager_->resize(width, statusbar_height);
    }
    
    // 调整标签页区域大小
    if (tab_manager_) {
        RECT tab_rect = {0, toolbar_height, width, height - statusbar_height};
        tab_manager_->resize(tab_rect);
    }
}

void Win32MainWindow::updateLayout()
{
    // 获取客户区大小
    RECT client_rect;
    GetClientRect(hwnd_, &client_rect);
    
    // 调用handleSize更新布局
    handleSize(client_rect.right, client_rect.bottom);
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor