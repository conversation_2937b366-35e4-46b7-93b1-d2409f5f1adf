#ifndef XSD_EDITOR_UI_WIN32_EVENT_DISPATCHER_H
#define XSD_EDITOR_UI_WIN32_EVENT_DISPATCHER_H

#include <windows.h>
#include <map>
#include <functional>
#include <tuple>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32事件分发器
 * 
 * 负责分发Win32消息到相应的处理函数。
 */
class Win32EventDispatcher {
public:
    /**
     * @brief 构造函数
     * @param hwnd 窗口句柄
     */
    Win32EventDispatcher(HWND hwnd);
    
    /**
     * @brief 析构函数
     */
    ~Win32EventDispatcher();
    
    /**
     * @brief 注册消息处理函数
     * @param msg 消息类型
     * @param handler 处理函数
     */
    void registerMessageHandler(UINT msg, std::function<void(WPARAM, LPARAM)> handler);
    
    /**
     * @brief 注册命令处理函数
     * @param command 命令ID
     * @param handler 处理函数
     */
    void registerCommandHandler(WORD command, std::function<void()> handler);
    
    /**
     * @brief 注册通知处理函数
     * @param control 控件句柄
     * @param code 通知代码
     * @param handler 处理函数
     */
    void registerNotifyHandler(HWND control, UINT code, std::function<void(LPNMHDR)> handler);
    
    /**
     * @brief 分发消息
     * @param msg 消息类型
     * @param wparam 消息参数
     * @param lparam 消息参数
     * @return 是否处理了消息
     */
    bool dispatchMessage(UINT msg, WPARAM wparam, LPARAM lparam);
    
private:
    HWND hwnd_;  // 窗口句柄
    
    // 消息处理函数映射
    std::map<UINT, std::function<void(WPARAM, LPARAM)>> message_handlers_;
    
    // 命令处理函数映射
    std::map<WORD, std::function<void()>> command_handlers_;
    
    // 通知处理函数映射，键为(控件句柄, 通知代码)元组
    std::map<std::tuple<HWND, UINT>, std::function<void(LPNMHDR)>> notify_handlers_;
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_EVENT_DISPATCHER_H