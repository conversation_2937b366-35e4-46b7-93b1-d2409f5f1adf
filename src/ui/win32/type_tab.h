#ifndef XSD_EDITOR_UI_WIN32_TYPE_TAB_H
#define XSD_EDITOR_UI_WIN32_TYPE_TAB_H

#include <windows.h>
#include <commctrl.h>
#include <vector>
#include <map>
#include <memory>
#include <string>
#include "win32_tab.h"
#include "../../core/model/type.h"

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief 类型标签页
 * 
 * 实现类型的列表显示和编辑功能。
 */
class TypeTab : public Win32Tab {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口句柄
     */
    TypeTab(HWND parent);
    
    /**
     * @brief 析构函数
     */
    virtual ~TypeTab();
    
    // 实现Win32Tab接口
    void show(bool visible) override;
    void resize(const RECT& rect) override;
    void handleNotify(LPNMHDR nmhdr) override;
    void handleCommand(WPARAM wparam, LPARAM lparam) override;
    
    /**
     * @brief 更新类型列表
     * @param types 类型列表
     */
    void updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types);
    
    /**
     * @brief 获取当前类型列表
     * @return 类型列表
     */
    std::vector<std::shared_ptr<core::model::AdvancedType>> getTypes() const;
    
    /**
     * @brief 获取选中的类型
     * @return 选中的类型
     */
    std::shared_ptr<core::model::AdvancedType> getSelectedType() const;
    
    /**
     * @brief 设置extras字段
     * @param extras extras字段的内容
     */
    void setExtras(const std::vector<std::string>& extras);
    
    /**
     * @brief 获取extras字段
     * @return extras字段的内容
     */
    std::vector<std::string> getExtras() const;
    
private:
    /**
     * @brief 创建类型列表
     */
    void createTypeList();
    
    /**
     * @brief 创建编辑面板
     */
    void createEditPanel();
    
    /**
     * @brief 创建约束编辑区域
     */
    void createConstraintPanel();
    
    /**
     * @brief 创建额外属性编辑区域
     */
    void createExtraAttrPanel();
    
    /**
     * @brief 创建extras编辑区域
     */
    void createExtrasPanel();
    
    /**
     * @brief 更新编辑面板
     * @param type 要编辑的类型
     */
    void updateEditPanel(const std::shared_ptr<core::model::AdvancedType>& type);
    
    /**
     * @brief 处理类型选择变更
     */
    void onSelectionChanged();
    
    /**
     * @brief 处理添加类型按钮点击
     */
    void onAddTypeClicked();
    
    /**
     * @brief 处理删除类型按钮点击
     */
    void onDeleteTypeClicked();
    
    /**
     * @brief 处理添加枚举值按钮点击
     */
    void onAddEnumClicked();
    
    /**
     * @brief 处理删除枚举值按钮点击
     */
    void onDeleteEnumClicked();
    
    /**
     * @brief 处理添加属性按钮点击
     */
    void onAddAttrClicked();
    
    /**
     * @brief 处理删除属性按钮点击
     */
    void onDeleteAttrClicked();
    
    /**
     * @brief 处理测试正则表达式按钮点击
     */
    void onTestRegexClicked();
    
    /**
     * @brief 更新类型在列表中的显示
     * @param type 要更新的类型
     */
    void updateTypeInList(const std::shared_ptr<core::model::AdvancedType>& type);
    
    // 成员变量
    
    // 控件句柄
    HWND type_list_;        // 类型列表
    HWND edit_panel_;       // 编辑面板
    HWND constraint_tab_;   // 约束标签页
    HWND enum_list_;        // 枚举值列表
    HWND attr_list_;        // 属性列表
    HWND extras_edit_;      // extras编辑框
    
    // 编辑控件
    HWND name_edit_;        // 名称编辑框
    HWND base_type_combo_;  // 基础类型下拉框
    HWND parent_combo_;     // 父类型下拉框
    
    // 约束编辑控件
    HWND regex_edit_;       // 正则表达式编辑框
    HWND test_regex_button_;// 测试正则按钮
    HWND min_edit_;         // 最小值编辑框
    HWND max_edit_;         // 最大值编辑框
    
    // 按钮控件
    HWND add_type_button_;  // 添加类型按钮
    HWND delete_type_button_;// 删除类型按钮
    HWND add_enum_button_;  // 添加枚举值按钮
    HWND del_enum_button_;  // 删除枚举值按钮
    HWND add_attr_button_;  // 添加属性按钮
    HWND del_attr_button_;  // 删除属性按钮
    
    // 数据
    std::vector<std::shared_ptr<core::model::AdvancedType>> types_;  // 类型列表
    std::vector<std::string> extras_;  // extras字段内容
    
    // 映射表，用于在列表项和类型之间建立关联
    std::map<int, std::shared_ptr<core::model::AdvancedType>> list_item_map_;
    
    // 标记是否正在更新UI
    bool updating_;
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_TYPE_TAB_H