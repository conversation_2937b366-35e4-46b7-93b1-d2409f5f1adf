# Win32 UI 实现架构设计

## 一、总体架构

基于对GTK UI实现的分析和Win32平台的特性，我设计了以下Win32 UI的实现架构。这个架构将保持与GTK UI相同的功能，同时充分利用Win32平台的特性。

```mermaid
graph TD
    A[BaseWindow] --> B[Win32MainWindow]
    B --> C[Win32TabManager]
    C --> D[ElementTab]
    C --> E[TypeTab]
    C --> F[DeviceTab]
    B --> G[Win32MenuManager]
    B --> H[Win32ToolbarManager]
    B --> I[Win32StatusBarManager]
    J[Win32ControlFactory] --> D
    J --> E
    J --> F
    K[Win32EventDispatcher] --> B
```

## 二、核心组件设计

### 2.1 Win32MainWindow

Win32MainWindow是整个UI的核心，它继承自BaseWindow接口，负责创建和管理主窗口、菜单、工具栏、状态栏和标签页。

```cpp
class Win32MainWindow : public BaseWindow {
public:
    Win32MainWindow();
    virtual ~Win32MainWindow();

    // 实现BaseWindow接口
    void show() override;
    void hide() override;
    void updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items) override;
    void updateTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) override;
    void updateDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices) override;
    void updateExtras(const std::vector<std::string>& extras) override;
    void showError(const std::string& message) override;
    void showInfo(const std::string& message) override;
    bool showConfirmDialog(const std::string& message) override;
    size_t addEventListener(UIEventListener listener) override;
    void removeEventListener(size_t id) override;

protected:
    void triggerEvent(UIEventType type, const std::string& data) override;
    void createMenuBar() override;
    void createToolBar() override;
    void createStatusBar() override;
    void createElementTab() override;
    void createTypeTab() override;
    void createDeviceTab() override;

private:
    // 窗口过程函数
    static LRESULT CALLBACK windowProc(HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam);
    
    // 消息处理
    void handleMessage(UINT msg, WPARAM wparam, LPARAM lparam);
    void handleCommand(WPARAM wparam, LPARAM lparam);
    void handleNotify(WPARAM wparam, LPARAM lparam);
    void handleSize(int width, int height);
    
    // 布局管理
    void updateLayout();
    
    // 成员变量
    HWND hwnd_;
    HINSTANCE instance_;
    std::unique_ptr<Win32TabManager> tab_manager_;
    std::unique_ptr<Win32MenuManager> menu_manager_;
    std::unique_ptr<Win32ToolbarManager> toolbar_manager_;
    std::unique_ptr<Win32StatusBarManager> statusbar_manager_;
    std::unique_ptr<Win32EventDispatcher> event_dispatcher_;
    
    // 事件监听器管理
    std::map<size_t, UIEventListener> listeners_;
    size_t next_listener_id_;
    
    // 状态管理
    bool is_modified_;
};
```

### 2.2 Win32TabManager

Win32TabManager负责管理标签页控件和各个标签页的内容。

```cpp
class Win32TabManager {
public:
    Win32TabManager(HWND parent);
    ~Win32TabManager();
    
    // 标签页管理
    void addTab(const std::wstring& title, Win32Tab* tab);
    void selectTab(int index);
    int getSelectedTabIndex() const;
    
    // 布局管理
    void resize(const RECT& rect);
    
    // 消息处理
    void handleNotify(LPNMHDR nmhdr);
    
    // 获取标签页控件句柄
    HWND getHandle() const;
    
private:
    HWND parent_;
    HWND tab_control_;
    std::vector<std::unique_ptr<Win32Tab>> tabs_;
};
```

### 2.3 Win32Tab基类

Win32Tab是所有标签页的基类，定义了标签页的通用接口。

```cpp
class Win32Tab {
public:
    Win32Tab(HWND parent);
    virtual ~Win32Tab();
    
    // 显示/隐藏
    virtual void show(bool visible) = 0;
    
    // 布局管理
    virtual void resize(const RECT& rect) = 0;
    
    // 消息处理
    virtual void handleNotify(LPNMHDR nmhdr) = 0;
    virtual void handleCommand(WPARAM wparam, LPARAM lparam) = 0;
    
    // 设置变更回调
    void setChangeCallback(std::function<void()> callback);
    
protected:
    HWND parent_;
    std::function<void()> change_callback_;
};
```

### 2.4 ElementTab、TypeTab和DeviceTab

这三个类继承自Win32Tab，分别实现元素、类型和设备的编辑功能。每个Tab都包含特定的UI组件和功能实现。

### 2.5 Win32ControlFactory

Win32ControlFactory是一个工厂类，用于创建各种Win32控件，简化控件创建过程。

```cpp
class Win32ControlFactory {
public:
    // 创建标准控件
    static HWND createButton(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id);
    static HWND createEdit(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    static HWND createStatic(HWND parent, const std::wstring& text, int x, int y, int width, int height, HMENU id = nullptr);
    static HWND createComboBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    static HWND createListBox(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    static HWND createTreeView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    static HWND createListView(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
    static HWND createTabControl(HWND parent, int x, int y, int width, int height, HMENU id, DWORD style = 0);
};
```

### 2.6 Win32EventDispatcher

Win32EventDispatcher负责分发Win32消息到相应的处理函数。

```cpp
class Win32EventDispatcher {
public:
    Win32EventDispatcher(HWND hwnd);
    ~Win32EventDispatcher();
    
    // 注册消息处理函数
    void registerMessageHandler(UINT msg, std::function<void(WPARAM, LPARAM)> handler);
    void registerCommandHandler(WORD command, std::function<void()> handler);
    void registerNotifyHandler(HWND control, UINT code, std::function<void(LPNMHDR)> handler);
    
    // 分发消息
    bool dispatchMessage(UINT msg, WPARAM wparam, LPARAM lparam);
    
private:
    HWND hwnd_;
    std::map<UINT, std::function<void(WPARAM, LPARAM)>> message_handlers_;
    std::map<WORD, std::function<void()>> command_handlers_;
    std::map<std::tuple<HWND, UINT>, std::function<void(LPNMHDR)>> notify_handlers_;
};
```

## 三、关键功能实现

### 3.1 树状视图实现

Win32的TreeView控件用于显示元素和设备的层级结构。实现包括：
- 创建树控件并设置样式
- 添加项目到树中
- 处理选择变更事件
- 支持拖放操作

### 3.2 编辑面板实现

编辑面板用于编辑选中项的属性，包括：
- 创建标签和编辑框
- 动态更新编辑控件内容
- 处理编辑事件

### 3.3 按钮面板实现

按钮面板包含添加、删除、移动等操作按钮：
- 创建按钮控件
- 处理按钮点击事件
- 实现添加、删除、移动等操作

### 3.4 事件处理实现

Win32的事件处理通过窗口过程函数和消息分发实现：
- 窗口过程函数接收所有消息
- 事件分发器将消息路由到相应处理函数
- 处理命令和通知消息

### 3.5 布局管理实现

Win32没有内置的布局管理器，需要手动计算控件位置：
- 监听窗口大小变化事件
- 计算各控件的位置和大小
- 更新控件布局

## 四、特殊功能实现

### 4.1 拖放操作

实现树视图中的拖放功能，用于移动设备和元素：
- 注册拖放源和目标
- 处理拖放事件
- 验证拖放操作的有效性

### 4.2 自定义绘制

为特定控件实现自定义绘制，提升用户体验：
- 处理WM_DRAWITEM消息
- 使用GDI函数绘制控件
- 支持主题和视觉样式

### 4.3 国际化支持

支持多语言界面：
- 使用资源字符串
- 支持Unicode文本
- 提供语言切换功能

## 五、与GTK UI的差异

Win32 UI与GTK UI在以下方面有所不同：

1. **消息处理机制**：Win32使用消息循环和窗口过程，而GTK使用信号和槽。
2. **控件创建**：Win32需要手动创建和管理控件，而GTK有更高级的控件创建机制。
3. **布局管理**：Win32没有内置的布局管理器，需要手动计算控件位置，而GTK有丰富的布局容器。
4. **事件处理**：Win32使用消息和通知，而GTK使用信号连接。
5. **绘制机制**：Win32使用GDI/GDI+进行绘制，而GTK使用Cairo。

## 六、实现计划

1. **基础框架实现**：
   - 创建Win32MainWindow类
   - 实现窗口过程函数和消息分发

2. **控件工厂实现**：
   - 实现Win32ControlFactory类
   - 创建各种控件的工厂方法

3. **标签页管理器实现**：
   - 实现Win32TabManager类
   - 创建和管理标签页

4. **各标签页实现**：
   - 实现ElementTab类
   - 实现TypeTab类
   - 实现DeviceTab类

5. **事件处理实现**：
   - 实现Win32EventDispatcher类
   - 连接事件和处理函数

6. **特殊功能实现**：
   - 实现拖放功能
   - 实现自定义绘制
   - 实现国际化支持

7. **测试和优化**：
   - 功能测试
   - 性能优化
   - 用户体验改进
