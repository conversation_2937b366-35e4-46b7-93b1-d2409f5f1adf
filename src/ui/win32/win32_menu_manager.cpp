#include "win32_menu_manager.h"
#include <stdexcept>

namespace xsd_editor {
namespace ui {
namespace win32 {

Win32MenuManager::Win32MenuManager(HWND hwnd)
    : hwnd_(hwnd)
    , menu_bar_(nullptr)
{
    // 构造函数只保存窗口句柄
}

Win32MenuManager::~Win32MenuManager()
{
    // 菜单会随窗口自动销毁，不需要特殊处理
}

void Win32MenuManager::createMenuBar()
{
    // 创建菜单栏
    menu_bar_ = CreateMenu();
    if (!menu_bar_) {
        throw std::runtime_error("Failed to create menu bar");
    }
    
    // 设置窗口菜单
    SetMenu(hwnd_, menu_bar_);
}

void Win32MenuManager::addMenuItem(int menu_id, const std::wstring& label, std::function<void()> handler)
{
    if (!menu_bar_) {
        throw std::runtime_error("Menu bar not created");
    }
    
    // 查找父菜单
    HMENU parent_menu = menu_bar_;
    auto it = sub_menus_.find(menu_id / 100);  // 假设菜单ID的前两位表示父菜单ID
    if (it != sub_menus_.end()) {
        parent_menu = it->second;
    }
    
    // 添加菜单项
    if (!AppendMenu(parent_menu, MF_STRING, menu_id, label.c_str())) {
        throw std::runtime_error("Failed to add menu item");
    }
    
    // 保存处理函数
    if (handler) {
        handlers_[menu_id] = std::move(handler);
    }
}

void Win32MenuManager::addSubMenu(int parent_id, int menu_id, const std::wstring& label)
{
    if (!menu_bar_) {
        throw std::runtime_error("Menu bar not created");
    }
    
    // 创建子菜单
    HMENU sub_menu = CreatePopupMenu();
    if (!sub_menu) {
        throw std::runtime_error("Failed to create sub menu");
    }
    
    // 查找父菜单
    HMENU parent_menu = menu_bar_;
    if (parent_id != 0) {
        auto it = sub_menus_.find(parent_id);
        if (it != sub_menus_.end()) {
            parent_menu = it->second;
        }
    }
    
    // 添加子菜单
    if (!AppendMenu(parent_menu, MF_POPUP, reinterpret_cast<UINT_PTR>(sub_menu), label.c_str())) {
        DestroyMenu(sub_menu);
        throw std::runtime_error("Failed to add sub menu");
    }
    
    // 保存子菜单
    sub_menus_[menu_id] = sub_menu;
}

void Win32MenuManager::addSeparator(int parent_id)
{
    if (!menu_bar_) {
        throw std::runtime_error("Menu bar not created");
    }
    
    // 查找父菜单
    HMENU parent_menu = menu_bar_;
    if (parent_id != 0) {
        auto it = sub_menus_.find(parent_id);
        if (it != sub_menus_.end()) {
            parent_menu = it->second;
        }
    }
    
    // 添加分隔符
    if (!AppendMenu(parent_menu, MF_SEPARATOR, 0, NULL)) {
        throw std::runtime_error("Failed to add separator");
    }
}

void Win32MenuManager::enableMenuItem(int menu_id, bool enabled)
{
    if (!menu_bar_) {
        throw std::runtime_error("Menu bar not created");
    }
    
    // 启用或禁用菜单项
    UINT flags = enabled ? MF_ENABLED : MF_GRAYED;
    ::EnableMenuItem(menu_bar_, menu_id, MF_BYCOMMAND | flags);
    
    // 重绘菜单
    DrawMenuBar(hwnd_);
}

bool Win32MenuManager::handleCommand(int menu_id)
{
    auto it = handlers_.find(menu_id);
    if (it != handlers_.end()) {
        it->second();
        return true;
    }
    
    return false;
}

} // namespace win32
} // namespace ui
} // namespace xsd_editor