#include "element_tab.h"
#include "win32_main_window.h"
#include "win32_control_factory.h"
#include <stdexcept>
#include <sstream>

namespace xsd_editor {
namespace ui {
namespace win32 {

// 控件ID
enum ControlID {
    ID_TREE_VIEW = 1001,
    ID_NAME_EDIT = 1002,
    ID_TYPE_EDIT = 1003,
    ID_ADV_TYPE_COMBO = 1004,
    ID_FILE_PATH_EDIT = 1005,
    ID_DEVICES_EDIT = 1006,
    ID_DEVICES_COMBO = 1007,
    ID_VERSIONS_EDIT = 1008,
    ID_IS_LIST_CHECK = 1009,
    ID_ATTR_LIST_VIEW = 1010,
    ID_ADD_BUTTON = 1011,
    ID_DELETE_BUTTON = 1012,
    ID_MOVE_UP_BUTTON = 1013,
    ID_MOVE_DOWN_BUTTON = 1014,
    ID_LEVEL_UP_BUTTON = 1015,
    ID_LEVEL_DOWN_BUTTON = 1016,
    ID_ADD_ATTR_BUTTON = 1017,
    ID_DEL_ATTR_BUTTON = 1018
};

ElementTab::ElementTab(HWND parent, Win32MainWindow* main_window)
    : Win32Tab(parent)
    , main_window_(main_window)
    , tree_view_(nullptr)
    , edit_panel_(nullptr)
    , attr_list_view_(nullptr)
    , button_panel_(nullptr)
    , updating_(false)
{
    // 创建标签页窗口
    hwnd_ = CreateWindow(
        L"STATIC",                // 窗口类名
        L"",                      // 窗口标题
        WS_CHILD | WS_VISIBLE,    // 样式
        0, 0, 0, 0,               // 位置和大小
        parent,                   // 父窗口
        NULL,                     // 菜单
        GetModuleHandle(NULL),    // 实例句柄
        NULL                      // 额外参数
    );
    
    if (!hwnd_) {
        throw std::runtime_error("Failed to create element tab window");
    }
    
    // 创建界面元素
    createTreeView();
    createEditPanel();
    createAttributesPanel();
    createButtonPanel();
}

ElementTab::~ElementTab()
{
    // 窗口会自动销毁
}

void ElementTab::show(bool visible)
{
    ShowWindow(hwnd_, visible ? SW_SHOW : SW_HIDE);
    
    // 显示或隐藏子控件
    ShowWindow(tree_view_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(edit_panel_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(attr_list_view_, visible ? SW_SHOW : SW_HIDE);
    ShowWindow(button_panel_, visible ? SW_SHOW : SW_HIDE);
}

void ElementTab::resize(const RECT& rect)
{
    // 调整标签页窗口大小
    MoveWindow(hwnd_, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, TRUE);
    
    // 计算各个区域的大小
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    int tree_width = width / 3;
    int edit_width = width - tree_width;
    int button_height = 40;
    int edit_height = height - button_height;
    
    // 调整树视图大小
    MoveWindow(tree_view_, 0, 0, tree_width, height, TRUE);
    
    // 调整编辑面板大小
    MoveWindow(edit_panel_, tree_width, 0, edit_width, edit_height / 2, TRUE);
    
    // 调整属性列表大小
    MoveWindow(attr_list_view_, tree_width, edit_height / 2, edit_width, edit_height / 2, TRUE);
    
    // 调整按钮面板大小
    MoveWindow(button_panel_, tree_width, edit_height, edit_width, button_height, TRUE);
}

void ElementTab::handleNotify(LPNMHDR nmhdr)
{
    if (nmhdr->hwndFrom == tree_view_) {
        if (nmhdr->code == TVN_SELCHANGED) {
            // 树节点选择变更
            onTreeSelectionChanged();
        }
    }
    else if (nmhdr->hwndFrom == attr_list_view_) {
        // 处理属性列表通知
    }
}

void ElementTab::handleCommand(WPARAM wparam, LPARAM lparam)
{
    // 获取控件ID和通知代码
    int control_id = LOWORD(wparam);
    int notify_code = HIWORD(wparam);
    
    // 处理编辑控件变更
    if (notify_code == EN_CHANGE) {
        if (!updating_) {
            // 更新当前选中的元素
            auto item = getSelectedItem();
            if (item) {
                // 根据控件ID更新元素属性
                if (control_id == ID_NAME_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(name_edit_, buffer, 256);
                    item->name = std::string(buffer, buffer + wcslen(buffer));
                    triggerChangeCallback();
                }
                else if (control_id == ID_TYPE_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(type_edit_, buffer, 256);
                    item->type = std::string(buffer, buffer + wcslen(buffer));
                    triggerChangeCallback();
                }
                else if (control_id == ID_FILE_PATH_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(file_path_edit_, buffer, 256);
                    item->file_path = std::string(buffer, buffer + wcslen(buffer));
                    triggerChangeCallback();
                }
                else if (control_id == ID_VERSIONS_EDIT) {
                    wchar_t buffer[256];
                    GetWindowText(versions_edit_, buffer, 256);
                    // 解析版本字符串
                    std::string versions_str(buffer, buffer + wcslen(buffer));
                    std::vector<std::string> versions;
                    std::istringstream iss(versions_str);
                    std::string version;
                    while (std::getline(iss, version, ',')) {
                        versions.push_back(version);
                    }
                    item->versions = versions;
                    triggerChangeCallback();
                }
            }
        }
    }
    // 处理下拉框选择变更
    else if (notify_code == CBN_SELCHANGE) {
        if (!updating_) {
            // 更新当前选中的元素
            auto item = getSelectedItem();
            if (item) {
                // 根据控件ID更新元素属性
                if (control_id == ID_ADV_TYPE_COMBO) {
                    int index = ComboBox_GetCurSel(adv_type_combo_);
                    if (index != CB_ERR) {
                        wchar_t buffer[256];
                        ComboBox_GetLBText(adv_type_combo_, index, buffer);
                        item->adv_type = std::string(buffer, buffer + wcslen(buffer));
                        triggerChangeCallback();
                    }
                }
                else if (control_id == ID_DEVICES_COMBO) {
                    int index = ComboBox_GetCurSel(devices_combo_);
                    if (index != CB_ERR) {
                        wchar_t buffer[256];
                        ComboBox_GetLBText(devices_combo_, index, buffer);
                        item->devices = std::string(buffer, buffer + wcslen(buffer));
                        triggerChangeCallback();
                    }
                }
            }
        }
    }
    // 处理复选框状态变更
    else if (notify_code == BN_CLICKED) {
        if (control_id == ID_IS_LIST_CHECK) {
            if (!updating_) {
                // 更新当前选中的元素
                auto item = getSelectedItem();
                if (item) {
                    item->is_list = (Button_GetCheck(is_list_check_) == BST_CHECKED);
                    triggerChangeCallback();
                }
            }
        }
        // 处理按钮点击
        else if (control_id == ID_ADD_BUTTON) {
            onAddClicked();
        }
        else if (control_id == ID_DELETE_BUTTON) {
            onDeleteClicked();
        }
        else if (control_id == ID_MOVE_UP_BUTTON) {
            onMoveUpClicked();
        }
        else if (control_id == ID_MOVE_DOWN_BUTTON) {
            onMoveDownClicked();
        }
        else if (control_id == ID_LEVEL_UP_BUTTON) {
            onLevelUpClicked();
        }
        else if (control_id == ID_LEVEL_DOWN_BUTTON) {
            onLevelDownClicked();
        }
        else if (control_id == ID_ADD_ATTR_BUTTON) {
            onAddAttrClicked();
        }
        else if (control_id == ID_DEL_ATTR_BUTTON) {
            onDeleteAttrClicked();
        }
    }
}

void ElementTab::updateItems(const std::vector<std::shared_ptr<core::model::Item>>& items)
{
    // 保存元素列表
    items_ = items;
    
    // 清空树视图
    TreeView_DeleteAllItems(tree_view_);
    tree_item_map_.clear();
    
    // 重新构建树
    for (const auto& item : items) {
        buildElementTree(NULL, item);
    }
}

std::vector<std::shared_ptr<core::model::Item>> ElementTab::getElements() const
{
    return items_;
}

std::shared_ptr<core::model::Item> ElementTab::getSelectedItem() const
{
    // 获取当前选中的树节点
    HTREEITEM selected = TreeView_GetSelection(tree_view_);
    if (!selected) {
        return nullptr;
    }
    
    // 查找对应的元素
    auto it = tree_item_map_.find(selected);
    if (it != tree_item_map_.end()) {
        return it->second;
    }
    
    return nullptr;
}

void ElementTab::setTypesList(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types)
{
    // 保存类型列表
    types_list_ = types;
    
    // 更新高级类型下拉框
    fillAdvTypeCombo();
}

void ElementTab::setDevicesList(const std::vector<std::shared_ptr<core::model::Device>>& devices)
{
    // 保存设备列表
    devices_list_ = devices;
    
    // 更新设备下拉框
    fillDevicesCombo();
}

void ElementTab::createTreeView()
{
    // 创建树视图
    tree_view_ = Win32ControlFactory::createTreeView(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_TREE_VIEW),
        TVS_HASBUTTONS | TVS_HASLINES | TVS_LINESATROOT | TVS_SHOWSELALWAYS
    );
}

void ElementTab::createEditPanel()
{
    // 创建编辑面板
    edit_panel_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Element Properties",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建标签和编辑控件
    int label_width = 80;
    int edit_width = 200;
    int row_height = 25;
    int margin = 10;
    
    // 名称
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Name:",
        margin, margin + 0 * row_height, label_width, row_height,
        NULL
    );
    name_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 0 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_NAME_EDIT)
    );
    
    // 类型
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Type:",
        margin, margin + 1 * row_height, label_width, row_height,
        NULL
    );
    type_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 1 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_TYPE_EDIT)
    );
    
    // 高级类型
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Adv Type:",
        margin, margin + 2 * row_height, label_width, row_height,
        NULL
    );
    adv_type_combo_ = Win32ControlFactory::createComboBox(
        edit_panel_,
        margin + label_width, margin + 2 * row_height, edit_width, row_height * 10,
        reinterpret_cast<HMENU>(ID_ADV_TYPE_COMBO)
    );
    
    // 文件路径
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"File Path:",
        margin, margin + 3 * row_height, label_width, row_height,
        NULL
    );
    file_path_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 3 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_FILE_PATH_EDIT)
    );
    
    // 设备
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Devices:",
        margin, margin + 4 * row_height, label_width, row_height,
        NULL
    );
    devices_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 4 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_DEVICES_EDIT)
    );
    
    // 设备下拉框
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Device:",
        margin, margin + 5 * row_height, label_width, row_height,
        NULL
    );
    devices_combo_ = Win32ControlFactory::createComboBox(
        edit_panel_,
        margin + label_width, margin + 5 * row_height, edit_width, row_height * 10,
        reinterpret_cast<HMENU>(ID_DEVICES_COMBO)
    );
    
    // 版本
    Win32ControlFactory::createStatic(
        edit_panel_,
        L"Versions:",
        margin, margin + 6 * row_height, label_width, row_height,
        NULL
    );
    versions_edit_ = Win32ControlFactory::createEdit(
        edit_panel_,
        L"",
        margin + label_width, margin + 6 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_VERSIONS_EDIT)
    );
    
    // 是否列表
    is_list_check_ = Win32ControlFactory::createCheckBox(
        edit_panel_,
        L"Is List",
        margin + label_width, margin + 7 * row_height, edit_width, row_height,
        reinterpret_cast<HMENU>(ID_IS_LIST_CHECK)
    );
}

void ElementTab::createAttributesPanel()
{
    // 创建属性列表
    attr_list_view_ = Win32ControlFactory::createListView(
        hwnd_,
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        reinterpret_cast<HMENU>(ID_ATTR_LIST_VIEW),
        LVS_REPORT | LVS_SINGLESEL
    );
    
    // 添加列
    LVCOLUMN lvc;
    lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;
    
    lvc.iSubItem = 0;
    lvc.cx = 150;
    lvc.pszText = const_cast<LPWSTR>(L"Key");
    ListView_InsertColumn(attr_list_view_, 0, &lvc);
    
    lvc.iSubItem = 1;
    lvc.cx = 300;
    lvc.pszText = const_cast<LPWSTR>(L"Value");
    ListView_InsertColumn(attr_list_view_, 1, &lvc);
    
    // 创建属性操作按钮
    add_attr_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Add Attr",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_ADD_ATTR_BUTTON)
    );
    
    del_attr_button_ = Win32ControlFactory::createButton(
        hwnd_,
        L"Delete Attr",
        0, 0, 80, 25,
        reinterpret_cast<HMENU>(ID_DEL_ATTR_BUTTON)
    );
}

void ElementTab::createButtonPanel()
{
    // 创建按钮面板
    button_panel_ = Win32ControlFactory::createGroupBox(
        hwnd_,
        L"Operations",
        0, 0, 0, 0,  // 位置和大小将在resize中设置
        NULL
    );
    
    // 创建按钮
    int button_width = 100;
    int button_height = 25;
    int margin = 10;
    
    add_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Add",
        margin, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_ADD_BUTTON)
    );
    
    delete_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Delete",
        margin + button_width + margin, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_DELETE_BUTTON)
    );
    
    move_up_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Move Up",
        margin + (button_width + margin) * 2, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_MOVE_UP_BUTTON)
    );
    
    move_down_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Move Down",
        margin + (button_width + margin) * 3, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_MOVE_DOWN_BUTTON)
    );
    
    level_up_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Level Up",
        margin + (button_width + margin) * 4, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_LEVEL_UP_BUTTON)
    );
    
    level_down_button_ = Win32ControlFactory::createButton(
        button_panel_,
        L"Level Down",
        margin + (button_width + margin) * 5, margin, button_width, button_height,
        reinterpret_cast<HMENU>(ID_LEVEL_DOWN_BUTTON)
    );
}

void ElementTab::updateEditPanel(const std::shared_ptr<core::model::Item>& item)
{
    if (!item) {
        // 清空编辑控件
        SetWindowText(name_edit_, L"");
        SetWindowText(type_edit_, L"");
        ComboBox_SetCurSel(adv_type_combo_, -1);
        SetWindowText(file_path_edit_, L"");
        SetWindowText(devices_edit_, L"");
        ComboBox_SetCurSel(devices_combo_, -1);
        SetWindowText(versions_edit_, L"");
        Button_SetCheck(is_list_check_, BST_UNCHECKED);
        return;
    }
    
    // 设置更新标志，防止触发变更事件
    updating_ = true;
    
    // 更新编辑控件
    SetWindowText(name_edit_, std::wstring(item->name.begin(), item->name.end()).c_str());
    SetWindowText(type_edit_, std::wstring(item->type.begin(), item->type.end()).c_str());
    
    // 设置高级类型
    int adv_type_index = -1;
    for (int i = 0; i < ComboBox_GetCount(adv_type_combo_); ++i) {
        wchar_t buffer[256];
        ComboBox_GetLBText(adv_type_combo_, i, buffer);
        if (std::wstring(buffer) == std::wstring(item->adv_type.begin(), item->adv_type.end())) {
            adv_type_index = i;
            break;
        }
    }
    ComboBox_SetCurSel(adv_type_combo_, adv_type_index);
    
    SetWindowText(file_path_edit_, std::wstring(item->file_path.begin(), item->file_path.end()).c_str());
    SetWindowText(devices_edit_, std::wstring(item->devices.begin(), item->devices.end()).c_str());
    
    // 设置设备
    int device_index = -1;
    for (int i = 0; i < ComboBox_GetCount(devices_combo_); ++i) {
        wchar_t buffer[256];
        ComboBox_GetLBText(devices_combo_, i, buffer);
        if (std::wstring(buffer) == std::wstring(item->devices.begin(), item->devices.end())) {
            device_index = i;
            break;
        }
    }
    ComboBox_SetCurSel(devices_combo_, device_index);
    
    // 设置版本
    std::string versions_str;
    for (size_t i = 0; i < item->versions.size(); ++i) {
        if (i > 0) {
            versions_str += ",";
        }
        versions_str += item->versions[i];
    }
    SetWindowText(versions_edit_, std::wstring(versions_str.begin(), versions_str.end()).c_str());
    
    // 设置是否列表
    Button_SetCheck(is_list_check_, item->is_list ? BST_CHECKED : BST_UNCHECKED);
    
    // 清除更新标志
    updating_ = false;
}

void ElementTab::updateAttributesList(const std::shared_ptr<core::model::Item>& item)
{
    // 清空属性列表
    ListView_DeleteAllItems(attr_list_view_);
    
    if (!item) {
        return;
    }
    
    // 添加属性
    int index = 0;
    for (const auto& [key, value] : item->attributes) {
        LVITEM lvi;
        lvi.mask = LVIF_TEXT;
        lvi.iItem = index;
        lvi.iSubItem = 0;
        lvi.pszText = const_cast<LPWSTR>(std::wstring(key.begin(), key.end()).c_str());
        ListView_InsertItem(attr_list_view_, &lvi);
        
        ListView_SetItemText(attr_list_view_, index, 1, const_cast<LPWSTR>(std::wstring(value.begin(), value.end()).c_str()));
        
        ++index;
    }
}

void ElementTab::fillAdvTypeCombo()
{
    // 清空下拉框
    ComboBox_ResetContent(adv_type_combo_);
    
    // 添加类型
    for (const auto& type : types_list_) {
        ComboBox_AddString(adv_type_combo_, std::wstring(type->name.begin(), type->name.end()).c_str());
    }
}

void ElementTab::fillDevicesCombo()
{
    // 清空下拉框
    ComboBox_ResetContent(devices_combo_);
    
    // 添加设备
    for (const auto& device : devices_list_) {
        addDeviceToCombo(device, 0);
    }
}

void ElementTab::addDeviceToCombo(const std::shared_ptr<core::model::Device>& device, int level)
{
    // 创建缩进
    std::wstring indent;
    for (int i = 0; i < level; ++i) {
        indent += L"  ";
    }
    
    // 添加设备
    std::wstring name = indent + std::wstring(device->name.begin(), device->name.end());
    ComboBox_AddString(devices_combo_, name.c_str());
    
    // 递归添加子设备
    for (const auto& child : device->children) {
        addDeviceToCombo(child, level + 1);
    }
}

void ElementTab::buildElementTree(HTREEITEM parent_item, const std::shared_ptr<core::model::Item>& item)
{
    // 创建树节点
    TVINSERTSTRUCT tvis;
    tvis.hParent = parent_item;
    tvis.hInsertAfter = TVI_LAST;
    tvis.item.mask = TVIF_TEXT | TVIF_PARAM;
    tvis.item.pszText = const_cast<LPWSTR>(std::wstring(item->name.begin(), item->name.end()).c_str());
    tvis.item.lParam = reinterpret_cast<LPARAM>(item.get());
    
    HTREEITEM tree_item = TreeView_InsertItem(tree_view_, &tvis);
    
    // 保存树节点和元素的映射关系
    tree_item_map_[tree_item] = item;
    
    // 递归添加子元素
    for (const auto& child : item->children) {
        buildElementTree(tree_item, child);
    }
    
    // 展开节点
    TreeView_Expand(tree_view_, tree_item, TVE_EXPAND);
}

HTREEITEM ElementTab::findElementInTree(const std::shared_ptr<core::model::Item>& item)
{
    // 从根节点开始查找
    HTREEITEM root = TreeView_GetRoot(tree_view_);
    while (root) {
        HTREEITEM found = findElementRecursive(root, item);
        if (found) {
            return found;
        }
        root = TreeView_GetNextSibling(tree_view_, root);
    }
    
    return NULL;
}

HTREEITEM ElementTab::findElementRecursive(HTREEITEM parent_item, const std::shared_ptr<core::model::Item>& item)
{
    // 检查当前节点
    auto it = tree_item_map_.find(parent_item);
    if (it != tree_item_map_.end() && it->second == item) {
        return parent_item;
    }
    
    // 递归查找子节点
    HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
    while (child) {
        HTREEITEM found = findElementRecursive(child, item);
        if (found) {
            return found;
        }
        child = TreeView_GetNextSibling(tree_view_, child);
    }
    
    return NULL;
}

void ElementTab::onTreeSelectionChanged()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    
    // 更新编辑面板
    updateEditPanel(item);
    
    // 更新属性列表
    updateAttributesList(item);
}

void ElementTab::onAddClicked()
{
    // 获取当前选中的元素
    auto parent = getSelectedItem();
    
    // 创建新元素
    auto new_item = std::make_shared<core::model::Item>();
    new_item->name = "New Item";
    
    if (parent) {
        // 添加为子元素
        parent->children.push_back(new_item);
        
        // 更新树视图
        HTREEITEM parent_item = findElementInTree(parent);
        if (parent_item) {
            buildElementTree(parent_item, new_item);
        }
    } else {
        // 添加为根元素
        items_.push_back(new_item);
        
        // 更新树视图
        buildElementTree(NULL, new_item);
    }
    
    // 触发变更事件
    triggerChangeCallback();
}

void ElementTab::onDeleteClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 确认删除
    if (MessageBox(hwnd_, L"Are you sure you want to delete this item?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
        return;
    }
    
    // 从树视图中删除
    HTREEITEM tree_item = findElementInTree(item);
    if (tree_item) {
        TreeView_DeleteItem(tree_view_, tree_item);
        
        // 从映射表中移除
        tree_item_map_.erase(tree_item);
        
        // 从父元素中移除
        auto parent = item->getParent().lock();
        if (parent) {
            parent->removeChild(item);
        } else {
            // 从根元素列表中移除
            auto it = std::find(items_.begin(), items_.end(), item);
            if (it != items_.end()) {
                items_.erase(it);
            }
        }
        
        // 触发变更事件
        triggerChangeCallback();
    }
}

void ElementTab::onMoveUpClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 获取父元素
    auto parent = item->getParent().lock();
    if (!parent) {
        // 根元素
        auto it = std::find(items_.begin(), items_.end(), item);
        if (it != items_.begin()) {
            // 交换位置
            auto prev_it = it - 1;
            std::iter_swap(it, prev_it);
            
            // 更新树视图
            updateItems(items_);
            
            // 重新选中元素
            HTREEITEM tree_item = findElementInTree(item);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    } else {
        // 子元素
        auto& siblings = parent->children;
        auto it = std::find(siblings.begin(), siblings.end(), item);
        if (it != siblings.begin()) {
            // 交换位置
            auto prev_it = it - 1;
            std::iter_swap(it, prev_it);
            
            // 更新树视图
            HTREEITEM parent_item = findElementInTree(parent);
            if (parent_item) {
                // 删除所有子节点
                HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
                while (child) {
                    HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                    TreeView_DeleteItem(tree_view_, child);
                    child = next;
                }
                
                // 重新添加子节点
                for (const auto& child : parent->children) {
                    buildElementTree(parent_item, child);
                }
                
                // 展开父节点
                TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
                
                // 重新选中元素
                HTREEITEM tree_item = findElementInTree(item);
                if (tree_item) {
                    TreeView_SelectItem(tree_view_, tree_item);
                }
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
}

void ElementTab::onMoveDownClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 获取父元素
    auto parent = item->getParent().lock();
    if (!parent) {
        // 根元素
        auto it = std::find(items_.begin(), items_.end(), item);
        if (it != items_.end() - 1) {
            // 交换位置
            auto next_it = it + 1;
            std::iter_swap(it, next_it);
            
            // 更新树视图
            updateItems(items_);
            
            // 重新选中元素
            HTREEITEM tree_item = findElementInTree(item);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    } else {
        // 子元素
        auto& siblings = parent->children;
        auto it = std::find(siblings.begin(), siblings.end(), item);
        if (it != siblings.end() - 1) {
            // 交换位置
            auto next_it = it + 1;
            std::iter_swap(it, next_it);
            
            // 更新树视图
            HTREEITEM parent_item = findElementInTree(parent);
            if (parent_item) {
                // 删除所有子节点
                HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
                while (child) {
                    HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                    TreeView_DeleteItem(tree_view_, child);
                    child = next;
                }
                
                // 重新添加子节点
                for (const auto& child : parent->children) {
                    buildElementTree(parent_item, child);
                }
                
                // 展开父节点
                TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
                
                // 重新选中元素
                HTREEITEM tree_item = findElementInTree(item);
                if (tree_item) {
                    TreeView_SelectItem(tree_view_, tree_item);
                }
            }
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
}

void ElementTab::onLevelUpClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 获取父元素和祖父元素
    auto parent = item->getParent().lock();
    if (!parent) {
        // 根元素无法上移层级
        return;
    }
    
    auto grandparent = parent->getParent().lock();
    if (!grandparent) {
        // 父元素是根元素，将当前元素移动到根元素列表
        // 从父元素中移除
        parent->removeChild(item);
        
        // 添加到根元素列表
        items_.push_back(item);
        
        // 更新树视图
        updateItems(items_);
        
        // 重新选中元素
        HTREEITEM tree_item = findElementInTree(item);
        if (tree_item) {
            TreeView_SelectItem(tree_view_, tree_item);
        }
        
        // 触发变更事件
        triggerChangeCallback();
    } else {
        // 将当前元素移动到祖父元素下
        // 从父元素中移除
        parent->removeChild(item);
        
        // 添加到祖父元素下
        grandparent->addChild(item);
        
        // 更新树视图
        HTREEITEM grandparent_item = findElementInTree(grandparent);
        if (grandparent_item) {
            // 删除所有子节点
            HTREEITEM child = TreeView_GetChild(tree_view_, grandparent_item);
            while (child) {
                HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                TreeView_DeleteItem(tree_view_, child);
                child = next;
            }
            
            // 重新添加子节点
            for (const auto& child : grandparent->children) {
                buildElementTree(grandparent_item, child);
            }
            
            // 展开祖父节点
            TreeView_Expand(tree_view_, grandparent_item, TVE_EXPAND);
            
            // 重新选中元素
            HTREEITEM tree_item = findElementInTree(item);
            if (tree_item) {
                TreeView_SelectItem(tree_view_, tree_item);
            }
        }
        
        // 触发变更事件
        triggerChangeCallback();
    }
}

void ElementTab::onLevelDownClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 获取父元素
    auto parent = item->getParent().lock();
    
    // 获取当前元素在父元素中的位置
    std::vector<std::shared_ptr<core::model::Item>> siblings;
    size_t index = 0;
    
    if (parent) {
        siblings = parent->children;
        auto it = std::find(siblings.begin(), siblings.end(), item);
        if (it != siblings.end()) {
            index = std::distance(siblings.begin(), it);
        }
    } else {
        siblings = items_;
        auto it = std::find(siblings.begin(), siblings.end(), item);
        if (it != siblings.end()) {
            index = std::distance(siblings.begin(), it);
        }
    }
    
    // 检查是否有前一个兄弟元素
    if (index == 0) {
        // 没有前一个兄弟元素，无法下移层级
        return;
    }
    
    // 获取前一个兄弟元素
    auto prev_sibling = siblings[index - 1];
    
    // 从当前位置移除
    if (parent) {
        parent->removeChild(item);
    } else {
        items_.erase(std::find(items_.begin(), items_.end(), item));
    }
    
    // 添加到前一个兄弟元素下
    prev_sibling->addChild(item);
    
    // 更新树视图
    if (parent) {
        HTREEITEM parent_item = findElementInTree(parent);
        if (parent_item) {
            // 删除所有子节点
            HTREEITEM child = TreeView_GetChild(tree_view_, parent_item);
            while (child) {
                HTREEITEM next = TreeView_GetNextSibling(tree_view_, child);
                TreeView_DeleteItem(tree_view_, child);
                child = next;
            }
            
            // 重新添加子节点
            for (const auto& child : parent->children) {
                buildElementTree(parent_item, child);
            }
            
            // 展开父节点
            TreeView_Expand(tree_view_, parent_item, TVE_EXPAND);
        }
    } else {
        // 更新根元素
        updateItems(items_);
    }
    
    // 展开前一个兄弟元素
    HTREEITEM prev_sibling_item = findElementInTree(prev_sibling);
    if (prev_sibling_item) {
        TreeView_Expand(tree_view_, prev_sibling_item, TVE_EXPAND);
    }
    
    // 重新选中元素
    HTREEITEM tree_item = findElementInTree(item);
    if (tree_item) {
        TreeView_SelectItem(tree_view_, tree_item);
    }
    
    // 触发变更事件
    triggerChangeCallback();
}

void ElementTab::onAddAttrClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 创建添加属性对话框
    // 这里使用简单的输入框
    wchar_t key[256] = L"";
    wchar_t value[256] = L"";
    
    // 创建对话框模板
    HGLOBAL hgbl = GlobalAlloc(GMEM_ZEROINIT, 1024);
    if (!hgbl) {
        return;
    }
    
    LPDLGTEMPLATE lpdt = (LPDLGTEMPLATE)GlobalLock(hgbl);
    
    // 设置对话框属性
    lpdt->style = WS_POPUP | WS_BORDER | WS_SYSMENU | DS_MODALFRAME | WS_CAPTION;
    lpdt->cdit = 6;  // 控件数量
    lpdt->x = 10;
    lpdt->y = 10;
    lpdt->cx = 300;
    lpdt->cy = 150;
    
    LPWORD lpw = (LPWORD)(lpdt + 1);
    *lpw++ = 0;   // 没有菜单
    *lpw++ = 0;   // 使用默认对话框类
    
    // 对话框标题
    LPWSTR lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"Add Attribute");
    lpw += wcslen(lpwsz) + 1;
    
    // 第一个控件：Key标签
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0082;  // 静态文本
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
    lpw[1] = 0;
    lpw[2] = 10;
    lpw[3] = 20;
    lpw[4] = 50;
    lpw[5] = 20;
    lpw[6] = 1;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"Key:");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    // 第二个控件：Key编辑框
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0081;  // 编辑框
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
    lpw[1] = 0;
    lpw[2] = 70;
    lpw[3] = 20;
    lpw[4] = 200;
    lpw[5] = 20;
    lpw[6] = 2;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    // 第三个控件：Value标签
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0082;  // 静态文本
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE | SS_LEFT;
    lpw[1] = 0;
    lpw[2] = 10;
    lpw[3] = 50;
    lpw[4] = 50;
    lpw[5] = 20;
    lpw[6] = 3;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"Value:");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    // 第四个控件：Value编辑框
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0081;  // 编辑框
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL;
    lpw[1] = 0;
    lpw[2] = 70;
    lpw[3] = 50;
    lpw[4] = 200;
    lpw[5] = 20;
    lpw[6] = 4;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    // 第五个控件：OK按钮
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0080;  // 按钮
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE | BS_DEFPUSHBUTTON;
    lpw[1] = 0;
    lpw[2] = 70;
    lpw[3] = 90;
    lpw[4] = 80;
    lpw[5] = 25;
    lpw[6] = IDOK;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"OK");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    // 第六个控件：Cancel按钮
    lpw = (LPWORD)(((LPBYTE)lpw + 3) & ~3);  // 对齐
    lpw[0] = 0xFFFF;
    lpw[1] = 0x0080;  // 按钮
    lpw += 2;
    lpw[0] = WS_CHILD | WS_VISIBLE;
    lpw[1] = 0;
    lpw[2] = 160;
    lpw[3] = 90;
    lpw[4] = 80;
    lpw[5] = 25;
    lpw[6] = IDCANCEL;  // ID
    lpw += 7;
    lpwsz = (LPWSTR)lpw;
    wcscpy(lpwsz, L"Cancel");
    lpw += wcslen(lpwsz) + 1;
    *lpw++ = 0;  // 没有创建数据
    
    GlobalUnlock(hgbl);
    
    // 对话框过程函数
    auto dlgProc = [](HWND hwnd, UINT msg, WPARAM wparam, LPARAM lparam) -> INT_PTR {
        static wchar_t* key_ptr = nullptr;
        static wchar_t* value_ptr = nullptr;
        
        switch (msg) {
            case WM_INITDIALOG:
                // 保存指针
                key_ptr = (wchar_t*)lparam;
                value_ptr = key_ptr + 256;
                return TRUE;
                
            case WM_COMMAND:
                if (LOWORD(wparam) == IDOK || LOWORD(wparam) == IDCANCEL)) {
                    if (LOWORD(wparam) == IDOK) {
                        // 获取输入的值
                        GetDlgItemText(hwnd, 2, key_ptr, 256);
                        GetDlgItemText(hwnd, 4, value_ptr, 256);
                    }
                    EndDialog(hwnd, LOWORD(wparam));
                    return TRUE;
                }
                break;
        }
        return FALSE;
    };
    
    // 显示对话框
    if (DialogBoxIndirect(GetModuleHandle(NULL), (LPDLGTEMPLATE)hgbl, hwnd_, (DLGPROC)dlgProc, (LPARAM)key) == IDOK) {
        // 添加属性
        if (wcslen(key) > 0) {
            std::string key_str(key, key + wcslen(key));
            std::string value_str(value, value + wcslen(value));
            
            item->setAttribute(key_str, value_str);
            
            // 更新属性列表
            updateAttributesList(item);
            
            // 触发变更事件
            triggerChangeCallback();
        }
    }
    
    GlobalFree(hgbl);
}

void ElementTab::onDeleteAttrClicked()
{
    // 获取当前选中的元素
    auto item = getSelectedItem();
    if (!item) {
        return;
    }
    
    // 获取选中的属性
    int selected = ListView_GetNextItem(attr_list_view_, -1, LVNI_SELECTED);
    if (selected == -1) {
        return;
    }
    
    // 获取属性键
    wchar_t key[256];
    ListView_GetItemText(attr_list_view_, selected, 0, key, 256);
    
    // 确认删除
    if (MessageBox(hwnd_, L"Are you sure you want to delete this attribute?", L"Confirm", MB_YESNO | MB_ICONQUESTION) != IDYES) {
        return;
    }
    
    // 删除属性
    std::string key_str(key, key + wcslen(key));
    item->removeAttribute(key_str);
    
    // 更新属性列表
    updateAttributesList(item);
    
    // 触发变更事件
    triggerChangeCallback();
}

bool ElementTab::isValidMove(const std::shared_ptr<core::model::Item>& source,
                           const std::shared_ptr<core::model::Item>& target,
                           bool as_sibling) const
{
    // 检查源元素是否为空
    if (!source) {
        return false;
    }
    
    // 检查目标元素是否为空
    if (!target) {
        // 如果目标为空，只有作为根元素才有效
        return as_sibling;
    }
    
    // 检查是否将元素移动到自身下
    if (source == target) {
        return false;
    }
    
    // 检查是否将元素移动到其子元素下
    if (!as_sibling) {
        // 检查目标是否为源的后代
        auto current = target;
        while (current) {
            auto parent = current->getParent().lock();
            if (parent == source) {
                return false;
            }
            current = parent;
        }
    }
    
    return true;
}
}
}
}
