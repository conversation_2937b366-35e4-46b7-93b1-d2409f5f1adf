#ifndef XSD_EDITOR_UI_WIN32_TAB_H
#define XSD_EDITOR_UI_WIN32_TAB_H

#include <windows.h>
#include <functional>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32标签页基类
 * 
 * 所有标签页的基类，定义了标签页的通用接口。
 */
class Win32Tab {
public:
    /**
     * @brief 构造函数
     * @param parent 父窗口句柄
     */
    Win32Tab(HWND parent);
    
    /**
     * @brief 虚析构函数
     */
    virtual ~Win32Tab();
    
    /**
     * @brief 显示或隐藏标签页
     * @param visible 是否可见
     */
    virtual void show(bool visible) = 0;
    
    /**
     * @brief 调整标签页大小
     * @param rect 新的矩形区域
     */
    virtual void resize(const RECT& rect) = 0;
    
    /**
     * @brief 处理通知消息
     * @param nmhdr 通知消息头
     */
    virtual void handleNotify(LPNMHDR nmhdr) = 0;
    
    /**
     * @brief 处理命令消息
     * @param wparam 命令参数
     * @param lparam 附加参数
     */
    virtual void handleCommand(WPARAM wparam, LPARAM lparam) = 0;
    
    /**
     * @brief 设置变更回调
     * @param callback 回调函数
     */
    void setChangeCallback(std::function<void()> callback);
    
    /**
     * @brief 获取标签页窗口句柄
     * @return 窗口句柄
     */
    HWND getHandle() const;
    
protected:
    HWND parent_;                          // 父窗口句柄
    HWND hwnd_;                            // 标签页窗口句柄
    std::function<void()> change_callback_; // 变更回调函数
    
    /**
     * @brief 触发变更回调
     */
    void triggerChangeCallback();
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_TAB_H