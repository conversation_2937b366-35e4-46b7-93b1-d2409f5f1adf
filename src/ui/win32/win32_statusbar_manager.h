#ifndef XSD_EDITOR_UI_WIN32_STATUSBAR_MANAGER_H
#define XSD_EDITOR_UI_WIN32_STATUSBAR_MANAGER_H

#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>

namespace xsd_editor {
namespace ui {
namespace win32 {

/**
 * @brief Win32状态栏管理器
 * 
 * 负责创建和管理应用程序的状态栏。
 */
class Win32StatusBarManager {
public:
    /**
     * @brief 构造函数
     * @param hwnd 窗口句柄
     * @param instance 应用程序实例
     */
    Win32StatusBarManager(HWND hwnd, HINSTANCE instance);
    
    /**
     * @brief 析构函数
     */
    ~Win32StatusBarManager();
    
    /**
     * @brief 创建状态栏
     */
    void createStatusBar();
    
    /**
     * @brief 设置状态栏文本
     * @param text 状态栏文本
     * @param part 状态栏分区索引，默认为0
     */
    void setText(const std::wstring& text, int part = 0);
    
    /**
     * @brief 设置状态栏分区
     * @param parts 分区宽度数组
     */
    void setParts(const std::vector<int>& parts);
    
    /**
     * @brief 调整状态栏大小
     * @param width 宽度
     * @param height 高度
     */
    void resize(int width, int height);
    
    /**
     * @brief 获取状态栏高度
     * @return 状态栏高度
     */
    int getHeight() const;
    
    /**
     * @brief 获取状态栏句柄
     * @return 状态栏句柄
     */
    HWND getHandle() const;
    
private:
    HWND hwnd_;          // 窗口句柄
    HINSTANCE instance_; // 应用程序实例
    HWND statusbar_;     // 状态栏句柄
    std::vector<int> parts_; // 分区宽度
};

} // namespace win32
} // namespace ui
} // namespace xsd_editor

#endif // XSD_EDITOR_UI_WIN32_STATUSBAR_MANAGER_H