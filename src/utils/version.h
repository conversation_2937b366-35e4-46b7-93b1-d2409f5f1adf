#ifndef XSD_EDITOR_UTILS_VERSION_H
#define XSD_EDITOR_UTILS_VERSION_H

#include <string>
#include <vector>
#include <regex>
#include "error.h"

namespace xsd_editor {
namespace utils {

/**
 * @brief 版本号类
 * 
 * 支持语义化版本号的解析、比较和匹配。
 * 格式：major.minor.patch
 */
class Version {
public:
    /**
     * @brief 默认构造函数
     */
    Version() : major_(0), minor_(0), patch_(0) {}

    /**
     * @brief 构造函数
     * 
     * @param major 主版本号
     * @param minor 次版本号
     * @param patch 修订号
     */
    Version(int major, int minor, int patch)
        : major_(major), minor_(minor), patch_(patch)
    {}

    /**
     * @brief 从字符串构造版本号
     * 
     * @param version 版本号字符串（如 "19.07.0"）
     * @throw ParseError 解析失败时抛出异常
     */
    explicit Version(const std::string& version);

    // Getters
    int getMajor() const { return major_; }
    int getMinor() const { return minor_; }
    int getPatch() const { return patch_; }

    /**
     * @brief 转换为字符串
     * @return 版本号字符串
     */
    std::string toString() const;

    // 比较操作符
    bool operator<(const Version& other) const;
    bool operator>(const Version& other) const;
    bool operator<=(const Version& other) const;
    bool operator>=(const Version& other) const;
    bool operator==(const Version& other) const;
    bool operator!=(const Version& other) const;

private:
    int major_;  // 主版本号
    int minor_;  // 次版本号
    int patch_;  // 修订号
};

/**
 * @brief 版本范围类
 * 
 * 支持版本范围的解析和匹配。
 * 支持的格式：
 * - 单个版本：19.07.0
 * - 比较：>19.07.0, <19.07.0, >=19.07.0, <=19.07.0
 * - 通配符：19.07.*, 19.*
 * - 区间：18.06.2-20.04.5
 * - 或运算：18.06.0|19.07.0
 */
class VersionRange {
public:
    /**
     * @brief 默认构造函数
     */
    VersionRange() = default;

    /**
     * @brief 从表达式构造版本范围
     * 
     * @param expression 版本范围表达式
     * @throw ParseError 解析失败时抛出异常
     */
    explicit VersionRange(const std::string& expression);

    /**
     * @brief 检查版本是否匹配范围
     * 
     * @param version 要检查的版本
     * @return 是否匹配
     */
    bool matches(const Version& version) const;

    /**
     * @brief 获取范围表达式
     * @return 范围表达式字符串
     */
    std::string toString() const { return expression_; }

private:
    /**
     * @brief 解析版本范围表达式
     * @param expression 范围表达式
     */
    void parse(const std::string& expression);

    /**
     * @brief 检查版本是否匹配单个条件
     * 
     * @param version 要检查的版本
     * @param condition 条件表达式
     * @return 是否匹配
     */
    bool matchesCondition(const Version& version, const std::string& condition) const;

    /**
     * @brief 检查版本是否匹配通配符模式
     * 
     * @param version 要检查的版本
     * @param pattern 通配符模式
     * @return 是否匹配
     */
    bool matchesWildcard(const Version& version, const std::string& pattern) const;

    std::string expression_;              // 原始表达式
    std::vector<std::string> conditions_; // 解析后的条件列表
};

} // namespace utils
} // namespace xsd_editor

#endif // XSD_EDITOR_UTILS_VERSION_H