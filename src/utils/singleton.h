#ifndef XSD_EDITOR_UTILS_SINGLETON_H
#define XSD_EDITOR_UTILS_SINGLETON_H

#include <memory>
#include <mutex>

namespace xsd_editor {
namespace utils {

/**
 * @brief 单例模式基类模板
 * 
 * 提供线程安全的单例实现，
 * 派生类通过继承此模板类获得单例特性。
 * 
 * 使用示例：
 * @code
 * class MyClass : public Singleton<MyClass> {
 *     friend class Singleton<MyClass>;
 * protected:
 *     MyClass() = default;  // 构造函数必须是protected或private
 * };
 * @endcode
 * 
 * @tparam T 派生类类型
 */
template<typename T>
class Singleton {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例的引用
     */
    static T& getInstance() {
        static std::once_flag flag;
        std::call_once(flag, [&]() {
            instance_.reset(new T());
        });
        return *instance_;
    }

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance() {
        instance_.reset();
    }

    // 禁止复制构造
    Singleton(const Singleton&) = delete;
    // 禁止赋值操作
    Singleton& operator=(const Singleton&) = delete;

protected:
    /**
     * @brief 默认构造函数
     * 
     * 设为protected以允许派生类访问，
     * 但阻止外部直接创建实例。
     */
    Singleton() = default;

    /**
     * @brief 虚析构函数
     * 
     * 允许通过基类指针正确析构派生类对象。
     */
    virtual ~Singleton() = default;

private:
    static std::unique_ptr<T> instance_;  // 单例实例指针
};

// 静态成员初始化
template<typename T>
std::unique_ptr<T> Singleton<T>::instance_;

} // namespace utils
} // namespace xsd_editor

#endif // XSD_EDITOR_UTILS_SINGLETON_H