#include "version.h"
#include <sstream>
#include <algorithm>

namespace xsd_editor {
namespace utils {

Version::Version(const std::string& version) {
    std::regex version_regex(R"(^(\d+)\.(\d+)\.(\d+)$)");
    std::smatch match;

    if (!std::regex_match(version, match, version_regex)) {
        throw ParseError("Invalid version format: " + version);
    }

    try {
        major_ = std::stoi(match[1]);
        minor_ = std::stoi(match[2]);
        patch_ = std::stoi(match[3]);

        if (major_ < 0 || minor_ < 0 || patch_ < 0) {
            throw ParseError("Version numbers cannot be negative: " + version);
        }
    }
    catch (const std::exception& e) {
        throw ParseError("Failed to parse version: " + version + " (" + e.what() + ")");
    }
}

std::string Version::toString() const {
    std::ostringstream oss;
    oss << major_ << "." << minor_ << "." << patch_;
    return oss.str();
}

bool Version::operator<(const Version& other) const {
    if (major_ != other.major_) return major_ < other.major_;
    if (minor_ != other.minor_) return minor_ < other.minor_;
    return patch_ < other.patch_;
}

bool Version::operator>(const Version& other) const {
    return other < *this;
}

bool Version::operator<=(const Version& other) const {
    return !(other < *this);
}

bool Version::operator>=(const Version& other) const {
    return !(*this < other);
}

bool Version::operator==(const Version& other) const {
    return major_ == other.major_ && 
           minor_ == other.minor_ && 
           patch_ == other.patch_;
}

bool Version::operator!=(const Version& other) const {
    return !(*this == other);
}

VersionRange::VersionRange(const std::string& expression) {
    parse(expression);
}

void VersionRange::parse(const std::string& expression) {
    expression_ = expression;
    conditions_.clear();

    // 处理或运算
    size_t pos = 0;
    size_t found;
    while ((found = expression.find('|', pos)) != std::string::npos) {
        conditions_.push_back(expression.substr(pos, found - pos));
        pos = found + 1;
    }
    conditions_.push_back(expression.substr(pos));

    // 验证每个条件
    for (const auto& condition : conditions_) {
        if (condition.empty()) {
            throw ParseError("Empty version condition in: " + expression);
        }
    }
}

bool VersionRange::matches(const Version& version) const {
    // 任意条件匹配即可
    return std::any_of(conditions_.begin(), conditions_.end(),
        [this, &version](const auto& condition) {
            return matchesCondition(version, condition);
        });
}

bool VersionRange::matchesCondition(const Version& version,
                                  const std::string& condition) const {
    // 处理通配符模式
    if (condition.find('*') != std::string::npos) {
        return matchesWildcard(version, condition);
    }

    // 处理比较操作符
    if (condition[0] == '>' || condition[0] == '<' || condition[0] == '=') {
        char op = condition[0];
        bool equal = false;
        size_t start = 1;

        if (condition[1] == '=') {
            equal = true;
            start = 2;
        }

        try {
            Version other(condition.substr(start));
            
            if (op == '>') {
                return equal ? version >= other : version > other;
            }
            else if (op == '<') {
                return equal ? version <= other : version < other;
            }
            else if (op == '=') {
                return version == other;
            }
        }
        catch (const ParseError&) {
            throw ParseError("Invalid version in condition: " + condition);
        }
    }

    // 处理区间
    size_t dash_pos = condition.find('-');
    if (dash_pos != std::string::npos) {
        try {
            Version start(condition.substr(0, dash_pos));
            Version end(condition.substr(dash_pos + 1));
            return version >= start && version <= end;
        }
        catch (const ParseError&) {
            throw ParseError("Invalid version range: " + condition);
        }
    }

    // 直接版本比较
    try {
        Version other(condition);
        return version == other;
    }
    catch (const ParseError&) {
        throw ParseError("Invalid version format: " + condition);
    }
}

bool VersionRange::matchesWildcard(const Version& version,
                                 const std::string& pattern) const {
    std::vector<std::string> pattern_parts;
    std::vector<std::string> version_parts = {
        std::to_string(version.getMajor()),
        std::to_string(version.getMinor()),
        std::to_string(version.getPatch())
    };

    // 分割模式
    std::stringstream ss(pattern);
    std::string part;
    while (std::getline(ss, part, '.')) {
        pattern_parts.push_back(part);
    }

    // 验证部分数量
    if (pattern_parts.size() > 3) {
        throw ParseError("Invalid wildcard pattern: " + pattern);
    }

    // 比较每个部分
    for (size_t i = 0; i < pattern_parts.size(); ++i) {
        if (pattern_parts[i] == "*") {
            continue;  // 通配符匹配任何值
        }
        
        try {
            if (std::stoi(pattern_parts[i]) != std::stoi(version_parts[i])) {
                return false;
            }
        }
        catch (const std::exception&) {
            throw ParseError("Invalid number in pattern: " + pattern);
        }
    }

    return true;
}

} // namespace utils
} // namespace xsd_editor