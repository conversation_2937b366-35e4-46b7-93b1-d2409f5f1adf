#ifndef XSD_EDITOR_UTILS_ERROR_H
#define XSD_EDITOR_UTILS_ERROR_H

#include <stdexcept>
#include <string>
#include <sstream>

namespace xsd_editor {
namespace utils {

/**
 * @brief 基础异常类
 * 
 * 所有自定义异常的基类，提供基本的错误信息和代码管理。
 */
class Error : public std::runtime_error {
public:
    /**
     * @brief 构造函数
     * 
     * @param code 错误代码
     * @param message 错误消息
     */
    Error(int code, const std::string& message)
        : std::runtime_error(message)
        , code_(code)
    {}

    /**
     * @brief 获取错误代码
     * @return 错误代码
     */
    int getCode() const { return code_; }

private:
    int code_;  // 错误代码
};

/**
 * @brief 文件操作错误
 */
class FileError : public Error {
public:
    FileError(const std::string& message)
        : Error(1000, "File error: " + message)
    {}
};

/**
 * @brief 解析错误
 */
class ParseError : public Error {
public:
    ParseError(const std::string& message)
        : Error(2000, "Parse error: " + message)
    {}
};

/**
 * @brief 类型错误
 */
class TypeError : public Error {
public:
    TypeError(const std::string& message)
        : Error(3000, "Type error: " + message)
    {}
};

/**
 * @brief 验证错误
 */
class ValidationError : public Error {
public:
    ValidationError(const std::string& message)
        : Error(4000, "Validation error: " + message)
    {}
};

/**
 * @brief 配置错误
 */
class ConfigError : public Error {
public:
    ConfigError(const std::string& message)
        : Error(5000, "Config error: " + message)
    {}
};

/**
 * @brief 错误信息构建器
 * 
 * 用于构建格式化的错误消息。
 */
class ErrorBuilder {
public:
    /**
     * @brief 构造函数
     * @param context 错误上下文
     */
    explicit ErrorBuilder(const std::string& context = "")
        : context_(context)
    {}

    /**
     * @brief 添加错误详情
     * @param detail 错误详情
     * @return 构建器自身
     */
    ErrorBuilder& addDetail(const std::string& detail) {
        if (!details_.str().empty()) {
            details_ << std::endl;
        }
        details_ << "- " << detail;
        return *this;
    }

    /**
     * @brief 设置文件路径
     * @param path 文件路径
     * @return 构建器自身
     */
    ErrorBuilder& setFile(const std::string& path) {
        file_ = path;
        return *this;
    }

    /**
     * @brief 设置行号
     * @param line 行号
     * @return 构建器自身
     */
    ErrorBuilder& setLine(int line) {
        line_ = line;
        return *this;
    }

    /**
     * @brief 构建错误消息
     * @return 格式化的错误消息
     */
    std::string build() const {
        std::ostringstream oss;
        
        // 添加上下文
        if (!context_.empty()) {
            oss << context_ << std::endl;
        }

        // 添加文件信息
        if (!file_.empty()) {
            oss << "File: " << file_;
            if (line_ > 0) {
                oss << " (line " << line_ << ")";
            }
            oss << std::endl;
        }

        // 添加详情
        if (!details_.str().empty()) {
            oss << "Details:" << std::endl << details_.str();
        }

        return oss.str();
    }

private:
    std::string context_;          // 错误上下文
    std::string file_;            // 文件路径
    int line_ = 0;                // 行号
    std::ostringstream details_;  // 错误详情
};

} // namespace utils
} // namespace xsd_editor

#endif // XSD_EDITOR_UTILS_ERROR_H