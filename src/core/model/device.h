#ifndef XSD_EDITOR_CORE_MODEL_DEVICE_H
#define XSD_EDITOR_CORE_MODEL_DEVICE_H

#include <string>
#include <vector>
#include <memory>

namespace xsd_editor {
namespace core {
namespace model {

/**
 * @brief 设备家族数据模型类
 * 
 * 该类表示一个设备或设备家族，支持树状层级结构，
 * 用于管理设备之间的父子关系。
 */
class Device : public std::enable_shared_from_this<Device> {
public:
    /**
     * @brief 默认构造函数
     */
    Device() = default;

    /**
     * @brief 带参数的构造函数
     * 
     * @param name 设备名称
     */
    explicit Device(const std::string& name);

    /**
     * @brief 析构函数
     */
    virtual ~Device() = default;

    // Getters
    const std::string& getName() const { return name_; }
    const std::vector<std::shared_ptr<Device>>& getChildren() const { return children_; }
    std::weak_ptr<Device> getParent() const { return parent_; }

    // Setters
    void setName(const std::string& name) { name_ = name; }

    /**
     * @brief 添加子设备
     * @param child 子设备指针
     */
    void addChild(const std::shared_ptr<Device>& child);

    /**
     * @brief 移除子设备
     * @param child 子设备指针
     * @return 是否成功移除
     */
    bool removeChild(const std::shared_ptr<Device>& child);

    /**
     * @brief 设置父设备
     * @param parent 父设备指针
     */
    void setParent(const std::weak_ptr<Device>& parent);

    /**
     * @brief 获取完整的设备路径
     * 
     * 返回从根设备到当前设备的完整路径，
     * 各级设备名称用斜杠分隔
     * 
     * @return 设备路径字符串
     */
    std::string getFullPath() const;

    /**
     * @brief 检查是否为指定设备的祖先
     * @param device 要检查的设备
     * @return 是否为祖先
     */
    bool isAncestorOf(const std::shared_ptr<const Device>& device) const;

    /**
     * @brief 检查是否为指定设备的后代
     * @param device 要检查的设备
     * @return 是否为后代
     */
    bool isDescendantOf(const std::shared_ptr<Device>& device) const;

private:
    std::string name_;                              // 设备名称
    std::vector<std::shared_ptr<Device>> children_; // 子设备列表
    std::weak_ptr<Device> parent_;                  // 父设备
};

} // namespace model
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_MODEL_DEVICE_H