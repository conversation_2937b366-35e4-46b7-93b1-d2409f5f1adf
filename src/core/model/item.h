#ifndef XSD_EDITOR_CORE_MODEL_ITEM_H
#define XSD_EDITOR_CORE_MODEL_ITEM_H

#include <string>
#include <vector>
#include <memory>

namespace xsd_editor {
namespace core {
namespace model {

inline std::vector<std::string> string_split(std::string const& str, char sp) {
    if (str.empty())
    {
        return {};
    }
    std::vector<std::string> result;
    size_t start = 0; // 子串的起始位置
    size_t end = str.find(sp); // 查找分隔符的位置

    // 遍历字符串，分割子串
    while (end != std::string::npos) {
        // 提取子串并添加到结果中
        if (end - start > 0)
            result.push_back(str.substr(start, end - start));
        start = end + 1; // 更新起始位置
        end = str.find(sp, start); // 查找下一个分隔符
    }

    // 添加最后一个子串
    if (end - start > 0)
        result.push_back(str.substr(start));

    return result;
}

inline std::string string_join(std::vector<std::string> const& list, char sp) {
    if (list.empty()) {
        return "";
    }

    // 计算总长度以预分配内存
    size_t total_length = 0;
    for (const auto& s : list) {
        total_length += s.size();
    }
    total_length += list.size() - 1; // 分隔符的总长度

    std::string result;
    result.reserve(total_length); // 预分配内存避免多次扩容

    bool is_first = true;
    for (const auto& s : list) {
        if (!is_first) {
            result += sp;
        } else {
            is_first = false;
        }
        result += s;
    }

    return result;
}

/**
 * @brief UCI配置元素的数据模型类
 * 
 * 该类表示UCI配置中的一个元素（module/config/option/list），
 * 包含元素的所有属性和层级关系。
 */
class Item : public std::enable_shared_from_this<Item>{
    struct Private{ explicit Private() = default; };
public:
    static std::shared_ptr<Item> create(const std::string& name, const std::string& type, int level)
    {
        return std::make_shared<Item>(Private(),name,type,level);
    }

    /**
     * @brief 带参数的构造函数
     * 
     * @param name 元素名称
     * @param type UCI元素类型
     * @param level 层级（0:module, 1:config, 2:option/list）
     */
    Item(Private,const std::string& name, const std::string& type, int level);
    /**
     * @brief 析构函数
     */
    virtual ~Item() = default;

    // Getters
    const std::string& getName() const { return name_; }
    std::string getQuotedSingleName() const { if (isMultipleName() || hasFlexibleName() || name_.empty())return {};return "'"+name_+"'"; }
    bool isMultipleName() const { return name_.find(',')!=name_.npos;}
    bool hasFlexibleName() const { return name_=="+";}
    const std::string& getType() const { return type_; }
    const std::string& getAdvType() const { return adv_type_; }
    const std::string& getFileLoc() const { return file_loc_; }
    const std::vector<std::string>& getAffectDevices() const { return affect_devices_; }
    std::string getAffectDevicesString() const {return string_join(affect_devices_, ','); }
    void setAffectDevicesString(const std::string& device_list)
    {
        affect_devices_=string_split(device_list, ',');
        
    }

    const std::vector<std::string>& getAffectVersions() const { return affect_versions_; }
    void setVersionsString(const std::string& version_list)
    {
        affect_versions_=string_split(version_list, ',');
    }
    std::string getVersionsString() const{ return string_join(affect_versions_, ',');}
    bool isList() const { return is_list_; }
    int getLevel() const { return level_; }
    const std::vector<std::shared_ptr<Item>>& getChildren() const { return children_; }
    std::weak_ptr<Item> getParent() const { return parent_; }
    const std::vector<std::pair<std::string, std::string>>& getAttributes() const { return attributes_; }

    // Setters
    void setName(const std::string& name) { name_ = name; }
    void setType(const std::string& type) { type_ = type; }
    void setAdvType(const std::string& adv_type) { adv_type_ = adv_type; }
    void setFileLoc(const std::string& file_loc) { file_loc_ = file_loc; }
    void setIsList(bool is_list) { is_list_ = is_list; }
    void setLevel(int level) { level_ = level; }
    void setLevelR(int level) { level_ = level; for(auto i:children_)i->setLevelR(level+1);}
    static bool swapItem(std::shared_ptr<Item> item1, std::shared_ptr<Item> item2);

    /**
     * @brief 添加一个受影响的设备
     * @param device 设备名称
     */
    void addAffectDevice(const std::string& device);

    /**
     * @brief 添加一个版本范围
     * @param version 版本范围表达式
     */
    void addAffectVersion(const std::string& version);

    /**
     * @brief 添加一个子元素
     * @param child 子元素指针
     */
    void addChild(const std::shared_ptr<Item>& child, const std::shared_ptr<Item>& after = {});

    /**
     * @brief 移除一个子元素
     * @param child 子元素指针
     * @return 是否成功移除
     */
    bool removeChild(const std::shared_ptr<Item>& child);

    /**
     * @brief 设置父元素
     * @param parent 父元素指针
     */
    void setParent(const std::weak_ptr<Item>& parent);

    /**
     * @brief 设置属性
     * @param key 属性名
     * @param value 属性值
     */
    void setAttribute(const std::string& key, const std::string& value);

    /**
     * @brief 获取属性值
     * @param key 属性名
     * @return 属性值，如果不存在返回空字符串
     */
    std::string getAttribute(const std::string& key) const;

    /**
     * @brief 移除属性
     * @param key 属性名
     * @return 是否成功移除
     */
    bool removeAttribute(const std::string& key);

private:
    std::string name_;                    // UCI元素名字
    std::string type_;                    // UCI元素类型
    std::string adv_type_;               // 新增额外类型
    std::string file_loc_;              // 对应的实际文件
    std::vector<std::string> affect_devices_;    // 受影响的设备家族
    std::vector<std::string> affect_versions_;   // 受影响的版本范围
    bool is_list_ = false;               // 是否为列表类型
    int level_ = 0;                      // 层级
    std::vector<std::shared_ptr<Item>> children_;  // 子元素列表
    std::weak_ptr<Item> parent_;         // 指向父元素
    std::vector<std::pair<std::string, std::string>> attributes_;  // 属性键值对
};

} // namespace model
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_MODEL_ITEM_H