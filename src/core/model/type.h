#ifndef XSD_EDITOR_CORE_MODEL_TYPE_H
#define XSD_EDITOR_CORE_MODEL_TYPE_H

#include <string>
#include <vector>
#include <map>
#include <optional>

namespace xsd_editor {
namespace core {
namespace model {

/**
 * @brief 高级类型数据模型类
 * 
 * 该类表示一个高级数据类型，支持枚举、正则表达式、数值范围等约束，
 * 以及类型继承和额外属性。
 */
class AdvancedType {
public:
    /**
     * @brief 默认构造函数
     */
    AdvancedType() = default;

    /**
     * @brief 带参数的构造函数
     * 
     * @param name 类型名称
     * @param base_type 基础类型（string/int/bool等）
     */
    AdvancedType(const std::string& name, const std::string& base_type);

    /**
     * @brief 析构函数
     */
    virtual ~AdvancedType() = default;

    // Getters
    const std::string& getName() const { return name_; }
    const std::string& getBaseType() const { return base_type_; }
    const std::string& getParentType() const { return parent_type_; }
    const std::vector<std::string>& getEnumValues() const { return enum_values_; }
    const std::string& getRegexPattern() const { return regex_pattern_; }
    std::optional<int> getMinValue() const { return min_value_; }
    std::optional<int> getMaxValue() const { return max_value_; }
    const std::map<std::string, std::string>& getExtraAttrs() const { return extra_attrs_; }

    // Setters
    void setName(const std::string& name) { name_ = name; }
    void setBaseType(const std::string& base_type) { base_type_ = base_type; }
    void setParentType(const std::string& parent_type) { parent_type_ = parent_type; }
    void setRegexPattern(const std::string& pattern) { regex_pattern_ = pattern; }
    void setMinValue(int min) { min_value_ = min; }
    void setMaxValue(int max) { max_value_ = max; }

    /**
     * @brief 添加一个枚举值
     * @param value 枚举值
     */
    void addEnumValue(const std::string& value);

    /**
     * @brief 移除一个枚举值
     * @param value 枚举值
     * @return 是否成功移除
     */
    bool removeEnumValue(const std::string& value);

    /**
     * @brief 设置额外属性
     * @param key 属性名
     * @param value 属性值
     */
    void setExtraAttr(const std::string& key, const std::string& value);

    /**
     * @brief 获取额外属性值
     * @param key 属性名
     * @return 属性值，如果不存在返回空字符串
     */
    std::string getExtraAttr(const std::string& key) const;

    /**
     * @brief 移除额外属性
     * @param key 属性名
     * @return 是否成功移除
     */
    bool removeExtraAttr(const std::string& key);

    /**
     * @brief 清除所有约束
     * 清除枚举值、正则表达式、数值范围等约束
     */
    void clearConstraints();

    /**
     * @brief 检查类型是否有效
     * 
     * 检查基础类型和约束是否匹配，例如：
     * - 枚举和正则表达式只能用于string类型
     * - 最大最小值只能用于int类型
     * 
     * @return 是否有效
     */
    bool isValid() const;

private:
    std::string name_;                    // 类型名称
    std::string base_type_;               // 基础类型
    std::string parent_type_;             // 父类型
    std::vector<std::string> enum_values_;  // 枚举值列表
    std::string regex_pattern_;           // 正则表达式
    std::optional<int> min_value_;        // 最小值
    std::optional<int> max_value_;        // 最大值
    std::map<std::string, std::string> extra_attrs_;  // 额外属性
};

} // namespace model
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_MODEL_TYPE_H