#include "device.h"
#include <algorithm>
#include <sstream>

namespace xsd_editor {
namespace core {
namespace model {

Device::Device(const std::string& name)
    : name_(name)
{
}

void Device::addChild(const std::shared_ptr<Device>& child) {
    if (child) {
        // 检查是否已经是子设备
        auto it = std::find(children_.begin(), children_.end(), child);
        if (it == children_.end()) {
            // 设置父子关系
            child->setParent(shared_from_this());
            children_.push_back(child);
        }
    }
}

bool Device::removeChild(const std::shared_ptr<Device>& child) {
    if (!child) {
        return false;
    }

    auto it = std::find(children_.begin(), children_.end(), child);
    if (it != children_.end()) {
        // 清除父子关系
        (*it)->setParent(std::weak_ptr<Device>());
        children_.erase(it);
        return true;
    }
    return false;
}

void Device::setParent(const std::weak_ptr<Device>& parent) {
    // 检查是否会形成循环引用
    if (auto p = parent.lock()) {
        if (isAncestorOf(p)) {
            return; // 避免循环引用
        }
    }
    parent_ = parent;
}

std::string Device::getFullPath() const {
    std::vector<std::string> path_components;
    path_components.push_back(name_);

    // 向上遍历父设备，收集路径组件
    auto current = parent_;
    while (auto p = current.lock()) {
        path_components.push_back(p->getName());
        current = p->getParent();
    }

    // 构建路径字符串（反转顺序，从根到叶）
    std::stringstream ss;
    for (auto it = path_components.rbegin(); it != path_components.rend(); ++it) {
        if (it != path_components.rbegin()) {
            ss << "/";
        }
        ss << *it;
    }

    return ss.str();
}

bool Device::isAncestorOf(const std::shared_ptr<const Device>& device) const {
    if (!device) {
        return false;
    }

    auto current = device->getParent();
    while (auto p = current.lock()) {
        if (p.get() == this) {
            return true;
        }
        current = p->getParent();
    }

    return false;
}

bool Device::isDescendantOf(const std::shared_ptr<Device>& device) const {
    if (!device) {
        return false;
    }
    return device->isAncestorOf(shared_from_this());
}

} // namespace model
} // namespace core
} // namespace xsd_editor