#include "item.h"
#include <algorithm>
#include <utility>

namespace xsd_editor {
namespace core {
namespace model {

Item::Item(Private,const std::string& name, const std::string& type, int level)
    : name_(name)
    , type_(type)
    , level_(level)
{
}

void Item::addAffectDevice(const std::string& device) {
    if (std::find(affect_devices_.begin(), affect_devices_.end(), device) == affect_devices_.end()) {
        affect_devices_.push_back(device);
    }
}

void Item::addAffectVersion(const std::string& version) {
    if (std::find(affect_versions_.begin(), affect_versions_.end(), version) == affect_versions_.end()) {
        affect_versions_.push_back(version);
    }
}

void Item::addChild(const std::shared_ptr<Item>& child, const std::shared_ptr<Item>& after) {
    if (child) {
        // 设置父子关系
        child->setParent(weak_from_this());
        if (after)
        {
            auto it = children_.begin();
            for (; it != children_.end(); ++it)
            {
                if (*it == after)
                {
                    ++it;
                    break;
                }
            }
            children_.insert(it, child);
        }else
            children_.push_back(child);
    }
}

bool Item::removeChild(const std::shared_ptr<Item>& child) {
    if (!child) {
        return false;
    }

    auto it = std::find(children_.begin(), children_.end(), child);
    if (it != children_.end()) {
        // 清除父子关系
        (*it)->setParent(std::weak_ptr<Item>());
        children_.erase(it);
        return true;
    }
    return false;
}

bool Item::swapItem(std::shared_ptr<Item> item1, std::shared_ptr<Item> item2)
{
    auto parent=item1->parent_.lock();
    if (!parent)return false;
    if (parent != item2->getParent().lock())return false;
    decltype(parent->children_)::iterator it1, it2;
    for (it1 = parent->children_.begin();it1 != parent->children_.end(); ++it1)if (*it1 == item1)break;
    for (it2 = parent->children_.begin();it2 != parent->children_.end(); ++it2)if (*it2 == item2)break;
    if (it1 == parent->children_.end())return false;
    if (it2 == parent->children_.end())return false;
    *it1=item2;
    *it2=item1;
    return true;
}

void Item::setParent(const std::weak_ptr<Item>& parent) {
    parent_ = parent;
}

void Item::setAttribute(const std::string& key, const std::string& value) {
    if (key == "type")
    {
        adv_type_ = value;
    }else if (key == "device_family")
    {
        setAffectDevicesString(value);
    }else if (key == "version")
    {
        setVersionsString(value);
    }else if (key == "name")
    {
        auto pos = value.find(':');
        if (pos != value.npos)
            setName(value.substr(pos+1));
        else
            setName("+");
    }else{
        attributes_.push_back(std::make_pair(key, value));
    }
}

std::string Item::getAttribute(const std::string& key) const {
    for (auto it = attributes_.begin(); it != attributes_.end(); ++it)
    {
        if (it->first == key)return it->second;
    }
    return {};
}

bool Item::removeAttribute(const std::string& key) {
    for (auto it = attributes_.begin(); it != attributes_.end(); ++it)
    {
        if (it->first == key)return attributes_.erase(it),true;
    }
    return false;
}

} // namespace model
} // namespace core
} // namespace xsd_editor