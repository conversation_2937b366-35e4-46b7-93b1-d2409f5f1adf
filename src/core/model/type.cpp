#include "type.h"
#include <algorithm>

namespace xsd_editor {
namespace core {
namespace model {

AdvancedType::AdvancedType(const std::string& name, const std::string& base_type)
    : name_(name)
    , base_type_(base_type)
{
}

void AdvancedType::addEnumValue(const std::string& value) {
    if (std::find(enum_values_.begin(), enum_values_.end(), value) == enum_values_.end()) {
        enum_values_.push_back(value);
    }
}

bool AdvancedType::removeEnumValue(const std::string& value) {
    auto it = std::find(enum_values_.begin(), enum_values_.end(), value);
    if (it != enum_values_.end()) {
        enum_values_.erase(it);
        return true;
    }
    return false;
}

void AdvancedType::setExtraAttr(const std::string& key, const std::string& value) {
    extra_attrs_[key] = value;
}

std::string AdvancedType::getExtraAttr(const std::string& key) const {
    auto it = extra_attrs_.find(key);
    return it != extra_attrs_.end() ? it->second : std::string();
}

bool AdvancedType::removeExtraAttr(const std::string& key) {
    return extra_attrs_.erase(key) > 0;
}

void AdvancedType::clearConstraints() {
    enum_values_.clear();
    regex_pattern_.clear();
    min_value_.reset();
    max_value_.reset();
}

bool AdvancedType::isValid() const {
    // 检查基础类型是否为空
    if (base_type_.empty()) {
        return false;
    }

    // 检查枚举和正则约束是否只用于string类型
    if (!enum_values_.empty() || !regex_pattern_.empty()) {
        if (base_type_ != "string") {
            return false;
        }
    }

    // 检查数值范围约束是否只用于int类型
    if (min_value_.has_value() || max_value_.has_value()) {
        if (base_type_ != "int") {
            return false;
        }
    }

    // 如果设置了最大最小值，检查最大值是否大于最小值
    if (min_value_.has_value() && max_value_.has_value()) {
        if (min_value_.value() > max_value_.value()) {
            return false;
        }
    }

    return true;
}

} // namespace model
} // namespace core
} // namespace xsd_editor