#include "type_check.h"
#include <regex>
#include <algorithm>
#include <stdexcept>
#include <set>

namespace xsd_editor {
namespace core {
namespace validator {

TypeChecker::TypeChecker(const std::vector<std::shared_ptr<model::AdvancedType>>& types)
    : types_(types) {
}

void TypeChecker::setTypes(const std::vector<std::shared_ptr<model::AdvancedType>>& types) {
    types_ = types;
}

void TypeChecker::validate(const std::string& value, const std::string& type_name) {
    // 首先检查是否是基本类型
    if (type_name == "string" || type_name == "int" || type_name == "bool") {
        validateBasicType(value, type_name);
        return;
    }

    // 查找高级类型
    auto it = std::find_if(types_.begin(), types_.end(),
        [&type_name](const auto& type) { return type->getName() == type_name; });

    if (it == types_.end()) {
        throw TypeError("Unknown type: " + type_name);
    }

    validateAdvancedType(value, *it);
}

bool TypeChecker::hasType(const std::string& type_name) const {
    if (type_name == "string" || type_name == "int" || type_name == "bool") {
        return true;
    }

    return std::any_of(types_.begin(), types_.end(),
        [&type_name](const auto& type) { return type->getName() == type_name; });
}

std::string TypeChecker::getRootType(const std::string& type_name) {
    if (type_name == "string" || type_name == "int" || type_name == "bool") {
        return type_name;
    }

    std::set<std::string> visited;  // 用于检测循环继承
    std::string current = type_name;

    while (true) {
        if (visited.find(current) != visited.end()) {
            throw TypeError("Circular inheritance detected for type: " + type_name);
        }
        visited.insert(current);

        auto it = std::find_if(types_.begin(), types_.end(),
            [&current](const auto& type) { return type->getName() == current; });

        if (it == types_.end()) {
            throw TypeError("Type not found: " + current);
        }

        const auto& parent_type = (*it)->getParentType();
        if (parent_type.empty()) {
            return (*it)->getBaseType();
        }
        current = parent_type;
    }
}

void TypeChecker::validateBasicType(const std::string& value, const std::string& type_name) {
    if (type_name == "string") {
        return;  // 字符串类型不需要特殊验证
    }
    else if (type_name == "int") {
        try {
            size_t pos;
            std::stoi(value, &pos);
            if (pos != value.length()) {
                throw TypeError("Invalid integer value: " + value);
            }
        }
        catch (const std::exception&) {
            throw TypeError("Invalid integer value: " + value);
        }
    }
    else if (type_name == "bool") {
        std::string lower_value = value;
        std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);
        
        if (lower_value != "true" && lower_value != "false" &&
            lower_value != "1" && lower_value != "0" &&
            lower_value != "yes" && lower_value != "no" &&
            lower_value != "on" && lower_value != "off") {
            throw TypeError("Invalid boolean value: " + value);
        }
    }
}

void TypeChecker::validateAdvancedType(const std::string& value,
                                     const std::shared_ptr<model::AdvancedType>& type) {
    // 首先验证基础类型
    validateBasicType(value, getRootType(type->getName()));

    // 验证枚举约束
    if (!type->getEnumValues().empty()) {
        validateEnum(value, type);
    }

    // 验证正则表达式约束
    if (!type->getRegexPattern().empty()) {
        validateRegex(value, type);
    }

    // 验证数值范围约束
    if (type->getMinValue() || type->getMaxValue()) {
        validateRange(value, type);
    }

    // 如果有父类型，递归验证父类型的约束
    if (!type->getParentType().empty()) {
        auto parent_it = std::find_if(types_.begin(), types_.end(),
            [&type](const auto& t) { return t->getName() == type->getParentType(); });

        if (parent_it == types_.end()) {
            throw TypeError("Parent type not found: " + type->getParentType());
        }

        validateAdvancedType(value, *parent_it);
    }
}

void TypeChecker::validateEnum(const std::string& value,
                             const std::shared_ptr<model::AdvancedType>& type) {
    const auto& enum_values = type->getEnumValues();
    if (std::find(enum_values.begin(), enum_values.end(), value) == enum_values.end()) {
        std::string valid_values;
        for (const auto& v : enum_values) {
            if (!valid_values.empty()) valid_values += ", ";
            valid_values += v;
        }
        throw TypeError("Value '" + value + "' not in enum [" + valid_values + "]");
    }
}

void TypeChecker::validateRegex(const std::string& value,
                              const std::shared_ptr<model::AdvancedType>& type) {
    try {
        std::regex pattern(type->getRegexPattern());
        if (!std::regex_match(value, pattern)) {
            throw TypeError("Value '" + value + "' does not match pattern: " + 
                          type->getRegexPattern());
        }
    }
    catch (const std::regex_error& e) {
        throw TypeError("Invalid regex pattern: " + type->getRegexPattern() + 
                       " (" + e.what() + ")");
    }
}

void TypeChecker::validateRange(const std::string& value,
                              const std::shared_ptr<model::AdvancedType>& type) {
    try {
        int num_value = std::stoi(value);
        
        if (type->getMinValue() && num_value < type->getMinValue().value()) {
            throw TypeError("Value " + value + " is less than minimum: " + 
                          std::to_string(type->getMinValue().value()));
        }
        
        if (type->getMaxValue() && num_value > type->getMaxValue().value()) {
            throw TypeError("Value " + value + " is greater than maximum: " + 
                          std::to_string(type->getMaxValue().value()));
        }
    }
    catch (const std::invalid_argument&) {
        throw TypeError("Invalid integer value for range check: " + value);
    }
    catch (const std::out_of_range&) {
        throw TypeError("Integer value out of range: " + value);
    }
}

} // namespace validator
} // namespace core
} // namespace xsd_editor