#ifndef XSD_EDITOR_CORE_VALIDATOR_TYPE_CHECK_H
#define XSD_EDITOR_CORE_VALIDATOR_TYPE_CHECK_H

#include <string>
#include <memory>
#include <vector>
#include "../model/type.h"
#include <exception>
#include <stdexcept>

namespace xsd_editor {
namespace core {
namespace validator {

/**
 * @brief 类型检查错误类
 */
class TypeError : public std::runtime_error {
public:
    explicit TypeError(const std::string& message) 
        : std::runtime_error(message) {}
};

/**
 * @brief 类型检查器类
 * 
 * 该类负责验证配置值是否符合类型约束，
 * 支持基本类型和高级类型的验证。
 */
class TypeChecker {
public:
    /**
     * @brief 默认构造函数
     */
    TypeChecker() = default;

    /**
     * @brief 构造函数
     * 
     * @param types 可用的高级类型列表
     */
    explicit TypeChecker(const std::vector<std::shared_ptr<model::AdvancedType>>& types);

    /**
     * @brief 析构函数
     */
    virtual ~TypeChecker() = default;

    /**
     * @brief 设置可用的高级类型列表
     * 
     * @param types 高级类型列表
     */
    void setTypes(const std::vector<std::shared_ptr<model::AdvancedType>>& types);

    /**
     * @brief 验证值是否符合类型约束
     * 
     * @param value 要验证的值
     * @param type_name 类型名称
     * @throw TypeError 验证失败时抛出异常
     */
    void validate(const std::string& value, const std::string& type_name);

    /**
     * @brief 检查类型是否存在
     * 
     * @param type_name 类型名称
     * @return 是否存在
     */
    bool hasType(const std::string& type_name) const;

    /**
     * @brief 获取类型的根类型
     * 
     * 通过类型继承链找到最顶层的基础类型
     * 
     * @param type_name 类型名称
     * @return 根类型名称
     * @throw TypeError 类型不存在或继承链断裂时抛出异常
     */
    std::string getRootType(const std::string& type_name);

private:
    /**
     * @brief 验证基本类型
     * 
     * @param value 要验证的值
     * @param type_name 类型名称（string/int/bool）
     * @throw TypeError 验证失败时抛出异常
     */
    void validateBasicType(const std::string& value, const std::string& type_name);

    /**
     * @brief 验证高级类型
     * 
     * @param value 要验证的值
     * @param type 高级类型对象
     * @throw TypeError 验证失败时抛出异常
     */
    void validateAdvancedType(const std::string& value, 
                             const std::shared_ptr<model::AdvancedType>& type);

    /**
     * @brief 验证枚举约束
     * 
     * @param value 要验证的值
     * @param type 高级类型对象
     * @throw TypeError 验证失败时抛出异常
     */
    void validateEnum(const std::string& value,
                     const std::shared_ptr<model::AdvancedType>& type);

    /**
     * @brief 验证正则表达式约束
     * 
     * @param value 要验证的值
     * @param type 高级类型对象
     * @throw TypeError 验证失败时抛出异常
     */
    void validateRegex(const std::string& value,
                      const std::shared_ptr<model::AdvancedType>& type);

    /**
     * @brief 验证数值范围约束
     * 
     * @param value 要验证的值
     * @param type 高级类型对象
     * @throw TypeError 验证失败时抛出异常
     */
    void validateRange(const std::string& value,
                      const std::shared_ptr<model::AdvancedType>& type);

    std::vector<std::shared_ptr<model::AdvancedType>> types_;  // 可用的高级类型列表
};

} // namespace validator
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_VALIDATOR_TYPE_CHECK_H