#ifndef XSD_EDITOR_CORE_PARSER_UCITYPE_H
#define XSD_EDITOR_CORE_PARSER_UCITYPE_H

#include <string>
#include <memory>
#include <vector>
#include "../model/item.h"

namespace xsd_editor {
namespace core {
namespace parser {

/**
 * @brief UCI类型文件解析器类
 * 
 * 该类负责解析.ucitype格式的配置定义文件，
 * 支持属性解析、注释处理和层级结构构建。
 */
class UciTypeParser {
public:
    /**
     * @brief 默认构造函数
     */
    UciTypeParser() = default;

    /**
     * @brief 析构函数
     */
    virtual ~UciTypeParser() = default;

    /**
     * @brief 解析UCI类型文件
     * 
     * @param file_path 文件路径
     * @return 解析得到的根元素（module）
     * @throw ParseError 解析错误时抛出异常
     */
    std::shared_ptr<model::Item> parseFile(const std::string& file_path);

    /**
     * @brief 解析UCI类型文本内容
     * 
     * @param content 文件内容
     * @param file_path 可选的文件路径（用于错误提示）
     * @return 解析得到的根元素（module）
     * @throw ParseError 解析错误时抛出异常
     */
    std::shared_ptr<model::Item> parseContent(const std::string& content, 
                                            const std::string& file_path = "");

    /**
     * @brief 将Item对象序列化为UCI类型文本
     * 
     * @param item 要序列化的Item对象
     * @return 生成的UCI类型文本
     */
    std::string serialize(const std::shared_ptr<model::Item>& item);

private:
    /**
     * @brief 解析属性行
     * 
     * 解析形如 @key(value1, value2...) 的属性定义
     * 
     * @param line 属性行文本
     * @return 解析得到的属性键值对
     */
    std::vector<std::pair<std::string, std::string>> parseAttributes(const std::string& line);

    /**
     * @brief 解析配置行
     * 
     * 解析形如 config system 'led' 的配置定义
     * 
     * @param line 配置行文本
     * @param level 当前层级
     * @return 解析得到的Item对象
     */
    std::shared_ptr<model::Item> parseConfigLine(const std::string& line, int level);

    /**
     * @brief 解析选项行
     * 
     * 解析形如 option name 'status_led' 的选项定义
     * 
     * @param line 选项行文本
     * @param level 当前层级
     * @return 解析得到的Item对象
     */
    std::shared_ptr<model::Item> parseOptionLine(const std::string& line, int level);

    /**
     * @brief 解析列表行
     * 
     * 解析形如 list maclist '00:11:22:33:44:55' 的列表定义
     * 
     * @param line 列表行文本
     * @param level 当前层级
     * @return 解析得到的Item对象
     */
    std::shared_ptr<model::Item> parseListLine(const std::string& line, int level);

    /**
     * @brief 获取行的缩进级别
     * 
     * @param line 行文本
     * @return 缩进的空格数
     */
    int getIndentLevel(const std::string& line);

    // 当前解析状态
    int current_line_ = 0;           // 当前解析的行号
    std::string current_file_;       // 当前解析的文件名
};

} // namespace parser
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_PARSER_UCITYPE_H