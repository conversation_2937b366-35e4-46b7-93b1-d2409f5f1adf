#include "ucitype.h"
#include <fstream>
#include <ostream>
#include <sstream>
#include <regex>
#include <stdexcept>
#include <filesystem>
#include <utility>

namespace fs = std::filesystem;

namespace xsd_editor {
namespace core {
namespace parser {

// 用于解析的正则表达式
namespace {
    const std::regex ATTR_REGEX(R"(@(\.?\w+)\s*(\((.*?)\))?)");
    const std::regex CONFIG_REGEX(R"(config\s+([\w-]+)(\s+'([^']*)')?)");
    const std::regex OPTION_REGEX(R"(option\s+([\w-]+)(\s+'([^']*)')?)");
    const std::regex LIST_REGEX(R"(list\s+([\w-]+)(\s+'([^']*)')?)");
}

std::shared_ptr<model::Item> UciTypeParser::parseFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: " + file_path);
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    return parseContent(buffer.str(), file_path);
}

std::shared_ptr<model::Item> UciTypeParser::parseContent(
    const std::string& content, const std::string& file_path) {
    
    current_file_ = file_path;
    current_line_ = 0;

    std::shared_ptr<model::Item> root;
    std::shared_ptr<model::Item> current_config;
    std::vector<std::pair<std::string, std::string>> pending_attrs;

    std::istringstream iss(content);
    std::string line;

    root = model::Item::create("", fs::path(file_path).stem(), 0);

    
    while (std::getline(iss, line)) {
        current_line_++;
        
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // 处理属性行
        if (line.find("@") == line.find_first_not_of(" \t")) {
            auto attrs = parseAttributes(line);
            pending_attrs.insert(pending_attrs.end(), attrs.begin(), attrs.end());
            continue;
        }

        //int indent = getIndentLevel(line);
        try {
            if (line.find("config") == line.find_first_not_of(" \t")) {
                auto item = parseConfigLine(line, 1);
                
                // 应用待处理的属性
                for (const auto& attr : pending_attrs) {
                    item->setAttribute(attr.first, attr.second);
                }
                pending_attrs.clear();

                if (!root) {
                    root = item;
                    current_config = item;
                } else {
                    current_config = item;
                    root->addChild(item);
                }
            }
            else if (line.find("option") == line.find_first_not_of(" \t")) {
                if (!current_config) {
                    throw std::runtime_error("Option without config at line " + 
                                           std::to_string(current_line_));
                }
                auto item = parseOptionLine(line, 2);
                
                // 应用待处理的属性
                for (const auto& attr : pending_attrs) {
                    item->setAttribute(attr.first, attr.second);
                }
                pending_attrs.clear();

                current_config->addChild(item);
            }
            else if (line.find("list") == line.find_first_not_of(" \t")) {
                if (!current_config) {
                    throw std::runtime_error("List without config at line " + 
                                           std::to_string(current_line_));
                }
                auto item = parseListLine(line, 2);
                
                // 应用待处理的属性
                for (const auto& attr : pending_attrs) {
                    item->setAttribute(attr.first, attr.second);
                }
                pending_attrs.clear();

                current_config->addChild(item);
            }
        } catch (const std::exception& e) {
            throw std::runtime_error("Parse error at line " + 
                                   std::to_string(current_line_) + ": " + e.what());
        }
    }

    if (!root) {
        throw std::runtime_error("No valid UCI configuration found in file");
    }

    return root;
}

std::vector<std::pair<std::string, std::string>> UciTypeParser::parseAttributes(const std::string& line) {
    std::vector<std::pair<std::string, std::string>> attrs;
    
    std::string::const_iterator search_start(line.cbegin());
    std::smatch match;
    
    while (std::regex_search(search_start, line.cend(), match, ATTR_REGEX)) {
        std::string key = match[1];
        std::string value = match[3];
        
        // 清理值中的空白字符
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        attrs.emplace_back(std::make_pair(key, value));
        search_start = match.suffix().first;
    }
    
    return attrs;
}

std::shared_ptr<model::Item> UciTypeParser::parseConfigLine(
    const std::string& line, int level) {
    
    std::smatch match;
    if (!std::regex_search(line, match, CONFIG_REGEX)) {
        throw std::runtime_error("Invalid config line format");
    }

    auto item = model::Item::create(match[3], match[1], level);
    return item;
}

std::shared_ptr<model::Item> UciTypeParser::parseOptionLine(
    const std::string& line, int level) {
    
    std::smatch match;
    if (!std::regex_search(line, match, OPTION_REGEX)) {
        throw std::runtime_error("Invalid option line format");
    }

    auto item = model::Item::create(match[3], match[1], level);
    return item;
}

std::shared_ptr<model::Item> UciTypeParser::parseListLine(
    const std::string& line, int level) {
    
    std::smatch match;
    if (!std::regex_search(line, match, LIST_REGEX)) {
        throw std::runtime_error("Invalid list line format");
    }

    auto item = model::Item::create(match[3], match[1], level);
    item->setIsList(true);
    return item;
}

int UciTypeParser::getIndentLevel(const std::string& line) {
    int spaces = 0;
    for (char c : line) {
        if (c == ' ') {
            spaces++;
        } else if (c == '\t') {
            spaces += 4;  // 假设一个制表符等于4个空格
        } else {
            break;
        }
    }
    return spaces;
}

std::string UciTypeParser::serialize(const std::shared_ptr<model::Item>& item) {
    if (!item) {
        return "";
    }

    std::stringstream ss;
    
    std::string indent(item->getLevel() > 0 ? (item->getLevel() - 1) * 4 : 0, ' ');  // 每级缩进4个空格
    // 序列化属性
    auto attrs = item->getAttributes();
    bool has_attr = !attrs.empty();
    bool is_first = true;
    ss  << indent;
    for (const auto& attr : attrs) {
        if(!is_first)ss<<' ';
        ss  << "@" << attr.first << "(" << attr.second << ")";
        is_first = false;
    }
    auto type =item->getAdvType();
    if (!type.empty())
    {
        if(!is_first)ss<<' ';
        ss  << "@type(" << type << ")";
        has_attr = true;
        is_first = false;
    }
    auto version =item->getVersionsString();
    if (!version.empty())
    {
        if(!is_first)ss<<' ';
        ss  << "@version(" << version << ")";
        has_attr = true;
        is_first = false;
    }
    auto devices =item->getAffectDevicesString();
    if (!devices.empty())
    {
        if(!is_first)ss<<' ';
        ss  << "@device_family(" << devices << ")";
        has_attr = true;
        is_first = false;
    }
    auto name =item->getName();
    if (item->isMultipleName())
    {
        if(!is_first)ss<<' ';
        ss  << "@name(enum:" << name << ")";
        has_attr = true;
        is_first = false;
    }
    else if (item->hasFlexibleName())
    {
        if(!is_first)ss<<' ';
        ss  << "@name";
        has_attr = true;
        is_first = false;
    }
    
    if (has_attr) ss << std::endl;

    // 序列化配置/选项/列表定义
    
    if (item->getLevel() == 0) {  // module

    } else if (item->getLevel() == 1) {  // config
        ss << "config " << item->getType();
    } else if (item->isList()) {
        ss << indent << "list " << item->getType();
    } else {
        ss << indent << "option " << item->getType() ;
    }
    name = item->getQuotedSingleName();
    if (!name.empty())
    {
        ss << ' ' << name;
    }
    ss << std::endl;

    // 递归序列化子元素
    for (const auto& child : item->getChildren()) {
        ss << serialize(child);
    }

    return ss.str();
}

} // namespace parser
} // namespace core
} // namespace xsd_editor