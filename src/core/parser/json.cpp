#include "json.h"
#include <fstream>
#include <stdexcept>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

namespace xsd_editor {
namespace core {
namespace parser {

std::vector<std::string> JsonParser::getExtras() const {
    return extras_;
}

void JsonParser::setExtras(const std::vector<std::string>& extras) {
    extras_ = extras;
}

std::vector<std::shared_ptr<model::AdvancedType>> JsonParser::loadTypes(
    const std::string& file_path) {
    
    std::vector<std::shared_ptr<model::AdvancedType>> types;
    
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + file_path);
        }

        json j;
        file >> j;

        // 保存extras字段（如果有）
        extras_.clear();
        if (j.contains("@extras") && j["@extras"].is_array()) {
            for (const auto& item : j["@extras"]) {
                if (item.is_string()) {
                    extras_.push_back(item.get<std::string>());
                }
            }
        }

        // 遍历所有类型定义
        for (const auto& [type_name, type_def] : j.items()) {
            // 跳过以@开头的特殊字段
            if (!type_name.empty() && type_name[0] == '@') {
                continue;
            }
            
            auto type = std::make_shared<model::AdvancedType>(
                type_name, type_def["base"].get<std::string>());

            // 设置父类型（如果有）
            if (type_def.contains("parent")) {
                type->setParentType(type_def["parent"].get<std::string>());
            }

            // 设置枚举值（如果有）
            if (type_def.contains("enum")) {
                for (const auto& enum_value : type_def["enum"]) {
                    type->addEnumValue(enum_value.get<std::string>());
                }
            }

            // 设置正则表达式（如果有）
            if (type_def.contains("regex")) {
                type->setRegexPattern(type_def["regex"].get<std::string>());
            }

            // 设置数值范围（如果有）
            if (type_def.contains("min")) {
                type->setMinValue(type_def["min"].get<int>());
            }
            if (type_def.contains("max")) {
                type->setMaxValue(type_def["max"].get<int>());
            }

            // 设置额外属性（如果有）
            if (type_def.contains("extra_attrs")) {
                for (const auto& [key, value] : type_def["extra_attrs"].items()) {
                    type->setExtraAttr(key, value.get<std::string>());
                }
            }

            types.push_back(type);
        }
    }
    catch (const json::exception& e) {
        throw std::runtime_error("JSON parse error: " + std::string(e.what()));
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Error loading types: " + std::string(e.what()));
    }

    return types;
}

void JsonParser::saveTypes(
    const std::vector<std::shared_ptr<model::AdvancedType>>& types,
    const std::string& file_path) {
    
    try {
        json j;

        // 转换所有类型为JSON对象
        for (const auto& type : types) {
            json type_def;
            
            type_def["base"] = type->getBaseType();
            
            if (!type->getParentType().empty()) {
                type_def["parent"] = type->getParentType();
            }

            // 添加枚举值（如果有）
            const auto& enum_values = type->getEnumValues();
            if (!enum_values.empty()) {
                type_def["enum"] = enum_values;
            }

            // 添加正则表达式（如果有）
            if (!type->getRegexPattern().empty()) {
                type_def["regex"] = type->getRegexPattern();
            }

            // 添加数值范围（如果有）
            if (type->getMinValue()) {
                type_def["min"] = type->getMinValue().value();
            }
            if (type->getMaxValue()) {
                type_def["max"] = type->getMaxValue().value();
            }

            // 添加额外属性（如果有）
            const auto& extra_attrs = type->getExtraAttrs();
            if (!extra_attrs.empty()) {
                type_def["extra_attrs"] = extra_attrs;
            }

            j[type->getName()] = type_def;
        }

        // 添加extras字段（如果有）
        if (!extras_.empty()) {
            j["@extras"] = extras_;
        }

        // 写入文件
        std::ofstream file(file_path);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file for writing: " + file_path);
        }

        file << j.dump(2);  // 使用2空格缩进
    }
    catch (const json::exception& e) {
        throw std::runtime_error("JSON serialization error: " + std::string(e.what()));
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Error saving types: " + std::string(e.what()));
    }
}

std::vector<std::shared_ptr<model::Device>> JsonParser::loadDevices(
    const std::string& file_path) {
    
    std::vector<std::shared_ptr<model::Device>> devices;
    
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file: " + file_path);
        }

        json j;
        file >> j;

        // 遍历顶层设备
        for (const auto& [device_name, device_def] : j.items()) {
            auto device = std::make_shared<model::Device>(device_name);
            buildDeviceTree(device_def.dump(), device);
            devices.push_back(device);
        }
    }
    catch (const json::exception& e) {
        throw std::runtime_error("JSON parse error: " + std::string(e.what()));
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Error loading devices: " + std::string(e.what()));
    }

    return devices;
}

void JsonParser::saveDevices(
    const std::vector<std::shared_ptr<model::Device>>& devices,
    const std::string& file_path) {
    
    try {
        json j;

        // 递归函数，用于构建设备树的JSON表示
        std::function<json(const std::shared_ptr<model::Device>&)> buildJson;
        buildJson = [&buildJson](const std::shared_ptr<model::Device>& device) -> json {
            json device_json;
            
            // 处理子设备
            for (const auto& child : device->getChildren()) {
                device_json[child->getName()] = buildJson(child);
            }
            
            return device_json;
        };

        // 转换所有根设备
        for (const auto& device : devices) {
            j[device->getName()] = buildJson(device);
        }

        // 写入文件
        std::ofstream file(file_path);
        if (!file.is_open()) {
            throw std::runtime_error("Failed to open file for writing: " + file_path);
        }

        file << j.dump(2);  // 使用2空格缩进
    }
    catch (const json::exception& e) {
        throw std::runtime_error("JSON serialization error: " + std::string(e.what()));
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Error saving devices: " + std::string(e.what()));
    }
}

void JsonParser::buildDeviceTree(
    const std::string& json_str,
    const std::shared_ptr<model::Device>& parent) {
    
    try {
        json j = json::parse(json_str);

        // 遍历所有子设备
        for (const auto& [device_name, device_def] : j.items()) {
            auto child = std::make_shared<model::Device>(device_name);
            parent->addChild(child);
            
            // 递归处理子设备的子设备
            if (!device_def.empty()) {
                buildDeviceTree(device_def.dump(), child);
            }
        }
    }
    catch (const json::exception& e) {
        throw std::runtime_error("JSON parse error in buildDeviceTree: " + std::string(e.what()));
    }
}

} // namespace parser
} // namespace core
} // namespace xsd_editor