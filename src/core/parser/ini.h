#ifndef XSD_EDITOR_CORE_PARSER_INI_H
#define XSD_EDITOR_CORE_PARSER_INI_H

#include <string>
#include <map>
#include <memory>

namespace xsd_editor {
namespace core {
namespace parser {

/**
 * @brief INI文件解析器类
 * 
 * 该类负责解析和生成INI格式的配置文件，
 * 支持分节（section）和键值对的读写。
 */
class IniParser {
public:
    /**
     * @brief 默认构造函数
     */
    IniParser() = default;

    /**
     * @brief 析构函数
     */
    virtual ~IniParser() = default;

    /**
     * @brief 从INI文件加载配置
     * 
     * @param file_path 文件路径
     * @throw ParseError 解析错误时抛出异常
     */
    void loadFromFile(const std::string& file_path);

    /**
     * @brief 保存配置到INI文件
     * 
     * @param file_path 目标文件路径
     * @throw IOException 保存失败时抛出异常
     */
    void saveToFile(const std::string& file_path) const;

    /**
     * @brief 获取字符串值
     * 
     * @param section 节名
     * @param key 键名
     * @param default_value 默认值
     * @return 对应的值，如果不存在则返回默认值
     */
    std::string getString(const std::string& section,
                         const std::string& key,
                         const std::string& default_value = "") const;

    /**
     * @brief 获取布尔值
     * 
     * @param section 节名
     * @param key 键名
     * @param default_value 默认值
     * @return 对应的布尔值，如果不存在则返回默认值
     */
    bool getBool(const std::string& section,
                 const std::string& key,
                 bool default_value = false) const;

    /**
     * @brief 设置字符串值
     * 
     * @param section 节名
     * @param key 键名
     * @param value 要设置的值
     */
    void setString(const std::string& section,
                  const std::string& key,
                  const std::string& value);

    /**
     * @brief 设置布尔值
     * 
     * @param section 节名
     * @param key 键名
     * @param value 要设置的值
     */
    void setBool(const std::string& section,
                const std::string& key,
                bool value);

    /**
     * @brief 检查键是否存在
     * 
     * @param section 节名
     * @param key 键名
     * @return 是否存在
     */
    bool hasKey(const std::string& section, const std::string& key) const;

    /**
     * @brief 删除键
     * 
     * @param section 节名
     * @param key 键名
     * @return 是否成功删除
     */
    bool removeKey(const std::string& section, const std::string& key);

    /**
     * @brief 清除所有配置
     */
    void clear();

private:
    /**
     * @brief 解析一行配置
     * 
     * @param line 配置行
     * @param current_section 当前节名
     */
    void parseLine(const std::string& line, std::string& current_section);

    /**
     * @brief 清理字符串两端的空白字符
     * 
     * @param str 要清理的字符串
     * @return 清理后的字符串
     */
    static std::string trim(const std::string& str);

    // 使用嵌套map存储配置数据：section -> (key -> value)
    std::map<std::string, std::map<std::string, std::string>> data_;
};

} // namespace parser
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_PARSER_INI_H