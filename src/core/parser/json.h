#ifndef XSD_EDITOR_CORE_PARSER_JSON_H
#define XSD_EDITOR_CORE_PARSER_JSON_H

#include <string>
#include <memory>
#include <vector>
#include <nlohmann/json.hpp>
#include "../model/type.h"
#include "../model/device.h"

namespace xsd_editor {
namespace core {
namespace parser {

/**
 * @brief JSON文件解析器类
 *
 * 该类负责解析和生成JSON格式的文件，
 * 主要用于高级类型定义和设备家族定义的存储。
 */
class JsonParser {
public:
    /**
     * @brief 默认构造函数
     */
    JsonParser() = default;

    /**
     * @brief 析构函数
     */
    virtual ~JsonParser() = default;

    /**
     * @brief 从JSON文件加载高级类型定义
     *
     * @param file_path 文件路径
     * @return 解析得到的高级类型列表
     * @throw ParseError 解析错误时抛出异常
     */
    std::vector<std::shared_ptr<model::AdvancedType>> loadTypes(const std::string& file_path);

    /**
     * @brief 保存高级类型定义到JSON文件
     *
     * @param types 要保存的高级类型列表
     * @param file_path 目标文件路径
     * @throw IOException 保存失败时抛出异常
     */
    void saveTypes(const std::vector<std::shared_ptr<model::AdvancedType>>& types,
                  const std::string& file_path);

    /**
     * @brief 从JSON文件加载设备家族定义
     *
     * @param file_path 文件路径
     * @return 解析得到的根设备列表
     * @throw ParseError 解析错误时抛出异常
     */
    std::vector<std::shared_ptr<model::Device>> loadDevices(const std::string& file_path);

    /**
     * @brief 保存设备家族定义到JSON文件
     *
     * @param devices 要保存的根设备列表
     * @param file_path 目标文件路径
     * @throw IOException 保存失败时抛出异常
     */
    void saveDevices(const std::vector<std::shared_ptr<model::Device>>& devices,
                    const std::string& file_path);
                    
    /**
     * @brief 获取extras字段
     *
     * @return extras字段的内容
     */
    std::vector<std::string> getExtras() const;
    
    /**
     * @brief 设置extras字段
     *
     * @param extras extras字段的内容
     */
    void setExtras(const std::vector<std::string>& extras);

private:
    /**
     * @brief 将高级类型对象转换为JSON对象
     *
     * @param type 高级类型对象
     * @return JSON格式的字符串
     */
    std::string typeToJson(const std::shared_ptr<model::AdvancedType>& type);

    /**
     * @brief 从JSON对象创建高级类型对象
     *
     * @param json_str JSON格式的字符串
     * @param type_name 类型名称
     * @return 创建的高级类型对象
     */
    std::shared_ptr<model::AdvancedType> jsonToType(const std::string& json_str,
                                                   const std::string& type_name);

    /**
     * @brief 将设备对象转换为JSON对象
     *
     * @param device 设备对象
     * @return JSON格式的字符串
     */
    std::string deviceToJson(const std::shared_ptr<model::Device>& device);

    /**
     * @brief 从JSON对象创建设备对象
     *
     * @param json_str JSON格式的字符串
     * @param device_name 设备名称
     * @return 创建的设备对象
     */
    std::shared_ptr<model::Device> jsonToDevice(const std::string& json_str,
                                               const std::string& device_name);

    /**
     * @brief 递归构建设备树
     *
     * @param json_str JSON格式的字符串
     * @param parent 父设备对象
     */
    void buildDeviceTree(const std::string& json_str,
                        const std::shared_ptr<model::Device>& parent);
                        
    /**
     * @brief 存储extras字段的内容
     */
    std::vector<std::string> extras_;
};

} // namespace parser
} // namespace core
} // namespace xsd_editor

#endif // XSD_EDITOR_CORE_PARSER_JSON_H