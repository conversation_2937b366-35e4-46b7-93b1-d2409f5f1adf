#include "ini.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <stdexcept>
#include <regex>

namespace xsd_editor {
namespace core {
namespace parser {

void IniParser::loadFromFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: " + file_path);
    }

    clear();  // 清除现有数据
    std::string line;
    std::string current_section;

    while (std::getline(file, line)) {
        parseLine(line, current_section);
    }
}

void IniParser::saveToFile(const std::string& file_path) const {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file for writing: " + file_path);
    }

    for (const auto& section : data_) {
        // 写入节名
        file << "[" << section.first << "]\n";

        // 写入该节的所有键值对
        for (const auto& pair : section.second) {
            file << pair.first << "=" << pair.second << "\n";
        }

        // 在节之间添加空行
        file << "\n";
    }
}

std::string IniParser::getString(
    const std::string& section,
    const std::string& key,
    const std::string& default_value) const {
    
    auto section_it = data_.find(section);
    if (section_it == data_.end()) {
        return default_value;
    }

    auto key_it = section_it->second.find(key);
    if (key_it == section_it->second.end()) {
        return default_value;
    }

    return key_it->second;
}

bool IniParser::getBool(
    const std::string& section,
    const std::string& key,
    bool default_value) const {
    
    std::string value = getString(section, key);
    if (value.empty()) {
        return default_value;
    }

    // 转换为小写进行比较
    std::transform(value.begin(), value.end(), value.begin(), ::tolower);
    
    // 支持多种布尔值表示
    if (value == "true" || value == "yes" || value == "1" || value == "on") {
        return true;
    }
    if (value == "false" || value == "no" || value == "0" || value == "off") {
        return false;
    }

    return default_value;
}

void IniParser::setString(
    const std::string& section,
    const std::string& key,
    const std::string& value) {
    
    data_[section][key] = value;
}

void IniParser::setBool(
    const std::string& section,
    const std::string& key,
    bool value) {
    
    setString(section, key, value ? "true" : "false");
}

bool IniParser::hasKey(const std::string& section, const std::string& key) const {
    auto section_it = data_.find(section);
    if (section_it == data_.end()) {
        return false;
    }

    return section_it->second.find(key) != section_it->second.end();
}

bool IniParser::removeKey(const std::string& section, const std::string& key) {
    auto section_it = data_.find(section);
    if (section_it == data_.end()) {
        return false;
    }

    return section_it->second.erase(key) > 0;
}

void IniParser::clear() {
    data_.clear();
}

void IniParser::parseLine(const std::string& line, std::string& current_section) {
    // 去除首尾空白
    std::string trimmed = trim(line);
    
    // 跳过空行和注释
    if (trimmed.empty() || trimmed[0] == ';' || trimmed[0] == '#') {
        return;
    }

    // 解析节名
    if (trimmed[0] == '[' && trimmed.back() == ']') {
        current_section = trimmed.substr(1, trimmed.length() - 2);
        current_section = trim(current_section);
        return;
    }

    // 如果没有当前节，使用默认节名
    if (current_section.empty()) {
        current_section = "default";
    }

    // 解析键值对
    size_t pos = trimmed.find('=');
    if (pos != std::string::npos) {
        std::string key = trim(trimmed.substr(0, pos));
        std::string value = trim(trimmed.substr(pos + 1));

        // 移除值两端的引号（如果有）
        if (value.length() >= 2 && value.front() == '"' && value.back() == '"') {
            value = value.substr(1, value.length() - 2);
        }

        if (!key.empty()) {
            data_[current_section][key] = value;
        }
    }
}

std::string IniParser::trim(const std::string& str) {
    const std::string whitespace = " \t\r\n";
    size_t start = str.find_first_not_of(whitespace);
    if (start == std::string::npos) {
        return "";
    }
    size_t end = str.find_last_not_of(whitespace);
    return str.substr(start, end - start + 1);
}

} // namespace parser
} // namespace core
} // namespace xsd_editor