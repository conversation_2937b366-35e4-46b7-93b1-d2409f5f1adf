#ifndef XSD_EDITOR_PERSISTENCE_FILE_MANAGER_H
#define XSD_EDITOR_PERSISTENCE_FILE_MANAGER_H

#include <string>
#include <vector>
#include <memory>
#include "../core/model/item.h"
#include "../core/model/type.h"
#include "../core/model/device.h"

namespace xsd_editor {
namespace persistence {

/**
 * @brief 文件管理器类
 * 
 * 该类负责管理所有文件的读写操作，
 * 包括UCI类型文件、类型定义、设备家族等。
 */
class FileManager {
public:
    /**
     * @brief 默认构造函数
     */
    FileManager() = default;

    /**
     * @brief 构造函数
     * 
     * @param root_dir UCI类型文件的根目录
     * @param work_dir 工作目录（存放types.json等文件）
     */
    FileManager(const std::string& root_dir, const std::string& work_dir);

    /**
     * @brief 析构函数
     */
    virtual ~FileManager() = default;

    /**
     * @brief 设置根目录
     * @param root_dir UCI类型文件的根目录
     */
    void setRootDir(const std::string& root_dir);

    /**
     * @brief 设置工作目录
     * @param work_dir 工作目录
     */
    void setWorkDir(const std::string& work_dir);

    /**
     * @brief 加载所有UCI类型文件
     * @return 加载的模块列表
     */
    std::vector<std::shared_ptr<core::model::Item>> loadAllModules();

    /**
     * @brief 保存模块
     * @param module 要保存的模块
     */
    void saveModule(const std::shared_ptr<core::model::Item>& module);

    /**
     * @brief 加载高级类型定义
     * @return 加载的类型列表
     */
    std::vector<std::shared_ptr<core::model::AdvancedType>> loadTypes();
    
    /**
     * @brief 加载extras字段
     * @return extras字段的内容
     */
    std::vector<std::string> loadExtras();

    /**
     * @brief 保存高级类型定义
     * @param types 要保存的类型列表
     */
    void saveTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types);
    
    /**
     * @brief 保存高级类型定义和extras字段
     * @param types 要保存的类型列表
     * @param extras extras字段的内容
     */
    void saveTypes(const std::vector<std::shared_ptr<core::model::AdvancedType>>& types,
                  const std::vector<std::string>& extras);

    /**
     * @brief 加载设备家族定义
     * @return 加载的设备列表
     */
    std::vector<std::shared_ptr<core::model::Device>> loadDevices();

    /**
     * @brief 保存设备家族定义
     * @param devices 要保存的设备列表
     */
    void saveDevices(const std::vector<std::shared_ptr<core::model::Device>>& devices);

    /**
     * @brief 检查文件是否存在
     * @param path 文件路径
     * @return 是否存在
     */
    bool fileExists(const std::string& path) const;

    /**
     * @brief 创建目录
     * @param path 目录路径
     * @return 是否成功
     */
    bool createDirectory(const std::string& path) const;

    /**
     * @brief 获取目录下所有匹配的文件
     * @param dir 目录路径
     * @param pattern 文件模式（例如 *.ucitype）
     * @return 文件路径列表
     */
    std::vector<std::string> findFiles(const std::string& dir,
                                     const std::string& pattern) const;
private:
    std::string root_dir_;   // UCI类型文件的根目录
    std::string work_dir_;   // 工作目录
};

} // namespace persistence
} // namespace xsd_editor

#endif // XSD_EDITOR_PERSISTENCE_FILE_MANAGER_H