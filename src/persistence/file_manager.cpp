#include "file_manager.h"
#include "../core/parser/ucitype.h"
#include "../core/parser/json.h"
#include <algorithm>
#include <filesystem>
#include <stdexcept>
#include <fstream>
#include <iostream>
#include <regex>

namespace fs = std::filesystem;

namespace xsd_editor {
namespace persistence {

FileManager::FileManager(const std::string& root_dir, const std::string& work_dir)
    : root_dir_(root_dir)
    , work_dir_(work_dir)
{
    // 验证目录是否存在
    if (!fs::exists(root_dir_)) {
        throw std::runtime_error("Root directory does not exist: " + root_dir_);
    }
    if (!fs::exists(work_dir_)) {
        throw std::runtime_error("Work directory does not exist: " + work_dir_);
    }
}

void FileManager::setRootDir(const std::string& root_dir) {
    if (!fs::exists(root_dir)) {
        throw std::runtime_error("Root directory does not exist: " + root_dir);
    }
    root_dir_ = root_dir;
}

void FileManager::setWorkDir(const std::string& work_dir) {
    if (!fs::exists(work_dir)) {
        throw std::runtime_error("Work directory does not exist: " + work_dir);
    }
    work_dir_ = work_dir;
}

std::vector<std::shared_ptr<core::model::Item>> FileManager::loadAllModules() {
    std::vector<std::shared_ptr<core::model::Item>> modules;
    core::parser::UciTypeParser parser;

    // 递归查找所有.ucitype文件
    auto ucitype_files = findFiles(root_dir_, "*.ucitype");
    std::sort(ucitype_files.begin(),ucitype_files.end());
    
    for (const auto& file : ucitype_files) {
        try {
            auto module = parser.parseFile(file);
            if (module) {
                // 设置文件路径
                module->setFileLoc(fs::path(file).parent_path());
                modules.push_back(module);
            }
        }
        catch (const std::exception& e) {
            // 记录错误但继续处理其他文件
            // TODO: 使用日志系统记录错误
            std::cerr << "Error loading module from " << file << ": " << e.what() << std::endl;
        }
    }

    return modules;
}

void FileManager::saveModule(const std::shared_ptr<core::model::Item>& module) {
    if (!module) {
        throw std::invalid_argument("Null module pointer");
    }

    std::string file_loc = module->getFileLoc();
    if (file_loc.empty()) {

        throw std::runtime_error("Failed to get location: " + file_loc);
    }

    // 确保目标目录存在
    fs::path dir_path = fs::path(file_loc);
    if (!fs::exists(dir_path)) {
        if (!createDirectory(dir_path.string())) {
            throw std::runtime_error("Failed to create directory: " + dir_path.string());
        }
    }

    core::parser::UciTypeParser parser;
    std::string content = parser.serialize(module);

    std::ofstream file(dir_path.append(module->getType()+".ucitype"));
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file for writing: " + dir_path.string());
    }

    file << content;
}

std::vector<std::shared_ptr<core::model::AdvancedType>> FileManager::loadTypes() {
    std::string types_file = fs::path(work_dir_) / "types.json";
    
    if (!fileExists(types_file)) {
        // 如果文件不存在，返回空列表
        return {};
    }

    core::parser::JsonParser parser;
    return parser.loadTypes(types_file);
}

std::vector<std::string> FileManager::loadExtras() {
    std::string types_file = fs::path(work_dir_) / "types.json";
    
    if (!fileExists(types_file)) {
        // 如果文件不存在，返回空列表
        return {};
    }

    core::parser::JsonParser parser;
    parser.loadTypes(types_file);  // 加载类型的同时会加载extras
    return parser.getExtras();
}

void FileManager::saveTypes(
    const std::vector<std::shared_ptr<core::model::AdvancedType>>& types) {
    
    std::string types_file = fs::path(work_dir_) / "types.json";
    core::parser::JsonParser parser;
    parser.saveTypes(types, types_file);
}

void FileManager::saveTypes(
    const std::vector<std::shared_ptr<core::model::AdvancedType>>& types,
    const std::vector<std::string>& extras) {
    
    std::string types_file = fs::path(work_dir_) / "types.json";
    core::parser::JsonParser parser;
    parser.setExtras(extras);
    parser.saveTypes(types, types_file);
}

std::vector<std::shared_ptr<core::model::Device>> FileManager::loadDevices() {
    std::string devices_file = fs::path(work_dir_) / "device_family.json";
    
    if (!fileExists(devices_file)) {
        // 如果文件不存在，返回空列表
        return {};
    }

    core::parser::JsonParser parser;
    return parser.loadDevices(devices_file);
}

void FileManager::saveDevices(
    const std::vector<std::shared_ptr<core::model::Device>>& devices) {
    
    std::string devices_file = fs::path(work_dir_) / "device_family.json";
    core::parser::JsonParser parser;
    parser.saveDevices(devices, devices_file);
}

bool FileManager::fileExists(const std::string& path) const {
    return fs::exists(path);
}

bool FileManager::createDirectory(const std::string& path) const {
    try {
        return fs::create_directories(path);
    }
    catch (const fs::filesystem_error&) {
        return false;
    }
}

std::vector<std::string> FileManager::findFiles(
    const std::string& dir,
    const std::string& pattern) const {
    
    std::vector<std::string> files;
    
    try {
        // 将通配符模式转换为正则表达式
        std::string regex_pattern = pattern;
        // 替换 . 为 \.
        regex_pattern = std::regex_replace(regex_pattern, std::regex("\\."), "\\.");
        // 替换 * 为 .*
        regex_pattern = std::regex_replace(regex_pattern, std::regex("\\*"), ".*");
        std::regex file_regex(regex_pattern);

        // 递归遍历目录
        for (const auto& entry : fs::recursive_directory_iterator(dir)) {
            if (fs::is_regular_file(entry)) {
                std::string filename = entry.path().filename().string();
                if (std::regex_match(filename, file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    }
    catch (const fs::filesystem_error& e) {
        // 记录错误但继续执行
        // TODO: 使用日志系统记录错误
        std::cerr << "Error searching files: " << e.what() << std::endl;
    }

    return files;
}

} // namespace persistence
} // namespace xsd_editor