#include "config_manager.h"
#include <filesystem>
#include <algorithm>
#include <stdexcept>
#include <iostream>
#include <fstream>

namespace fs = std::filesystem;

namespace xsd_editor {
namespace persistence {

void ConfigManager::loadConfig(const std::string& config_file) {
    config_file_ = fs::absolute(config_file).lexically_normal().string();
    parser_ = std::make_unique<core::parser::IniParser>();

    // 存储配置文件的目录路径
    config_dir_ = fs::path(config_file_).parent_path().string();

    try {
        parser_->loadFromFile(config_file);

        // 加载路径配置
        // 处理相对路径 - 以配置文件所在目录为基准
        std::string root_dir = parser_->getString("paths", "root_dir");
        std::string work_dir = parser_->getString("paths", "work_dir");
        std::cout<<"Using config_dir="<< config_dir_ <<std::endl;
        if (!fs::path(root_dir).is_absolute()) {
            root_dir_ = (fs::path(config_dir_) / root_dir).lexically_normal().string();
        } else {
            root_dir_ = root_dir;
        }

        if (!fs::path(work_dir).is_absolute()) {
            work_dir_ = (fs::path(config_dir_) / work_dir).lexically_normal().string();
        } else {
            work_dir_ = work_dir;
        }

        // 验证目录
        validateDirectory(root_dir_);
        validateDirectory(work_dir_);

        // 加载应用配置
        single_instance_ = parser_->getBool("app", "single_instance", true);
        auto_save_ = parser_->getBool("app", "auto_save", false);
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Failed to load config: " + std::string(e.what()));
    }
}

void ConfigManager::saveConfig() {
    if (!parser_) {
        throw std::runtime_error("Config not loaded");
    }

    try {
        // 保存路径配置
        parser_->setString("paths", "root_dir", root_dir_);
        parser_->setString("paths", "work_dir", work_dir_);

        // 保存应用配置
        parser_->setBool("app", "single_instance", single_instance_);
        parser_->setBool("app", "auto_save", auto_save_);

        // 写入文件
        parser_->saveToFile(config_file_);
    }
    catch (const std::exception& e) {
        throw std::runtime_error("Failed to save config: " + std::string(e.what()));
    }
}

void ConfigManager::setRootDir(const std::string& dir) {
    validateDirectory(dir);
    if (root_dir_ != dir) {
        root_dir_ = dir;
        notifyChange(ConfigChangeType::ROOT_DIR_CHANGED);
    }
}

void ConfigManager::setWorkDir(const std::string& dir) {
    validateDirectory(dir);
    if (work_dir_ != dir) {
        work_dir_ = dir;
        notifyChange(ConfigChangeType::WORK_DIR_CHANGED);
    }
}

void ConfigManager::setSingleInstance(bool enabled) {
    if (single_instance_ != enabled) {
        single_instance_ = enabled;
        notifyChange(ConfigChangeType::SINGLE_INSTANCE_CHANGED);
    }
}

void ConfigManager::setAutoSave(bool enabled) {
    if (auto_save_ != enabled) {
        auto_save_ = enabled;
        notifyChange(ConfigChangeType::AUTO_SAVE_CHANGED);
    }
}

size_t ConfigManager::addChangeListener(ConfigChangeListener listener) {
    if (!listener) {
        throw std::invalid_argument("Null listener");
    }

    size_t id = next_listener_id_++;
    listeners_.emplace_back(id, std::move(listener));
    return id;
}

void ConfigManager::removeChangeListener(size_t id) {
    auto it = std::find_if(listeners_.begin(), listeners_.end(),
        [id](const auto& pair) { return pair.first == id; });
    
    if (it != listeners_.end()) {
        listeners_.erase(it);
    }
}

void ConfigManager::notifyChange(ConfigChangeType type) {
    // 复制监听器列表，以防在通知过程中有监听器被移除
    auto listeners = listeners_;
    for (const auto& [id, listener] : listeners) {
        try {
            listener(type);
        }
        catch (const std::exception& e) {
            // 记录错误但继续通知其他监听器
            // TODO: 使用日志系统记录错误
            std::cerr << "Error in config change listener: " << e.what() << std::endl;
        }
    }

    // 如果启用了自动保存，则保存配置
    if (auto_save_) {
        try {
            saveConfig();
        }
        catch (const std::exception& e) {
            // TODO: 使用日志系统记录错误
            std::cerr << "Error auto-saving config: " << e.what() << std::endl;
        }
    }
}

void ConfigManager::validateDirectory(const std::string& dir) {
    if (dir.empty()) {
        throw std::runtime_error("Empty directory path");
    }

    if (!fs::exists(dir)) {
        throw std::runtime_error(fs::current_path().string() + std::string("Directory does not exist: ") + dir);
    }

    if (!fs::is_directory(dir)) {
        throw std::runtime_error("Path is not a directory: " + dir);
    }

    // 检查目录权限
    try {
        // 尝试创建临时文件来测试写入权限
        auto temp_path = fs::path(dir) / ".write_test";
        std::ofstream test_file(temp_path);
        if (test_file.is_open()) {
            test_file.close();
            fs::remove(temp_path);
        } else {
            throw std::runtime_error("Directory is not writable: " + dir);
        }
    }
    catch (const fs::filesystem_error& e) {
        throw std::runtime_error("Failed to verify directory permissions: " + 
                               std::string(e.what()));
    }
}

} // namespace persistence
} // namespace xsd_editor