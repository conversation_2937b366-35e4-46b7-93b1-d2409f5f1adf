#ifndef XSD_EDITOR_PERSISTENCE_CONFIG_MANAGER_H
#define XSD_EDITOR_PERSISTENCE_CONFIG_MANAGER_H

#include <string>
#include <memory>
#include <functional>
#include <vector>
#include "../core/parser/ini.h"
#include "../utils/singleton.h"

namespace xsd_editor {
namespace persistence {

/**
 * @brief 配置变更事件类型
 */
enum class ConfigChangeType {
    ROOT_DIR_CHANGED,    // 根目录变更
    WORK_DIR_CHANGED,    // 工作目录变更
    SINGLE_INSTANCE_CHANGED,  // 单实例设置变更
    AUTO_SAVE_CHANGED    // 自动保存设置变更
};

/**
 * @brief 配置变更监听器类型
 */
using ConfigChangeListener = std::function<void(ConfigChangeType)>;

/**
 * @brief 配置管理器类
 * 
 * 该类负责管理程序的配置信息，
 * 包括路径配置、运行模式等。
 * 使用单例模式确保全局唯一实例。
 */
class ConfigManager : public utils::Singleton<ConfigManager> {
    friend class utils::Singleton<ConfigManager>;

public:
    /**
     * @brief 加载配置文件
     * 
     * @param config_file 配置文件路径
     * @throw std::runtime_error 加载失败时抛出异常
     */
    void loadConfig(const std::string& config_file);

    /**
     * @brief 保存配置到文件
     * 
     * @throw std::runtime_error 保存失败时抛出异常
     */
    void saveConfig();

    // Getters
    const std::string& getRootDir() const { return root_dir_; }
    const std::string& getWorkDir() const { return work_dir_; }
    bool isSingleInstance() const { return single_instance_; }
    bool isAutoSave() const { return auto_save_; }
    const std::string& getConfigFile() const { return config_file_; }

    /**
     * @brief 设置根目录
     * 
     * @param dir 根目录路径
     * @throw std::runtime_error 目录不存在时抛出异常
     */
    void setRootDir(const std::string& dir);

    /**
     * @brief 设置工作目录
     * 
     * @param dir 工作目录路径
     * @throw std::runtime_error 目录不存在时抛出异常
     */
    void setWorkDir(const std::string& dir);

    /**
     * @brief 设置单实例模式
     * @param enabled 是否启用
     */
    void setSingleInstance(bool enabled);

    /**
     * @brief 设置自动保存
     * @param enabled 是否启用
     */
    void setAutoSave(bool enabled);

    /**
     * @brief 添加配置变更监听器
     * 
     * @param listener 监听器函数
     * @return 监听器ID，用于移除监听器
     */
    size_t addChangeListener(ConfigChangeListener listener);

    /**
     * @brief 移除配置变更监听器
     * @param id 监听器ID
     */
    void removeChangeListener(size_t id);

protected:
    /**
     * @brief 默认构造函数
     */
    ConfigManager() = default;

private:
    /**
     * @brief 通知配置变更
     * @param type 变更类型
     */
    void notifyChange(ConfigChangeType type);

    /**
     * @brief 验证目录是否存在
     * 
     * @param dir 目录路径
     * @throw std::runtime_error 目录不存在时抛出异常
     */
    void validateDirectory(const std::string& dir);

    std::string config_file_;          // 配置文件路径
    std::string config_dir_;           // 配置文件所在目录
    std::string root_dir_;             // UCI类型文件根目录
    std::string work_dir_;             // 工作目录
    bool single_instance_ = true;      // 是否单实例模式
    bool auto_save_ = false;           // 是否自动保存

    std::unique_ptr<core::parser::IniParser> parser_;  // INI解析器
    
    // 配置变更监听器管理
    size_t next_listener_id_ = 0;
    std::vector<std::pair<size_t, ConfigChangeListener>> listeners_;
};

} // namespace persistence
} // namespace xsd_editor

#endif // XSD_EDITOR_PERSISTENCE_CONFIG_MANAGER_H