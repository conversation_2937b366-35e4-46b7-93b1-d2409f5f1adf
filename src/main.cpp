#include <iostream>
#include <cstring>
#include <filesystem>
#include "persistence/config_manager.h"
#include "persistence/file_manager.h"

// 根据编译选项选择UI头文件
#ifdef USE_GTK_UI
#include <gtkmm.h>
#include "ui/gtk/main_window.h"
#endif

#ifdef USE_WIN32_UI
#include <windows.h>
#include "ui/win32/win32_main_window.h"
#endif

namespace fs = std::filesystem;

int main(int argc, char* argv[]) {
    try {
        // 解析命令行参数
        std::string config_path;
        for (int i = 1; i < argc; ++i) {
            if (strcmp(argv[i], "--config") == 0 && i + 1 < argc) {
                config_path = argv[i+1];
                i++; // 跳过参数值
            }
        }

        // 获取程序路径
        fs::path exe_path;
        
        #ifdef USE_WIN32_UI
        wchar_t path[MAX_PATH];
        GetModuleFileNameW(NULL, path, MAX_PATH);
        exe_path = fs::path(path);
        #else
        exe_path = fs::canonical("/proc/self/exe");
        #endif
        
        fs::path exe_dir = exe_path.parent_path();
        fs::path config_file = exe_dir / "config.ini";

        fs::path default_config = "/usr/etc/xsd-editor/default.ini";
        
        // 加载配置
        auto& config_manager = xsd_editor::persistence::ConfigManager::getInstance();
        if (!config_path.empty()) {
            try {
                config_manager.loadConfig(config_path);
            } catch (const std::exception& e) {
                std::cerr << "Failed to load specified config: " << e.what() << std::endl;
                std::cerr << "Falling back to executable directory config..." << std::endl;
                config_path.clear(); // 强制回退
            }
        }

        if (config_path.empty()) {
            // 尝试加载运行目录下的配置
            try {
                config_manager.loadConfig(config_file.string());
            } catch (const std::exception& e) {
                std::cerr << "Failed to load executable directory config: " << e.what() << std::endl;
                
                // 尝试加载系统默认配置
                if (fs::exists(default_config)) {
                    try {
                        std::cerr << "Falling back to system default config..." << std::endl;
                        config_manager.loadConfig(default_config.string());
                    } catch (const std::exception& e) {
                        std::cerr << "Failed to load default config: " << e.what() << std::endl;
                        throw std::runtime_error("No valid configuration could be loaded");
                    }
                } else {
                    throw std::runtime_error("No valid configuration could be loaded");
                }
            }
        }

        // 检查单实例模式
        if (config_manager.isSingleInstance()) {
            // TODO: 实现单实例检查
        }

        // 创建文件管理器
        xsd_editor::persistence::FileManager file_manager(
            config_manager.getRootDir(),
            config_manager.getWorkDir()
        );

        #ifdef USE_GTK_UI
        // 初始化GTK应用程序
        auto app = Gtk::Application::create("org.xsd.editor");
        
        // 创建主窗口（但不显示）
        std::unique_ptr<xsd_editor::ui::gtk::MainWindow> window;

        // 连接activate信号
        app->signal_activate().connect([&]() {
            // 在activate信号处理程序中创建和显示窗口
            // 此时应用程序已经完全启动
            if (!window) {
                // 创建主窗口
                window = std::make_unique<xsd_editor::ui::gtk::MainWindow>(app);
                
                // 加载数据
                try {
                    // 加载高级类型
                    auto types = file_manager.loadTypes();
                    window->updateTypes(types);
                    
                    // 加载extras字段
                    auto extras = file_manager.loadExtras();
                    window->updateExtras(extras);

                    // 加载设备家族
                    auto devices = file_manager.loadDevices();
                    window->updateDevices(devices);

                    // 加载UCI类型文件
                    auto modules = file_manager.loadAllModules();
                    window->updateItems(modules);
                }
                catch (const std::exception& e) {
                    window->showError("Failed to load data: " + std::string(e.what()));
                }

                // 设置事件处理
                window->addEventListener([&](xsd_editor::ui::UIEventType type, const std::string& data) {
                    (void) data;
                    try {
                        switch (type) {
                            case xsd_editor::ui::UIEventType::SAVE_REQUESTED:
                                // 保存所有数据
                                {
                                    for (auto item : window->getElements()) {
                                            file_manager.saveModule(item);
                                    }
                                    auto types = window->getTypes();
                                    auto extras = window->getTypeExtras();
                                    file_manager.saveTypes(types, extras);
                                    file_manager.saveDevices(
                                        window->getDevices());
                                    window->showInfo(
                                        "All changes saved successfully.");
                                    break;
                                }
                            case xsd_editor::ui::UIEventType::LOAD_REQUESTED:
                                // 重新加载数据
                                window->updateTypes(file_manager.loadTypes());
                                window->updateDevices(file_manager.loadDevices());
                                window->updateItems(file_manager.loadAllModules());
                                window->showInfo("Data reloaded successfully.");
                                break;

                            case xsd_editor::ui::UIEventType::ITEM_CHANGED:
                            case xsd_editor::ui::UIEventType::TYPE_CHANGED:
                            case xsd_editor::ui::UIEventType::DEVICE_CHANGED:
                                // 如果启用了自动保存，则保存更改
                                if (config_manager.isAutoSave()) {
                                    if (type == xsd_editor::ui::UIEventType::ITEM_CHANGED) {
                                        if (auto item = window->getSelectedItem()) {
                                            file_manager.saveModule(item);
                                        }
                                    }
                                    else if (type == xsd_editor::ui::UIEventType::TYPE_CHANGED) {
                                        auto types = window->getTypes();
                                        auto extras = window->getTypeExtras();
                                        file_manager.saveTypes(types, extras);
                                    }
                                    else if (type == xsd_editor::ui::UIEventType::DEVICE_CHANGED) {
                                        file_manager.saveDevices(window->getDevices());
                                    }
                                }
                                break;

                            default:
                                break;
                        }
                    }
                    catch (const std::exception& e) {
                        window->showError(e.what());
                    }
                });
                
                // 显示窗口
                window->show();
            }
        });

        // 过滤掉--config参数后再运行GTK应用
        std::vector<char*> filtered_argv;
        for (int i = 0; i < argc; ++i) {
            if (strcmp(argv[i], "--config") != 0) {
                filtered_argv.push_back(argv[i]);
            } else {
                i++; // 跳过参数值
            }
        }
        
        // 运行应用程序
        int filtered_argc = filtered_argv.size();
        return app->run(filtered_argc, filtered_argv.data());
        
        #endif // USE_GTK_UI
        
        #ifdef USE_WIN32_UI
        // 初始化Win32应用程序
        HINSTANCE hInstance = GetModuleHandle(NULL);
        
        // 创建主窗口
        auto window = std::make_unique<xsd_editor::ui::win32::Win32MainWindow>();
        
        // 加载数据
        try {
            // 加载高级类型
            auto types = file_manager.loadTypes();
            window->updateTypes(types);
            
            // 加载extras字段
            auto extras = file_manager.loadExtras();
            window->updateExtras(extras);

            // 加载设备家族
            auto devices = file_manager.loadDevices();
            window->updateDevices(devices);

            // 加载UCI类型文件
            auto modules = file_manager.loadAllModules();
            window->updateItems(modules);
        }
        catch (const std::exception& e) {
            window->showError("Failed to load data: " + std::string(e.what()));
        }

        // 设置事件处理
        window->addEventListener([&](xsd_editor::ui::UIEventType type, const std::string& data) {
            (void) data;
            try {
                switch (type) {
                    case xsd_editor::ui::UIEventType::SAVE_REQUESTED:
                        // 保存所有数据
                        {
                            for (auto item : window->getElements()) {
                                    file_manager.saveModule(item);
                            }
                            auto types = window->getTypes();
                            auto extras = window->getTypeExtras();
                            file_manager.saveTypes(types, extras);
                            file_manager.saveDevices(
                                window->getDevices());
                            window->showInfo(
                                "All changes saved successfully.");
                            break;
                        }
                    case xsd_editor::ui::UIEventType::LOAD_REQUESTED:
                        // 重新加载数据
                        window->updateTypes(file_manager.loadTypes());
                        window->updateDevices(file_manager.loadDevices());
                        window->updateItems(file_manager.loadAllModules());
                        window->showInfo("Data reloaded successfully.");
                        break;

                    case xsd_editor::ui::UIEventType::ITEM_CHANGED:
                    case xsd_editor::ui::UIEventType::TYPE_CHANGED:
                    case xsd_editor::ui::UIEventType::DEVICE_CHANGED:
                        // 如果启用了自动保存，则保存更改
                        if (config_manager.isAutoSave()) {
                            if (type == xsd_editor::ui::UIEventType::ITEM_CHANGED) {
                                if (auto item = window->getSelectedItem()) {
                                    file_manager.saveModule(item);
                                }
                            }
                            else if (type == xsd_editor::ui::UIEventType::TYPE_CHANGED) {
                                auto types = window->getTypes();
                                auto extras = window->getTypeExtras();
                                file_manager.saveTypes(types, extras);
                            }
                            else if (type == xsd_editor::ui::UIEventType::DEVICE_CHANGED) {
                                file_manager.saveDevices(window->getDevices());
                            }
                        }
                        break;

                    default:
                        break;
                }
            }
            catch (const std::exception& e) {
                window->showError(e.what());
            }
        });
        
        // 显示窗口
        window->show();
        
        // 消息循环
        MSG msg;
        while (GetMessage(&msg, NULL, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        return (int)msg.wParam;
        #endif // USE_WIN32_UI
    }
    catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}